-- =============================================
-- Base de données pour Système de Gestion de Supermarché Algérien
-- Créé pour: GestionDeStock2024
-- Date: 2025-06-28
-- Spécificités: Algérie (NIF, NIS, RC, ART, Timbre fiscal)
-- =============================================

CREATE DATABASE IF NOT EXISTS `gestion_supermarche_dz` 
CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE `gestion_supermarche_dz`;

-- =============================================
-- 1. TABLE DES ENTREPRISES/SOCIÉTÉS
-- =============================================
CREATE TABLE `entreprises` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `nom_entreprise` VARCHAR(255) NOT NULL,
    `raison_sociale` VARCHAR(255),
    `adresse` TEXT,
    `ville` VARCHAR(100),
    `wilaya` VARCHAR(100),
    `code_postal` VARCHAR(10),
    `telephone` VARCHAR(20),
    `fax` VARCHAR(20),
    `email` VARCHAR(100),
    `site_web` VARCHAR(255),
    `nif` VARCHAR(20) UNIQUE, -- Numéro d'Identification Fiscale
    `nis` VARCHAR(20) UNIQUE, -- Numéro d'Identification Statistique
    `rc` VARCHAR(20), -- Registre de Commerce
    `art` VARCHAR(20), -- Article d'Imposition
    `logo` LONGBLOB,
    `capital_social` DECIMAL(15,2) DEFAULT 0,
    `forme_juridique` VARCHAR(100),
    `activite_principale` VARCHAR(255),
    `date_creation` DATE,
    `statut` ENUM('Actif', 'Inactif') DEFAULT 'Actif',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- =============================================
-- 2. TABLE DES UTILISATEURS
-- =============================================
CREATE TABLE `utilisateurs` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `nom_utilisateur` VARCHAR(50) UNIQUE NOT NULL,
    `mot_de_passe` VARCHAR(255) NOT NULL,
    `nom_complet` VARCHAR(255) NOT NULL,
    `email` VARCHAR(100),
    `telephone` VARCHAR(20),
    `role` ENUM('Admin', 'Manager', 'Vendeur', 'Comptable', 'Magasinier') NOT NULL,
    `permissions` JSON, -- Permissions détaillées
    `derniere_connexion` TIMESTAMP NULL,
    `statut` ENUM('Actif', 'Inactif', 'Suspendu') DEFAULT 'Actif',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- =============================================
-- 3. TABLE DES SESSIONS UTILISATEURS
-- =============================================
CREATE TABLE `sessions_utilisateurs` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `utilisateur_id` INT NOT NULL,
    `heure_connexion` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `heure_deconnexion` TIMESTAMP NULL,
    `adresse_ip` VARCHAR(45),
    `navigateur` VARCHAR(255),
    `statut_session` ENUM('Active', 'Fermée', 'Expirée') DEFAULT 'Active',
    FOREIGN KEY (`utilisateur_id`) REFERENCES `utilisateurs`(`id`) ON DELETE CASCADE
);

-- =============================================
-- 4. TABLE DES CATÉGORIES D'ARTICLES
-- =============================================
CREATE TABLE `categories` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `nom_categorie` VARCHAR(255) NOT NULL,
    `description` TEXT,
    `categorie_parent_id` INT NULL,
    `code_categorie` VARCHAR(20) UNIQUE,
    `tva_applicable` DECIMAL(5,2) DEFAULT 19.00, -- TVA par défaut en Algérie
    `statut` ENUM('Actif', 'Inactif') DEFAULT 'Actif',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (`categorie_parent_id`) REFERENCES `categories`(`id`) ON DELETE SET NULL
);

-- =============================================
-- 5. TABLE DES UNITÉS DE MESURE
-- =============================================
CREATE TABLE `unites_mesure` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `nom_unite` VARCHAR(50) NOT NULL,
    `abreviation` VARCHAR(10) NOT NULL,
    `type_unite` ENUM('Poids', 'Volume', 'Longueur', 'Quantité') NOT NULL,
    `facteur_conversion` DECIMAL(10,4) DEFAULT 1.0000,
    `unite_base_id` INT NULL,
    `statut` ENUM('Actif', 'Inactif') DEFAULT 'Actif',
    FOREIGN KEY (`unite_base_id`) REFERENCES `unites_mesure`(`id`) ON DELETE SET NULL
);

-- =============================================
-- 6. TABLE DES ARTICLES/PRODUITS
-- =============================================
CREATE TABLE `articles` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `code_article` VARCHAR(50) UNIQUE NOT NULL,
    `nom_article` VARCHAR(255) NOT NULL,
    `description` TEXT,
    `categorie_id` INT NOT NULL,
    `unite_mesure_id` INT NOT NULL,
    `prix_achat_unitaire` DECIMAL(12,2) NOT NULL DEFAULT 0,
    `prix_vente_unitaire` DECIMAL(12,2) NOT NULL DEFAULT 0,
    `prix_vente_gros` DECIMAL(12,2) DEFAULT 0,
    `marge_beneficiaire` DECIMAL(5,2) DEFAULT 0,
    `tva_achat` DECIMAL(5,2) DEFAULT 19.00,
    `tva_vente` DECIMAL(5,2) DEFAULT 19.00,
    `stock_minimum` DECIMAL(10,2) DEFAULT 0,
    `stock_maximum` DECIMAL(10,2) DEFAULT 0,
    `stock_actuel` DECIMAL(10,2) DEFAULT 0,
    `stock_reserve` DECIMAL(10,2) DEFAULT 0,
    `pmp` DECIMAL(12,2) DEFAULT 0, -- Prix Moyen Pondéré
    `pieces_par_fardeau` INT DEFAULT 1,
    `prix_fardeau` DECIMAL(12,2) DEFAULT 0,
    `date_expiration` DATE NULL,
    `lot_numero` VARCHAR(100),
    `emplacement` VARCHAR(100),
    `image` LONGBLOB,
    `statut` ENUM('Actif', 'Inactif', 'Discontinué') DEFAULT 'Actif',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (`categorie_id`) REFERENCES `categories`(`id`) ON DELETE RESTRICT,
    FOREIGN KEY (`unite_mesure_id`) REFERENCES `unites_mesure`(`id`) ON DELETE RESTRICT
);

-- =============================================
-- 7. TABLE DES CODES-BARRES (MULTIPLE PAR ARTICLE)
-- =============================================
CREATE TABLE `codes_barres` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `article_id` INT NOT NULL,
    `code_barre` VARCHAR(100) UNIQUE NOT NULL,
    `type_code` ENUM('EAN13', 'EAN8', 'UPC', 'Code128', 'Autre') DEFAULT 'EAN13',
    `quantite_unitaire` DECIMAL(10,2) DEFAULT 1, -- Pour les codes-barres de lots
    `prix_unitaire` DECIMAL(12,2) NULL, -- Prix spécifique pour ce code-barre
    `statut` ENUM('Actif', 'Inactif') DEFAULT 'Actif',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`article_id`) REFERENCES `articles`(`id`) ON DELETE CASCADE
);

-- =============================================
-- 8. TABLE DES FOURNISSEURS
-- =============================================
CREATE TABLE `fournisseurs` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `code_fournisseur` VARCHAR(50) UNIQUE NOT NULL,
    `nom_fournisseur` VARCHAR(255) NOT NULL,
    `raison_sociale` VARCHAR(255),
    `adresse` TEXT,
    `ville` VARCHAR(100),
    `wilaya` VARCHAR(100),
    `code_postal` VARCHAR(10),
    `telephone` VARCHAR(20),
    `fax` VARCHAR(20),
    `email` VARCHAR(100),
    `site_web` VARCHAR(255),
    `nif` VARCHAR(20), -- Numéro d'Identification Fiscale
    `nis` VARCHAR(20), -- Numéro d'Identification Statistique
    `rc` VARCHAR(20), -- Registre de Commerce
    `art` VARCHAR(20), -- Article d'Imposition
    `solde_initial` DECIMAL(15,2) DEFAULT 0,
    `solde_actuel` DECIMAL(15,2) DEFAULT 0,
    `credit_limite` DECIMAL(15,2) DEFAULT 0,
    `delai_paiement` INT DEFAULT 30, -- en jours
    `remise_habituelle` DECIMAL(5,2) DEFAULT 0,
    `contact_principal` VARCHAR(255),
    `telephone_contact` VARCHAR(20),
    `statut` ENUM('Actif', 'Inactif', 'Bloqué') DEFAULT 'Actif',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- =============================================
-- 9. TABLE DES CLIENTS
-- =============================================
CREATE TABLE `clients` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `code_client` VARCHAR(50) UNIQUE NOT NULL,
    `nom_client` VARCHAR(255) NOT NULL,
    `prenom_client` VARCHAR(255),
    `raison_sociale` VARCHAR(255), -- Pour les entreprises
    `type_client` ENUM('Particulier', 'Entreprise', 'Revendeur') DEFAULT 'Particulier',
    `adresse` TEXT,
    `ville` VARCHAR(100),
    `wilaya` VARCHAR(100),
    `code_postal` VARCHAR(10),
    `telephone` VARCHAR(20),
    `email` VARCHAR(100),
    `nif` VARCHAR(20), -- Pour les entreprises
    `nis` VARCHAR(20), -- Pour les entreprises
    `rc` VARCHAR(20), -- Pour les entreprises
    `art` VARCHAR(20), -- Pour les entreprises
    `solde_initial` DECIMAL(15,2) DEFAULT 0,
    `solde_actuel` DECIMAL(15,2) DEFAULT 0,
    `credit_limite` DECIMAL(15,2) DEFAULT 0,
    `remise_habituelle` DECIMAL(5,2) DEFAULT 0,
    `date_naissance` DATE NULL,
    `statut` ENUM('Actif', 'Inactif', 'Bloqué') DEFAULT 'Actif',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- =============================================
-- 10. TABLE DES MODES DE PAIEMENT
-- =============================================
CREATE TABLE `modes_paiement` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `nom_mode` VARCHAR(100) NOT NULL,
    `type_mode` ENUM('Espèces', 'Espèces_sans_timbre', 'Chèque', 'Virement', 'Carte_bancaire', 'Crédit', 'Autre') NOT NULL,
    `timbre_fiscal_requis` BOOLEAN DEFAULT FALSE,
    `taux_timbre` DECIMAL(5,2) DEFAULT 0, -- Pourcentage ou montant fixe
    `compte_comptable` VARCHAR(20),
    `statut` ENUM('Actif', 'Inactif') DEFAULT 'Actif',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =============================================
-- 11. TABLE DES BANQUES
-- =============================================
CREATE TABLE `banques` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `nom_banque` VARCHAR(255) NOT NULL,
    `code_banque` VARCHAR(10),
    `adresse` TEXT,
    `telephone` VARCHAR(20),
    `statut` ENUM('Actif', 'Inactif') DEFAULT 'Actif',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =============================================
-- 12. TABLE DES COMPTES BANCAIRES
-- =============================================
CREATE TABLE `comptes_bancaires` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `banque_id` INT NOT NULL,
    `numero_compte` VARCHAR(50) NOT NULL,
    `rib` VARCHAR(23), -- RIB algérien (23 chiffres)
    `iban` VARCHAR(34),
    `nom_titulaire` VARCHAR(255) NOT NULL,
    `solde_initial` DECIMAL(15,2) DEFAULT 0,
    `solde_actuel` DECIMAL(15,2) DEFAULT 0,
    `type_compte` ENUM('Courant', 'Épargne', 'Professionnel') DEFAULT 'Courant',
    `statut` ENUM('Actif', 'Inactif', 'Fermé') DEFAULT 'Actif',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (`banque_id`) REFERENCES `banques`(`id`) ON DELETE RESTRICT
);

-- =============================================
-- 13. TABLE DES CAISSES
-- =============================================
CREATE TABLE `caisses` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `nom_caisse` VARCHAR(100) NOT NULL,
    `code_caisse` VARCHAR(20) UNIQUE NOT NULL,
    `solde_initial` DECIMAL(15,2) DEFAULT 0,
    `solde_actuel` DECIMAL(15,2) DEFAULT 0,
    `utilisateur_responsable_id` INT,
    `statut` ENUM('Ouverte', 'Fermée', 'Suspendue') DEFAULT 'Fermée',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (`utilisateur_responsable_id`) REFERENCES `utilisateurs`(`id`) ON DELETE SET NULL
);

-- =============================================
-- 14. TABLE DES FACTURES DE VENTE
-- =============================================
CREATE TABLE `factures_vente` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `numero_facture` VARCHAR(50) UNIQUE NOT NULL,
    `type_facture` ENUM('Facture', 'Proforma', 'Bon_livraison', 'Devis') DEFAULT 'Facture',
    `client_id` INT,
    `utilisateur_id` INT NOT NULL,
    `caisse_id` INT,
    `date_facture` DATE NOT NULL,
    `heure_facture` TIME NOT NULL,
    `montant_ht` DECIMAL(15,2) DEFAULT 0,
    `montant_tva` DECIMAL(15,2) DEFAULT 0,
    `montant_ttc` DECIMAL(15,2) DEFAULT 0,
    `remise_globale` DECIMAL(15,2) DEFAULT 0,
    `remise_pourcentage` DECIMAL(5,2) DEFAULT 0,
    `timbre_fiscal` DECIMAL(8,2) DEFAULT 0,
    `montant_total` DECIMAL(15,2) DEFAULT 0,
    `montant_paye` DECIMAL(15,2) DEFAULT 0,
    `montant_restant` DECIMAL(15,2) DEFAULT 0,
    `mode_paiement_id` INT,
    `statut_paiement` ENUM('Non_payé', 'Partiellement_payé', 'Payé', 'Remboursé') DEFAULT 'Non_payé',
    `statut_facture` ENUM('Brouillon', 'Validée', 'Annulée', 'Archivée') DEFAULT 'Brouillon',
    `notes` TEXT,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (`client_id`) REFERENCES `clients`(`id`) ON DELETE SET NULL,
    FOREIGN KEY (`utilisateur_id`) REFERENCES `utilisateurs`(`id`) ON DELETE RESTRICT,
    FOREIGN KEY (`caisse_id`) REFERENCES `caisses`(`id`) ON DELETE SET NULL,
    FOREIGN KEY (`mode_paiement_id`) REFERENCES `modes_paiement`(`id`) ON DELETE SET NULL
);

-- =============================================
-- 15. TABLE DES DÉTAILS FACTURES DE VENTE
-- =============================================
CREATE TABLE `details_facture_vente` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `facture_vente_id` INT NOT NULL,
    `article_id` INT NOT NULL,
    `code_barre_utilise` VARCHAR(100),
    `quantite` DECIMAL(10,2) NOT NULL,
    `prix_unitaire_ht` DECIMAL(12,2) NOT NULL,
    `prix_unitaire_ttc` DECIMAL(12,2) NOT NULL,
    `remise_ligne` DECIMAL(8,2) DEFAULT 0,
    `remise_pourcentage` DECIMAL(5,2) DEFAULT 0,
    `tva_taux` DECIMAL(5,2) DEFAULT 19.00,
    `montant_ht` DECIMAL(12,2) NOT NULL,
    `montant_tva` DECIMAL(12,2) NOT NULL,
    `montant_ttc` DECIMAL(12,2) NOT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`facture_vente_id`) REFERENCES `factures_vente`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`article_id`) REFERENCES `articles`(`id`) ON DELETE RESTRICT
);

-- =============================================
-- 16. TABLE DES FACTURES D'ACHAT
-- =============================================
CREATE TABLE `factures_achat` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `numero_facture` VARCHAR(50) NOT NULL,
    `numero_facture_fournisseur` VARCHAR(50),
    `fournisseur_id` INT NOT NULL,
    `utilisateur_id` INT NOT NULL,
    `date_facture` DATE NOT NULL,
    `date_echeance` DATE,
    `montant_ht` DECIMAL(15,2) DEFAULT 0,
    `montant_tva` DECIMAL(15,2) DEFAULT 0,
    `montant_ttc` DECIMAL(15,2) DEFAULT 0,
    `remise_globale` DECIMAL(15,2) DEFAULT 0,
    `remise_pourcentage` DECIMAL(5,2) DEFAULT 0,
    `montant_total` DECIMAL(15,2) DEFAULT 0,
    `montant_paye` DECIMAL(15,2) DEFAULT 0,
    `montant_restant` DECIMAL(15,2) DEFAULT 0,
    `mode_paiement_id` INT,
    `statut_paiement` ENUM('Non_payé', 'Partiellement_payé', 'Payé') DEFAULT 'Non_payé',
    `statut_facture` ENUM('Brouillon', 'Validée', 'Annulée') DEFAULT 'Brouillon',
    `notes` TEXT,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (`fournisseur_id`) REFERENCES `fournisseurs`(`id`) ON DELETE RESTRICT,
    FOREIGN KEY (`utilisateur_id`) REFERENCES `utilisateurs`(`id`) ON DELETE RESTRICT,
    FOREIGN KEY (`mode_paiement_id`) REFERENCES `modes_paiement`(`id`) ON DELETE SET NULL
);

-- =============================================
-- 17. TABLE DES DÉTAILS FACTURES D'ACHAT
-- =============================================
CREATE TABLE `details_facture_achat` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `facture_achat_id` INT NOT NULL,
    `article_id` INT NOT NULL,
    `quantite` DECIMAL(10,2) NOT NULL,
    `prix_unitaire_ht` DECIMAL(12,2) NOT NULL,
    `prix_unitaire_ttc` DECIMAL(12,2) NOT NULL,
    `remise_ligne` DECIMAL(8,2) DEFAULT 0,
    `remise_pourcentage` DECIMAL(5,2) DEFAULT 0,
    `tva_taux` DECIMAL(5,2) DEFAULT 19.00,
    `montant_ht` DECIMAL(12,2) NOT NULL,
    `montant_tva` DECIMAL(12,2) NOT NULL,
    `montant_ttc` DECIMAL(12,2) NOT NULL,
    `date_expiration` DATE NULL,
    `numero_lot` VARCHAR(100),
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`facture_achat_id`) REFERENCES `factures_achat`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`article_id`) REFERENCES `articles`(`id`) ON DELETE RESTRICT
);

-- =============================================
-- 18. TABLE DES RETOURS CLIENTS
-- =============================================
CREATE TABLE `retours_clients` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `numero_retour` VARCHAR(50) UNIQUE NOT NULL,
    `facture_vente_id` INT,
    `client_id` INT,
    `utilisateur_id` INT NOT NULL,
    `date_retour` DATE NOT NULL,
    `motif_retour` ENUM('Défectueux', 'Erreur_commande', 'Non_conforme', 'Autre') NOT NULL,
    `description_motif` TEXT,
    `montant_ht` DECIMAL(15,2) DEFAULT 0,
    `montant_tva` DECIMAL(15,2) DEFAULT 0,
    `montant_ttc` DECIMAL(15,2) DEFAULT 0,
    `montant_rembourse` DECIMAL(15,2) DEFAULT 0,
    `mode_remboursement_id` INT,
    `statut` ENUM('En_attente', 'Approuvé', 'Rejeté', 'Remboursé') DEFAULT 'En_attente',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (`facture_vente_id`) REFERENCES `factures_vente`(`id`) ON DELETE SET NULL,
    FOREIGN KEY (`client_id`) REFERENCES `clients`(`id`) ON DELETE SET NULL,
    FOREIGN KEY (`utilisateur_id`) REFERENCES `utilisateurs`(`id`) ON DELETE RESTRICT,
    FOREIGN KEY (`mode_remboursement_id`) REFERENCES `modes_paiement`(`id`) ON DELETE SET NULL
);

-- =============================================
-- 19. TABLE DES DÉTAILS RETOURS CLIENTS
-- =============================================
CREATE TABLE `details_retour_client` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `retour_client_id` INT NOT NULL,
    `article_id` INT NOT NULL,
    `quantite_retournee` DECIMAL(10,2) NOT NULL,
    `prix_unitaire_ht` DECIMAL(12,2) NOT NULL,
    `prix_unitaire_ttc` DECIMAL(12,2) NOT NULL,
    `tva_taux` DECIMAL(5,2) DEFAULT 19.00,
    `montant_ht` DECIMAL(12,2) NOT NULL,
    `montant_tva` DECIMAL(12,2) NOT NULL,
    `montant_ttc` DECIMAL(12,2) NOT NULL,
    `action_stock` ENUM('Remettre_stock', 'Détruire', 'Retour_fournisseur') DEFAULT 'Remettre_stock',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`retour_client_id`) REFERENCES `retours_clients`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`article_id`) REFERENCES `articles`(`id`) ON DELETE RESTRICT
);

-- =============================================
-- 20. TABLE DES RETOURS FOURNISSEURS
-- =============================================
CREATE TABLE `retours_fournisseurs` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `numero_retour` VARCHAR(50) UNIQUE NOT NULL,
    `facture_achat_id` INT,
    `fournisseur_id` INT NOT NULL,
    `utilisateur_id` INT NOT NULL,
    `date_retour` DATE NOT NULL,
    `motif_retour` ENUM('Défectueux', 'Erreur_livraison', 'Non_conforme', 'Périmé', 'Autre') NOT NULL,
    `description_motif` TEXT,
    `montant_ht` DECIMAL(15,2) DEFAULT 0,
    `montant_tva` DECIMAL(15,2) DEFAULT 0,
    `montant_ttc` DECIMAL(15,2) DEFAULT 0,
    `montant_rembourse` DECIMAL(15,2) DEFAULT 0,
    `statut` ENUM('En_attente', 'Approuvé', 'Rejeté', 'Remboursé') DEFAULT 'En_attente',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (`facture_achat_id`) REFERENCES `factures_achat`(`id`) ON DELETE SET NULL,
    FOREIGN KEY (`fournisseur_id`) REFERENCES `fournisseurs`(`id`) ON DELETE RESTRICT,
    FOREIGN KEY (`utilisateur_id`) REFERENCES `utilisateurs`(`id`) ON DELETE RESTRICT
);

-- =============================================
-- 21. TABLE DES DÉTAILS RETOURS FOURNISSEURS
-- =============================================
CREATE TABLE `details_retour_fournisseur` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `retour_fournisseur_id` INT NOT NULL,
    `article_id` INT NOT NULL,
    `quantite_retournee` DECIMAL(10,2) NOT NULL,
    `prix_unitaire_ht` DECIMAL(12,2) NOT NULL,
    `prix_unitaire_ttc` DECIMAL(12,2) NOT NULL,
    `tva_taux` DECIMAL(5,2) DEFAULT 19.00,
    `montant_ht` DECIMAL(12,2) NOT NULL,
    `montant_tva` DECIMAL(12,2) NOT NULL,
    `montant_ttc` DECIMAL(12,2) NOT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`retour_fournisseur_id`) REFERENCES `retours_fournisseurs`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`article_id`) REFERENCES `articles`(`id`) ON DELETE RESTRICT
);

-- =============================================
-- 22. TABLE DES PROMOTIONS
-- =============================================
CREATE TABLE `promotions` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `nom_promotion` VARCHAR(255) NOT NULL,
    `description` TEXT,
    `type_promotion` ENUM('Pourcentage', 'Montant_fixe', 'Achetez_X_Obtenez_Y', 'Pack') NOT NULL,
    `valeur_remise` DECIMAL(8,2) DEFAULT 0,
    `quantite_requise` INT DEFAULT 1, -- Pour "Achetez X"
    `quantite_gratuite` INT DEFAULT 0, -- Pour "Obtenez Y"
    `date_debut` DATE NOT NULL,
    `date_fin` DATE NOT NULL,
    `heure_debut` TIME DEFAULT '00:00:00',
    `heure_fin` TIME DEFAULT '23:59:59',
    `jours_semaine` SET('Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi', 'Dimanche') DEFAULT 'Lundi,Mardi,Mercredi,Jeudi,Vendredi,Samedi,Dimanche',
    `limite_utilisation` INT DEFAULT 0, -- 0 = illimité
    `utilisations_actuelles` INT DEFAULT 0,
    `montant_minimum_achat` DECIMAL(12,2) DEFAULT 0,
    `applicable_clients` ENUM('Tous', 'Particuliers', 'Entreprises', 'Revendeurs') DEFAULT 'Tous',
    `statut` ENUM('Actif', 'Inactif', 'Expiré') DEFAULT 'Actif',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- =============================================
-- 23. TABLE DES ARTICLES EN PROMOTION
-- =============================================
CREATE TABLE `articles_promotion` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `promotion_id` INT NOT NULL,
    `article_id` INT NOT NULL,
    `prix_promo` DECIMAL(12,2) NULL, -- Prix spécial pour cette promotion
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`promotion_id`) REFERENCES `promotions`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`article_id`) REFERENCES `articles`(`id`) ON DELETE CASCADE,
    UNIQUE KEY `unique_promo_article` (`promotion_id`, `article_id`)
);

-- =============================================
-- 24. TABLE DES PACKS/LOTS
-- =============================================
CREATE TABLE `packs` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `nom_pack` VARCHAR(255) NOT NULL,
    `description` TEXT,
    `code_pack` VARCHAR(50) UNIQUE NOT NULL,
    `prix_pack` DECIMAL(12,2) NOT NULL,
    `prix_unitaire_calcule` DECIMAL(12,2) DEFAULT 0, -- Prix calculé automatiquement
    `remise_pack` DECIMAL(8,2) DEFAULT 0,
    `tva_pack` DECIMAL(5,2) DEFAULT 19.00,
    `stock_pack` DECIMAL(10,2) DEFAULT 0,
    `statut` ENUM('Actif', 'Inactif') DEFAULT 'Actif',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- =============================================
-- 25. TABLE DES ARTICLES DANS LES PACKS
-- =============================================
CREATE TABLE `articles_pack` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `pack_id` INT NOT NULL,
    `article_id` INT NOT NULL,
    `quantite` DECIMAL(10,2) NOT NULL DEFAULT 1,
    `prix_unitaire` DECIMAL(12,2) NOT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`pack_id`) REFERENCES `packs`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`article_id`) REFERENCES `articles`(`id`) ON DELETE CASCADE
);

-- =============================================
-- 26. TABLE DES MOUVEMENTS DE STOCK
-- =============================================
CREATE TABLE `mouvements_stock` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `article_id` INT NOT NULL,
    `type_mouvement` ENUM('Entrée', 'Sortie', 'Ajustement', 'Transfert', 'Inventaire') NOT NULL,
    `reference_document` VARCHAR(100), -- Numéro facture, bon, etc.
    `type_document` ENUM('Facture_achat', 'Facture_vente', 'Retour_client', 'Retour_fournisseur', 'Ajustement_manuel', 'Inventaire', 'Transfert') NOT NULL,
    `document_id` INT, -- ID du document source
    `quantite_avant` DECIMAL(10,2) NOT NULL,
    `quantite_mouvement` DECIMAL(10,2) NOT NULL,
    `quantite_apres` DECIMAL(10,2) NOT NULL,
    `prix_unitaire` DECIMAL(12,2) DEFAULT 0,
    `cout_total` DECIMAL(15,2) DEFAULT 0,
    `utilisateur_id` INT NOT NULL,
    `date_mouvement` DATE NOT NULL,
    `heure_mouvement` TIME NOT NULL,
    `motif` VARCHAR(255),
    `notes` TEXT,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`article_id`) REFERENCES `articles`(`id`) ON DELETE RESTRICT,
    FOREIGN KEY (`utilisateur_id`) REFERENCES `utilisateurs`(`id`) ON DELETE RESTRICT
);

-- =============================================
-- 27. TABLE DES INVENTAIRES
-- =============================================
CREATE TABLE `inventaires` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `numero_inventaire` VARCHAR(50) UNIQUE NOT NULL,
    `nom_inventaire` VARCHAR(255) NOT NULL,
    `date_inventaire` DATE NOT NULL,
    `utilisateur_id` INT NOT NULL,
    `statut` ENUM('En_cours', 'Terminé', 'Validé', 'Annulé') DEFAULT 'En_cours',
    `notes` TEXT,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (`utilisateur_id`) REFERENCES `utilisateurs`(`id`) ON DELETE RESTRICT
);

-- =============================================
-- 28. TABLE DES DÉTAILS INVENTAIRES
-- =============================================
CREATE TABLE `details_inventaire` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `inventaire_id` INT NOT NULL,
    `article_id` INT NOT NULL,
    `stock_theorique` DECIMAL(10,2) NOT NULL,
    `stock_physique` DECIMAL(10,2) NOT NULL,
    `ecart` DECIMAL(10,2) NOT NULL,
    `valeur_ecart` DECIMAL(15,2) DEFAULT 0,
    `motif_ecart` VARCHAR(255),
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`inventaire_id`) REFERENCES `inventaires`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`article_id`) REFERENCES `articles`(`id`) ON DELETE RESTRICT
);

-- =============================================
-- 29. TABLE DES CATÉGORIES DE DÉPENSES
-- =============================================
CREATE TABLE `categories_depenses` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `nom_categorie` VARCHAR(255) NOT NULL,
    `description` TEXT,
    `code_comptable` VARCHAR(20),
    `statut` ENUM('Actif', 'Inactif') DEFAULT 'Actif',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =============================================
-- 30. TABLE DES DÉPENSES
-- =============================================
CREATE TABLE `depenses` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `numero_depense` VARCHAR(50) UNIQUE NOT NULL,
    `categorie_depense_id` INT NOT NULL,
    `fournisseur_id` INT NULL,
    `utilisateur_id` INT NOT NULL,
    `date_depense` DATE NOT NULL,
    `description` VARCHAR(255) NOT NULL,
    `montant_ht` DECIMAL(15,2) DEFAULT 0,
    `montant_tva` DECIMAL(15,2) DEFAULT 0,
    `montant_ttc` DECIMAL(15,2) NOT NULL,
    `mode_paiement_id` INT,
    `compte_bancaire_id` INT NULL,
    `caisse_id` INT NULL,
    `numero_piece` VARCHAR(100), -- Numéro de la pièce justificative
    `statut` ENUM('En_attente', 'Payé', 'Annulé') DEFAULT 'En_attente',
    `notes` TEXT,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (`categorie_depense_id`) REFERENCES `categories_depenses`(`id`) ON DELETE RESTRICT,
    FOREIGN KEY (`fournisseur_id`) REFERENCES `fournisseurs`(`id`) ON DELETE SET NULL,
    FOREIGN KEY (`utilisateur_id`) REFERENCES `utilisateurs`(`id`) ON DELETE RESTRICT,
    FOREIGN KEY (`mode_paiement_id`) REFERENCES `modes_paiement`(`id`) ON DELETE SET NULL,
    FOREIGN KEY (`compte_bancaire_id`) REFERENCES `comptes_bancaires`(`id`) ON DELETE SET NULL,
    FOREIGN KEY (`caisse_id`) REFERENCES `caisses`(`id`) ON DELETE SET NULL
);

-- =============================================
-- 31. TABLE DES CATÉGORIES DE REVENUS
-- =============================================
CREATE TABLE `categories_revenus` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `nom_categorie` VARCHAR(255) NOT NULL,
    `description` TEXT,
    `code_comptable` VARCHAR(20),
    `statut` ENUM('Actif', 'Inactif') DEFAULT 'Actif',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =============================================
-- 32. TABLE DES REVENUS (AUTRES QUE VENTES)
-- =============================================
CREATE TABLE `revenus` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `numero_revenu` VARCHAR(50) UNIQUE NOT NULL,
    `categorie_revenu_id` INT NOT NULL,
    `client_id` INT NULL,
    `utilisateur_id` INT NOT NULL,
    `date_revenu` DATE NOT NULL,
    `description` VARCHAR(255) NOT NULL,
    `montant_ht` DECIMAL(15,2) DEFAULT 0,
    `montant_tva` DECIMAL(15,2) DEFAULT 0,
    `montant_ttc` DECIMAL(15,2) NOT NULL,
    `mode_paiement_id` INT,
    `compte_bancaire_id` INT NULL,
    `caisse_id` INT NULL,
    `numero_piece` VARCHAR(100),
    `statut` ENUM('En_attente', 'Encaissé', 'Annulé') DEFAULT 'En_attente',
    `notes` TEXT,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (`categorie_revenu_id`) REFERENCES `categories_revenus`(`id`) ON DELETE RESTRICT,
    FOREIGN KEY (`client_id`) REFERENCES `clients`(`id`) ON DELETE SET NULL,
    FOREIGN KEY (`utilisateur_id`) REFERENCES `utilisateurs`(`id`) ON DELETE RESTRICT,
    FOREIGN KEY (`mode_paiement_id`) REFERENCES `modes_paiement`(`id`) ON DELETE SET NULL,
    FOREIGN KEY (`compte_bancaire_id`) REFERENCES `comptes_bancaires`(`id`) ON DELETE SET NULL,
    FOREIGN KEY (`caisse_id`) REFERENCES `caisses`(`id`) ON DELETE SET NULL
);

-- =============================================
-- 33. TABLE DES PAIEMENTS
-- =============================================
CREATE TABLE `paiements` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `numero_paiement` VARCHAR(50) UNIQUE NOT NULL,
    `type_paiement` ENUM('Vente', 'Achat', 'Dépense', 'Revenu') NOT NULL,
    `document_id` INT NOT NULL, -- ID de la facture/document concerné
    `montant_paiement` DECIMAL(15,2) NOT NULL,
    `mode_paiement_id` INT NOT NULL,
    `compte_bancaire_id` INT NULL,
    `caisse_id` INT NULL,
    `numero_cheque` VARCHAR(50) NULL,
    `date_echeance_cheque` DATE NULL,
    `banque_cheque` VARCHAR(255) NULL,
    `timbre_fiscal` DECIMAL(8,2) DEFAULT 0,
    `date_paiement` DATE NOT NULL,
    `heure_paiement` TIME NOT NULL,
    `utilisateur_id` INT NOT NULL,
    `notes` TEXT,
    `statut` ENUM('Validé', 'En_attente', 'Rejeté', 'Annulé') DEFAULT 'Validé',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (`mode_paiement_id`) REFERENCES `modes_paiement`(`id`) ON DELETE RESTRICT,
    FOREIGN KEY (`compte_bancaire_id`) REFERENCES `comptes_bancaires`(`id`) ON DELETE SET NULL,
    FOREIGN KEY (`caisse_id`) REFERENCES `caisses`(`id`) ON DELETE SET NULL,
    FOREIGN KEY (`utilisateur_id`) REFERENCES `utilisateurs`(`id`) ON DELETE RESTRICT
);

-- =============================================
-- 34. TABLE DES MOUVEMENTS DE CAISSE
-- =============================================
CREATE TABLE `mouvements_caisse` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `caisse_id` INT NOT NULL,
    `type_mouvement` ENUM('Entrée', 'Sortie', 'Ouverture', 'Fermeture', 'Transfert') NOT NULL,
    `montant` DECIMAL(15,2) NOT NULL,
    `solde_avant` DECIMAL(15,2) NOT NULL,
    `solde_apres` DECIMAL(15,2) NOT NULL,
    `reference_document` VARCHAR(100),
    `type_document` ENUM('Facture_vente', 'Dépense', 'Revenu', 'Transfert', 'Ajustement') NULL,
    `document_id` INT NULL,
    `utilisateur_id` INT NOT NULL,
    `date_mouvement` DATE NOT NULL,
    `heure_mouvement` TIME NOT NULL,
    `description` VARCHAR(255),
    `notes` TEXT,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`caisse_id`) REFERENCES `caisses`(`id`) ON DELETE RESTRICT,
    FOREIGN KEY (`utilisateur_id`) REFERENCES `utilisateurs`(`id`) ON DELETE RESTRICT
);

-- =============================================
-- 35. TABLE DES MOUVEMENTS BANCAIRES
-- =============================================
CREATE TABLE `mouvements_bancaires` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `compte_bancaire_id` INT NOT NULL,
    `type_mouvement` ENUM('Crédit', 'Débit', 'Virement_entrant', 'Virement_sortant', 'Frais_bancaires') NOT NULL,
    `montant` DECIMAL(15,2) NOT NULL,
    `solde_avant` DECIMAL(15,2) NOT NULL,
    `solde_apres` DECIMAL(15,2) NOT NULL,
    `reference_document` VARCHAR(100),
    `type_document` ENUM('Facture_achat', 'Dépense', 'Revenu', 'Transfert', 'Frais') NULL,
    `document_id` INT NULL,
    `numero_operation` VARCHAR(50),
    `utilisateur_id` INT NOT NULL,
    `date_mouvement` DATE NOT NULL,
    `date_valeur` DATE NOT NULL,
    `description` VARCHAR(255),
    `notes` TEXT,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`compte_bancaire_id`) REFERENCES `comptes_bancaires`(`id`) ON DELETE RESTRICT,
    FOREIGN KEY (`utilisateur_id`) REFERENCES `utilisateurs`(`id`) ON DELETE RESTRICT
);

-- =============================================
-- 36. TABLE DES PARAMÈTRES SYSTÈME
-- =============================================
CREATE TABLE `parametres_systeme` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `cle_parametre` VARCHAR(100) UNIQUE NOT NULL,
    `valeur_parametre` TEXT,
    `type_parametre` ENUM('String', 'Integer', 'Decimal', 'Boolean', 'Date', 'JSON') DEFAULT 'String',
    `description` VARCHAR(255),
    `categorie` VARCHAR(100) DEFAULT 'Général',
    `modifiable` BOOLEAN DEFAULT TRUE,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- =============================================
-- 37. TABLE DES LOGS SYSTÈME
-- =============================================
CREATE TABLE `logs_systeme` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `utilisateur_id` INT NULL,
    `action` VARCHAR(255) NOT NULL,
    `table_affectee` VARCHAR(100),
    `id_enregistrement` INT NULL,
    `anciennes_valeurs` JSON NULL,
    `nouvelles_valeurs` JSON NULL,
    `adresse_ip` VARCHAR(45),
    `user_agent` TEXT,
    `date_action` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`utilisateur_id`) REFERENCES `utilisateurs`(`id`) ON DELETE SET NULL
);

-- =============================================
-- 38. TABLE DES SAUVEGARDES
-- =============================================
CREATE TABLE `sauvegardes` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `nom_sauvegarde` VARCHAR(255) NOT NULL,
    `chemin_fichier` VARCHAR(500) NOT NULL,
    `taille_fichier` BIGINT DEFAULT 0,
    `type_sauvegarde` ENUM('Complète', 'Incrémentale', 'Différentielle') DEFAULT 'Complète',
    `utilisateur_id` INT NOT NULL,
    `date_sauvegarde` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `statut` ENUM('Réussie', 'Échouée', 'En_cours') DEFAULT 'En_cours',
    `notes` TEXT,
    FOREIGN KEY (`utilisateur_id`) REFERENCES `utilisateurs`(`id`) ON DELETE RESTRICT
);

-- =============================================
-- INSERTION DES DONNÉES INITIALES
-- =============================================

-- Insertion des unités de mesure de base
INSERT INTO `unites_mesure` (`nom_unite`, `abreviation`, `type_unite`) VALUES
('Pièce', 'Pcs', 'Quantité'),
('Kilogramme', 'Kg', 'Poids'),
('Gramme', 'g', 'Poids'),
('Litre', 'L', 'Volume'),
('Millilitre', 'ml', 'Volume'),
('Mètre', 'm', 'Longueur'),
('Centimètre', 'cm', 'Longueur'),
('Boîte', 'Bte', 'Quantité'),
('Carton', 'Ctn', 'Quantité'),
('Paquet', 'Pqt', 'Quantité');

-- Insertion des modes de paiement avec spécificités algériennes
INSERT INTO `modes_paiement` (`nom_mode`, `type_mode`, `timbre_fiscal_requis`, `compte_comptable`) VALUES
('Espèces', 'Espèces', TRUE, '530'),
('Espèces sans timbre', 'Espèces_sans_timbre', FALSE, '530'),
('Chèque bancaire', 'Chèque', TRUE, '512'),
('Virement bancaire', 'Virement', FALSE, '512'),
('Carte bancaire', 'Carte_bancaire', FALSE, '512'),
('Crédit client', 'Crédit', FALSE, '411'),
('Autre', 'Autre', FALSE, '530');

-- Insertion des banques algériennes principales
INSERT INTO `banques` (`nom_banque`, `code_banque`, `telephone`) VALUES
('Banque Nationale d\'Algérie (BNA)', '001', '021-23-45-67'),
('Crédit Populaire d\'Algérie (CPA)', '002', '021-23-45-68'),
('Banque Extérieure d\'Algérie (BEA)', '003', '021-23-45-69'),
('Banque de l\'Agriculture et du Développement Rural (BADR)', '004', '021-23-45-70'),
('Banque de Développement Local (BDL)', '005', '021-23-45-71'),
('Société Générale Algérie', '006', '021-23-45-72'),
('BNP Paribas El Djazair', '007', '021-23-45-73'),
('Trust Bank Algeria', '008', '021-23-45-74'),
('Al Baraka Bank Algeria', '009', '021-23-45-75'),
('Gulf Bank Algeria', '010', '021-23-45-76');

-- Insertion des catégories de base
INSERT INTO `categories` (`nom_categorie`, `code_categorie`, `description`, `tva_applicable`) VALUES
('Alimentation générale', 'ALIM', 'Produits alimentaires de base', 9.00),
('Boissons', 'BOIS', 'Boissons diverses', 19.00),
('Produits laitiers', 'LAIT', 'Lait, fromages, yaourts', 9.00),
('Viandes et poissons', 'VIAN', 'Viandes fraîches et poissons', 9.00),
('Fruits et légumes', 'FRUI', 'Fruits et légumes frais', 9.00),
('Produits d\'hygiène', 'HYGI', 'Savons, shampoings, etc.', 19.00),
('Produits d\'entretien', 'ENTR', 'Produits de nettoyage', 19.00),
('Électroménager', 'ELEC', 'Appareils électroménagers', 19.00),
('Textiles', 'TEXT', 'Vêtements et textiles', 19.00),
('Divers', 'DIVE', 'Autres produits', 19.00);

-- Insertion des catégories de dépenses
INSERT INTO `categories_depenses` (`nom_categorie`, `code_comptable`, `description`) VALUES
('Achats de marchandises', '607', 'Achats pour revente'),
('Frais de transport', '624', 'Transport et livraison'),
('Électricité', '605', 'Factures d\'électricité'),
('Eau', '605', 'Factures d\'eau'),
('Téléphone/Internet', '626', 'Communications'),
('Loyer', '613', 'Loyer des locaux'),
('Assurances', '616', 'Primes d\'assurance'),
('Entretien et réparations', '615', 'Maintenance'),
('Fournitures de bureau', '606', 'Papeterie, etc.'),
('Salaires', '641', 'Rémunérations du personnel'),
('Charges sociales', '645', 'Cotisations sociales'),
('Impôts et taxes', '635', 'Taxes diverses'),
('Frais bancaires', '627', 'Commissions bancaires'),
('Publicité', '623', 'Frais de publicité'),
('Autres charges', '658', 'Charges diverses');

-- Insertion des catégories de revenus
INSERT INTO `categories_revenus` (`nom_categorie`, `code_comptable`, `description`) VALUES
('Ventes de marchandises', '707', 'Chiffre d\'affaires principal'),
('Prestations de services', '706', 'Services rendus'),
('Revenus financiers', '766', 'Intérêts bancaires'),
('Subventions', '740', 'Aides reçues'),
('Autres produits', '758', 'Revenus divers');

-- Insertion des paramètres système de base
INSERT INTO `parametres_systeme` (`cle_parametre`, `valeur_parametre`, `type_parametre`, `description`, `categorie`) VALUES
('tva_defaut', '19.00', 'Decimal', 'Taux de TVA par défaut', 'Fiscal'),
('tva_reduit', '9.00', 'Decimal', 'Taux de TVA réduit', 'Fiscal'),
('timbre_seuil_min', '300.00', 'Decimal', 'Seuil minimum pour timbre fiscal (DA)', 'Fiscal'),
('timbre_seuil_max', '30000.00', 'Decimal', 'Seuil maximum pour timbre 1 DA', 'Fiscal'),
('timbre_taux_1', '1.00', 'Decimal', 'Montant timbre pour 300-30000 DA', 'Fiscal'),
('timbre_taux_2', '1.50', 'Decimal', 'Montant timbre par tranche 100 DA (30000-100000)', 'Fiscal'),
('devise_principale', 'DA', 'String', 'Devise principale (Dinar Algérien)', 'Général'),
('format_facture', 'FAC-{YYYY}-{MM}-{NNNN}', 'String', 'Format numérotation factures', 'Général'),
('backup_auto', 'true', 'Boolean', 'Sauvegarde automatique activée', 'Système'),
('backup_frequence', '7', 'Integer', 'Fréquence sauvegarde (jours)', 'Système'),
('stock_alerte_active', 'true', 'Boolean', 'Alertes stock activées', 'Stock'),
('langue_interface', 'fr', 'String', 'Langue de l\'interface', 'Interface'),
('format_date', 'dd/MM/yyyy', 'String', 'Format d\'affichage des dates', 'Interface'),
('decimal_precision', '2', 'Integer', 'Nombre de décimales pour les prix', 'Général');

-- Création de l'utilisateur administrateur par défaut
INSERT INTO `utilisateurs` (`nom_utilisateur`, `mot_de_passe`, `nom_complet`, `email`, `role`, `permissions`) VALUES
('admin', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Administrateur Système', '<EMAIL>', 'Admin',
'{"ventes": true, "achats": true, "stock": true, "clients": true, "fournisseurs": true, "comptabilite": true, "parametres": true, "utilisateurs": true, "rapports": true, "sauvegardes": true}');

-- Création d'une caisse par défaut
INSERT INTO `caisses` (`nom_caisse`, `code_caisse`, `solde_initial`, `solde_actuel`, `utilisateur_responsable_id`) VALUES
('Caisse Principale', 'CAISSE01', 0.00, 0.00, 1);

-- =============================================
-- FONCTIONS ET PROCÉDURES STOCKÉES
-- =============================================

DELIMITER $$

-- =============================================
-- FONCTION: Calcul du timbre fiscal algérien
-- Règles:
-- - ≤ 300 DA: 0 DA
-- - 300-30000 DA: 1 DA
-- - 30000-100000 DA: 1.5 DA par tranche de 100 DA
-- =============================================
CREATE FUNCTION `CalculerTimbreFiscal`(montant DECIMAL(15,2))
RETURNS DECIMAL(8,2)
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE timbre DECIMAL(8,2) DEFAULT 0.00;

    IF montant <= 300.00 THEN
        SET timbre = 0.00;
    ELSEIF montant <= 30000.00 THEN
        SET timbre = 1.00;
    ELSEIF montant <= 100000.00 THEN
        SET timbre = CEIL((montant - 30000.00) / 100.00) * 1.50;
    ELSE
        -- Pour les montants > 100000 DA, appliquer le maximum
        SET timbre = CEIL((100000.00 - 30000.00) / 100.00) * 1.50;
    END IF;

    RETURN timbre;
END$$

-- =============================================
-- FONCTION: Génération du prochain numéro de facture
-- =============================================
CREATE FUNCTION `GenererNumeroFacture`(type_doc VARCHAR(10))
RETURNS VARCHAR(50)
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE nouveau_numero VARCHAR(50);
    DECLARE compteur INT DEFAULT 1;
    DECLARE annee_courante YEAR DEFAULT YEAR(CURDATE());
    DECLARE mois_courant INT DEFAULT MONTH(CURDATE());

    -- Récupérer le dernier numéro pour l'année et le mois courants
    SELECT COALESCE(MAX(CAST(SUBSTRING_INDEX(numero_facture, '-', -1) AS UNSIGNED)), 0) + 1
    INTO compteur
    FROM factures_vente
    WHERE YEAR(date_facture) = annee_courante
    AND MONTH(date_facture) = mois_courant
    AND numero_facture LIKE CONCAT(type_doc, '-', annee_courante, '-', LPAD(mois_courant, 2, '0'), '-%');

    SET nouveau_numero = CONCAT(type_doc, '-', annee_courante, '-', LPAD(mois_courant, 2, '0'), '-', LPAD(compteur, 4, '0'));

    RETURN nouveau_numero;
END$$

-- =============================================
-- FONCTION: Calcul du Prix Moyen Pondéré (PMP)
-- =============================================
CREATE FUNCTION `CalculerPMP`(article_id_param INT, nouvelle_quantite DECIMAL(10,2), nouveau_prix DECIMAL(12,2))
RETURNS DECIMAL(12,2)
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE stock_actuel DECIMAL(10,2) DEFAULT 0;
    DECLARE pmp_actuel DECIMAL(12,2) DEFAULT 0;
    DECLARE nouveau_pmp DECIMAL(12,2) DEFAULT 0;

    -- Récupérer le stock et PMP actuels
    SELECT stock_actuel, pmp INTO stock_actuel, pmp_actuel
    FROM articles
    WHERE id = article_id_param;

    -- Calculer le nouveau PMP
    IF (stock_actuel + nouvelle_quantite) > 0 THEN
        SET nouveau_pmp = ((stock_actuel * pmp_actuel) + (nouvelle_quantite * nouveau_prix)) / (stock_actuel + nouvelle_quantite);
    ELSE
        SET nouveau_pmp = nouveau_prix;
    END IF;

    RETURN nouveau_pmp;
END$$

-- =============================================
-- PROCÉDURE: Mise à jour du stock après vente
-- =============================================
CREATE PROCEDURE `MettreAJourStockVente`(
    IN p_article_id INT,
    IN p_quantite_vendue DECIMAL(10,2),
    IN p_prix_vente DECIMAL(12,2),
    IN p_utilisateur_id INT,
    IN p_facture_id INT
)
BEGIN
    DECLARE stock_avant DECIMAL(10,2);
    DECLARE stock_apres DECIMAL(10,2);

    -- Récupérer le stock actuel
    SELECT stock_actuel INTO stock_avant FROM articles WHERE id = p_article_id;

    -- Calculer le nouveau stock
    SET stock_apres = stock_avant - p_quantite_vendue;

    -- Mettre à jour le stock
    UPDATE articles
    SET stock_actuel = stock_apres,
        updated_at = CURRENT_TIMESTAMP
    WHERE id = p_article_id;

    -- Enregistrer le mouvement de stock
    INSERT INTO mouvements_stock (
        article_id, type_mouvement, reference_document, type_document, document_id,
        quantite_avant, quantite_mouvement, quantite_apres, prix_unitaire,
        cout_total, utilisateur_id, date_mouvement, heure_mouvement
    ) VALUES (
        p_article_id, 'Sortie', CONCAT('FAC-', p_facture_id), 'Facture_vente', p_facture_id,
        stock_avant, p_quantite_vendue, stock_apres, p_prix_vente,
        (p_quantite_vendue * p_prix_vente), p_utilisateur_id, CURDATE(), CURTIME()
    );
END$$

-- =============================================
-- PROCÉDURE: Mise à jour du stock après achat
-- =============================================
CREATE PROCEDURE `MettreAJourStockAchat`(
    IN p_article_id INT,
    IN p_quantite_achetee DECIMAL(10,2),
    IN p_prix_achat DECIMAL(12,2),
    IN p_utilisateur_id INT,
    IN p_facture_id INT
)
BEGIN
    DECLARE stock_avant DECIMAL(10,2);
    DECLARE stock_apres DECIMAL(10,2);
    DECLARE nouveau_pmp DECIMAL(12,2);

    -- Récupérer le stock actuel
    SELECT stock_actuel INTO stock_avant FROM articles WHERE id = p_article_id;

    -- Calculer le nouveau stock et PMP
    SET stock_apres = stock_avant + p_quantite_achetee;
    SET nouveau_pmp = CalculerPMP(p_article_id, p_quantite_achetee, p_prix_achat);

    -- Mettre à jour le stock et le PMP
    UPDATE articles
    SET stock_actuel = stock_apres,
        pmp = nouveau_pmp,
        prix_achat_unitaire = p_prix_achat,
        updated_at = CURRENT_TIMESTAMP
    WHERE id = p_article_id;

    -- Enregistrer le mouvement de stock
    INSERT INTO mouvements_stock (
        article_id, type_mouvement, reference_document, type_document, document_id,
        quantite_avant, quantite_mouvement, quantite_apres, prix_unitaire,
        cout_total, utilisateur_id, date_mouvement, heure_mouvement
    ) VALUES (
        p_article_id, 'Entrée', CONCAT('ACH-', p_facture_id), 'Facture_achat', p_facture_id,
        stock_avant, p_quantite_achetee, stock_apres, p_prix_achat,
        (p_quantite_achetee * p_prix_achat), p_utilisateur_id, CURDATE(), CURTIME()
    );
END$$

DELIMITER ;

-- =============================================
-- VUES POUR FACILITER LES REQUÊTES
-- =============================================

-- Vue pour les articles avec informations complètes
CREATE VIEW `vue_articles_complets` AS
SELECT
    a.id,
    a.code_article,
    a.nom_article,
    a.description,
    c.nom_categorie,
    u.nom_unite,
    a.prix_achat_unitaire,
    a.prix_vente_unitaire,
    a.prix_vente_gros,
    a.marge_beneficiaire,
    a.tva_achat,
    a.tva_vente,
    a.stock_minimum,
    a.stock_maximum,
    a.stock_actuel,
    a.stock_reserve,
    a.pmp,
    a.pieces_par_fardeau,
    a.prix_fardeau,
    a.statut,
    CASE
        WHEN a.stock_actuel <= a.stock_minimum THEN 'Stock faible'
        WHEN a.stock_actuel = 0 THEN 'Rupture de stock'
        ELSE 'Stock normal'
    END AS alerte_stock
FROM articles a
LEFT JOIN categories c ON a.categorie_id = c.id
LEFT JOIN unites_mesure u ON a.unite_mesure_id = u.id;

-- Vue pour le tableau de bord des ventes
CREATE VIEW `vue_dashboard_ventes` AS
SELECT
    DATE(fv.date_facture) as date_vente,
    COUNT(fv.id) as nombre_factures,
    SUM(fv.montant_ht) as total_ht,
    SUM(fv.montant_tva) as total_tva,
    SUM(fv.montant_ttc) as total_ttc,
    SUM(fv.timbre_fiscal) as total_timbre,
    SUM(fv.montant_total) as chiffre_affaires
FROM factures_vente fv
WHERE fv.statut_facture = 'Validée'
GROUP BY DATE(fv.date_facture)
ORDER BY date_vente DESC;

-- =============================================
-- INDEX POUR OPTIMISER LES PERFORMANCES
-- =============================================

-- Index sur les tables principales
CREATE INDEX idx_articles_code ON articles(code_article);
CREATE INDEX idx_articles_nom ON articles(nom_article);
CREATE INDEX idx_articles_categorie ON articles(categorie_id);
CREATE INDEX idx_articles_stock ON articles(stock_actuel);

CREATE INDEX idx_factures_vente_date ON factures_vente(date_facture);
CREATE INDEX idx_factures_vente_client ON factures_vente(client_id);
CREATE INDEX idx_factures_vente_statut ON factures_vente(statut_facture);

CREATE INDEX idx_factures_achat_date ON factures_achat(date_facture);
CREATE INDEX idx_factures_achat_fournisseur ON factures_achat(fournisseur_id);

CREATE INDEX idx_mouvements_stock_article ON mouvements_stock(article_id);
CREATE INDEX idx_mouvements_stock_date ON mouvements_stock(date_mouvement);

CREATE INDEX idx_codes_barres_code ON codes_barres(code_barre);
CREATE INDEX idx_clients_code ON clients(code_client);
CREATE INDEX idx_fournisseurs_code ON fournisseurs(code_fournisseur);

-- =============================================
-- TRIGGERS POUR L'AUDIT ET LA COHÉRENCE
-- =============================================

DELIMITER $$

-- Trigger pour logger les modifications d'articles
CREATE TRIGGER `tr_articles_audit`
AFTER UPDATE ON `articles`
FOR EACH ROW
BEGIN
    INSERT INTO logs_systeme (
        utilisateur_id, action, table_affectee, id_enregistrement,
        anciennes_valeurs, nouvelles_valeurs, date_action
    ) VALUES (
        @current_user_id, 'UPDATE', 'articles', NEW.id,
        JSON_OBJECT('stock_actuel', OLD.stock_actuel, 'pmp', OLD.pmp, 'prix_vente', OLD.prix_vente_unitaire),
        JSON_OBJECT('stock_actuel', NEW.stock_actuel, 'pmp', NEW.pmp, 'prix_vente', NEW.prix_vente_unitaire),
        NOW()
    );
END$$

DELIMITER ;

-- =============================================
-- COMMENTAIRES FINAUX
-- =============================================
/*
Cette base de données comprend:

1. ✅ Gestion complète des articles avec codes-barres multiples
2. ✅ Gestion des clients et fournisseurs avec informations fiscales algériennes (NIF, NIS, RC, ART)
3. ✅ Système de facturation complet (vente, achat, retours, proforma, bons de livraison)
4. ✅ Gestion des promotions et packs
5. ✅ Système de caisse et banques
6. ✅ Calcul automatique du timbre fiscal algérien
7. ✅ Gestion des stocks avec PMP automatique
8. ✅ Système de paiements multiples (espèces avec/sans timbre, chèques, virements)
9. ✅ Comptabilité de base (dépenses, revenus, mouvements)
10. ✅ Système d'audit et de logs
11. ✅ Paramètres système configurables
12. ✅ Vues optimisées pour les rapports
13. ✅ Index pour les performances
14. ✅ Fonctions et procédures pour l'automatisation

Spécificités algériennes intégrées:
- Calcul du timbre fiscal selon la réglementation algérienne
- Support des identifiants fiscaux (NIF, NIS, RC, ART)
- TVA à 9% et 19% selon les produits
- Modes de paiement adaptés au marché algérien
- Banques algériennes pré-configurées

Pour utiliser cette base de données:
1. Exécuter ce script sur un serveur MySQL 8.0+
2. Configurer les paramètres dans la table 'parametres_systeme'
3. Créer les utilisateurs et leurs permissions
4. Importer les articles et données de base
*/
