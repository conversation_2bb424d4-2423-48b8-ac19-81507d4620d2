using System;
using BCrypt.Net;

namespace GestionDeStock2024.UTILS
{
    /// <summary>
    /// Classe utilitaire pour la gestion sécurisée des mots de passe avec BCrypt
    /// </summary>
    public static class PasswordHelper
    {
        /// <summary>
        /// Hache un mot de passe en utilisant BCrypt
        /// </summary>
        /// <param name="password">Mot de passe en clair</param>
        /// <returns>Mot de passe haché</returns>
        public static string HashPassword(string password)
        {
            if (string.IsNullOrEmpty(password))
                throw new ArgumentException("Le mot de passe ne peut pas être vide", nameof(password));

            // Utilise BCrypt avec un salt automatique et un coût de 12 (recommandé pour 2024)
            return BCrypt.Net.BCrypt.HashPassword(password, 12);
        }

        /// <summary>
        /// Vérifie si un mot de passe correspond au hash stocké
        /// </summary>
        /// <param name="password">Mot de passe en clair à vérifier</param>
        /// <param name="hashedPassword">Hash stocké en base de données</param>
        /// <returns>True si le mot de passe correspond</returns>
        public static bool VerifyPassword(string password, string hashedPassword)
        {
            if (string.IsNullOrEmpty(password) || string.IsNullOrEmpty(hashedPassword))
                return false;

            try
            {
                return BCrypt.Net.BCrypt.Verify(password, hashedPassword);
            }
            catch (Exception)
            {
                // En cas d'erreur de vérification (hash invalide, etc.)
                return false;
            }
        }

        /// <summary>
        /// Vérifie si un hash de mot de passe nécessite une mise à jour
        /// (par exemple, si le coût de hachage a changé)
        /// </summary>
        /// <param name="hashedPassword">Hash à vérifier</param>
        /// <param name="targetCost">Coût cible (par défaut 12)</param>
        /// <returns>True si le hash doit être mis à jour</returns>
        public static bool NeedsRehash(string hashedPassword, int targetCost = 12)
        {
            if (string.IsNullOrEmpty(hashedPassword))
                return true;

            try
            {
                // Extrait le coût du hash existant
                var parts = hashedPassword.Split('$');
                if (parts.Length >= 3 && int.TryParse(parts[2], out int currentCost))
                {
                    return currentCost < targetCost;
                }
                return true;
            }
            catch
            {
                return true;
            }
        }

        /// <summary>
        /// Génère un mot de passe temporaire sécurisé
        /// </summary>
        /// <param name="length">Longueur du mot de passe (minimum 8)</param>
        /// <returns>Mot de passe temporaire</returns>
        public static string GenerateTemporaryPassword(int length = 12)
        {
            if (length < 8)
                length = 8;

            const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*";
            var random = new Random();
            var password = new char[length];

            // S'assurer qu'il y a au moins un caractère de chaque type
            password[0] = "ABCDEFGHIJKLMNOPQRSTUVWXYZ"[random.Next(26)]; // Majuscule
            password[1] = "abcdefghijklmnopqrstuvwxyz"[random.Next(26)]; // Minuscule
            password[2] = "0123456789"[random.Next(10)]; // Chiffre
            password[3] = "!@#$%^&*"[random.Next(8)]; // Caractère spécial

            // Remplir le reste aléatoirement
            for (int i = 4; i < length; i++)
            {
                password[i] = chars[random.Next(chars.Length)];
            }

            // Mélanger le tableau pour éviter un pattern prévisible
            for (int i = 0; i < length; i++)
            {
                int j = random.Next(length);
                (password[i], password[j]) = (password[j], password[i]);
            }

            return new string(password);
        }

        /// <summary>
        /// Valide la force d'un mot de passe
        /// </summary>
        /// <param name="password">Mot de passe à valider</param>
        /// <returns>Résultat de la validation</returns>
        public static PasswordValidationResult ValidatePasswordStrength(string password)
        {
            var result = new PasswordValidationResult();

            if (string.IsNullOrEmpty(password))
            {
                result.IsValid = false;
                result.Errors.Add("Le mot de passe ne peut pas être vide");
                return result;
            }

            // Longueur minimum
            if (password.Length < 8)
            {
                result.IsValid = false;
                result.Errors.Add("Le mot de passe doit contenir au moins 8 caractères");
            }

            // Au moins une majuscule
            if (!System.Text.RegularExpressions.Regex.IsMatch(password, @"[A-Z]"))
            {
                result.IsValid = false;
                result.Errors.Add("Le mot de passe doit contenir au moins une majuscule");
            }

            // Au moins une minuscule
            if (!System.Text.RegularExpressions.Regex.IsMatch(password, @"[a-z]"))
            {
                result.IsValid = false;
                result.Errors.Add("Le mot de passe doit contenir au moins une minuscule");
            }

            // Au moins un chiffre
            if (!System.Text.RegularExpressions.Regex.IsMatch(password, @"[0-9]"))
            {
                result.IsValid = false;
                result.Errors.Add("Le mot de passe doit contenir au moins un chiffre");
            }

            // Calculer le score de force
            result.Strength = CalculatePasswordStrength(password);

            return result;
        }

        private static PasswordStrength CalculatePasswordStrength(string password)
        {
            int score = 0;

            // Longueur
            if (password.Length >= 8) score += 1;
            if (password.Length >= 12) score += 1;
            if (password.Length >= 16) score += 1;

            // Complexité
            if (System.Text.RegularExpressions.Regex.IsMatch(password, @"[a-z]")) score += 1;
            if (System.Text.RegularExpressions.Regex.IsMatch(password, @"[A-Z]")) score += 1;
            if (System.Text.RegularExpressions.Regex.IsMatch(password, @"[0-9]")) score += 1;
            if (System.Text.RegularExpressions.Regex.IsMatch(password, @"[!@#$%^&*()_+\-=\[\]{};':""\\|,.<>\/?]")) score += 1;

            // Diversité des caractères
            var uniqueChars = new System.Collections.Generic.HashSet<char>(password);
            if (uniqueChars.Count >= password.Length * 0.7) score += 1;

            if (score <= 3)
                return PasswordStrength.Weak;
            else if (score <= 5)
                return PasswordStrength.Medium;
            else if (score <= 7)
                return PasswordStrength.Strong;
            else
                return PasswordStrength.VeryStrong;
        }
    }

    /// <summary>
    /// Résultat de la validation d'un mot de passe
    /// </summary>
    public class PasswordValidationResult
    {
        public bool IsValid { get; set; } = true;
        public System.Collections.Generic.List<string> Errors { get; set; } = new System.Collections.Generic.List<string>();
        public PasswordStrength Strength { get; set; } = PasswordStrength.Weak;
    }

    /// <summary>
    /// Niveaux de force d'un mot de passe
    /// </summary>
    public enum PasswordStrength
    {
        Weak,
        Medium,
        Strong,
        VeryStrong
    }
}
