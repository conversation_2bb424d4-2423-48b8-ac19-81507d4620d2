using System;

namespace GestionDeStock2024.UTILS
{
    /// <summary>
    /// Calculateur de timbre fiscal selon la réglementation algérienne
    /// </summary>
    public static class TimbreFiscalCalculator
    {
        // Seuils définis par la réglementation algérienne
        private const decimal SEUIL_MINIMUM = 300.00m;
        private const decimal SEUIL_MAXIMUM_TRANCHE_1 = 30000.00m;
        private const decimal SEUIL_MAXIMUM_TRANCHE_2 = 100000.00m;
        
        // Montants de timbre
        private const decimal TIMBRE_TRANCHE_1 = 1.00m;
        private const decimal TIMBRE_TRANCHE_2_PAR_100DA = 1.50m;
        private const decimal TRANCHE_CALCUL = 100.00m;

        /// <summary>
        /// Calcule le timbre fiscal selon la réglementation algérienne
        /// </summary>
        /// <param name="montant">Montant en dinars algériens</param>
        /// <returns>Montant du timbre fiscal</returns>
        public static decimal CalculerTimbreFiscal(decimal montant)
        {
            if (montant <= 0)
                return 0;

            // Règle 1: ≤ 300 DA = 0 DA de timbre
            if (montant <= SEUIL_MINIMUM)
                return 0;

            // Règle 2: 300 DA < montant ≤ 30 000 DA = 1 DA de timbre
            if (montant <= SEUIL_MAXIMUM_TRANCHE_1)
                return TIMBRE_TRANCHE_1;

            // Règle 3: 30 000 DA < montant ≤ 100 000 DA = 1,5 DA par tranche de 100 DA
            if (montant <= SEUIL_MAXIMUM_TRANCHE_2)
            {
                var montantAuDelaDe30000 = montant - SEUIL_MAXIMUM_TRANCHE_1;
                var nombreTranches = Math.Ceiling(montantAuDelaDe30000 / TRANCHE_CALCUL);
                return nombreTranches * TIMBRE_TRANCHE_2_PAR_100DA;
            }

            // Règle 4: > 100 000 DA = Maximum calculé sur 100 000 DA
            var montantMaximumCalcul = SEUIL_MAXIMUM_TRANCHE_2 - SEUIL_MAXIMUM_TRANCHE_1;
            var nombreTranchesMax = Math.Ceiling(montantMaximumCalcul / TRANCHE_CALCUL);
            return nombreTranchesMax * TIMBRE_TRANCHE_2_PAR_100DA;
        }

        /// <summary>
        /// Calcule le timbre fiscal avec détails du calcul
        /// </summary>
        /// <param name="montant">Montant en dinars algériens</param>
        /// <returns>Résultat détaillé du calcul</returns>
        public static TimbreFiscalResult CalculerTimbreFiscalDetaille(decimal montant)
        {
            var result = new TimbreFiscalResult
            {
                MontantOriginal = montant,
                TimbreFiscal = CalculerTimbreFiscal(montant),
                MontantTotal = montant + CalculerTimbreFiscal(montant)
            };

            // Déterminer la tranche applicable
            if (montant <= 0)
            {
                result.TrancheApplicable = "Montant invalide";
                result.ExplicationCalcul = "Le montant doit être positif";
            }
            else if (montant <= SEUIL_MINIMUM)
            {
                result.TrancheApplicable = "Tranche 1: ≤ 300 DA";
                result.ExplicationCalcul = "Aucun timbre fiscal applicable";
            }
            else if (montant <= SEUIL_MAXIMUM_TRANCHE_1)
            {
                result.TrancheApplicable = "Tranche 2: 300 DA < montant ≤ 30 000 DA";
                result.ExplicationCalcul = "Timbre fiscal fixe de 1 DA";
            }
            else if (montant <= SEUIL_MAXIMUM_TRANCHE_2)
            {
                var montantAuDelaDe30000 = montant - SEUIL_MAXIMUM_TRANCHE_1;
                var nombreTranches = Math.Ceiling(montantAuDelaDe30000 / TRANCHE_CALCUL);
                result.TrancheApplicable = "Tranche 3: 30 000 DA < montant ≤ 100 000 DA";
                result.ExplicationCalcul = $"({montant:F2} - {SEUIL_MAXIMUM_TRANCHE_1:F2}) / {TRANCHE_CALCUL:F2} = {nombreTranches} tranches × {TIMBRE_TRANCHE_2_PAR_100DA:F2} DA = {result.TimbreFiscal:F2} DA";
            }
            else
            {
                var montantMaximumCalcul = SEUIL_MAXIMUM_TRANCHE_2 - SEUIL_MAXIMUM_TRANCHE_1;
                var nombreTranchesMax = Math.Ceiling(montantMaximumCalcul / TRANCHE_CALCUL);
                result.TrancheApplicable = "Tranche 4: > 100 000 DA";
                result.ExplicationCalcul = $"Calcul plafonné sur {SEUIL_MAXIMUM_TRANCHE_2:F2} DA: {nombreTranchesMax} tranches × {TIMBRE_TRANCHE_2_PAR_100DA:F2} DA = {result.TimbreFiscal:F2} DA";
            }

            return result;
        }

        /// <summary>
        /// Vérifie si un montant nécessite un timbre fiscal
        /// </summary>
        /// <param name="montant">Montant à vérifier</param>
        /// <returns>True si un timbre fiscal est requis</returns>
        public static bool NecessiteTimbreFiscal(decimal montant)
        {
            return montant > SEUIL_MINIMUM;
        }

        /// <summary>
        /// Obtient les seuils de timbre fiscal
        /// </summary>
        /// <returns>Information sur les seuils</returns>
        public static TimbreFiscalSeuils ObtenirSeuils()
        {
            return new TimbreFiscalSeuils
            {
                SeuilMinimum = SEUIL_MINIMUM,
                SeuilMaximumTranche1 = SEUIL_MAXIMUM_TRANCHE_1,
                SeuilMaximumTranche2 = SEUIL_MAXIMUM_TRANCHE_2,
                TimbreTranche1 = TIMBRE_TRANCHE_1,
                TimbreTranche2Par100DA = TIMBRE_TRANCHE_2_PAR_100DA,
                TrancheCalcul = TRANCHE_CALCUL
            };
        }

        /// <summary>
        /// Calcule le montant HT maximum pour un timbre fiscal donné
        /// </summary>
        /// <param name="timbreFiscal">Montant du timbre fiscal</param>
        /// <returns>Montant HT maximum correspondant</returns>
        public static decimal CalculerMontantMaximumPourTimbre(decimal timbreFiscal)
        {
            if (timbreFiscal <= 0)
                return SEUIL_MINIMUM;

            if (timbreFiscal <= TIMBRE_TRANCHE_1)
                return SEUIL_MAXIMUM_TRANCHE_1;

            // Calcul inverse pour la tranche 3
            var nombreTranches = timbreFiscal / TIMBRE_TRANCHE_2_PAR_100DA;
            var montantAuDelaDe30000 = nombreTranches * TRANCHE_CALCUL;
            var montantTotal = SEUIL_MAXIMUM_TRANCHE_1 + montantAuDelaDe30000;

            return Math.Min(montantTotal, SEUIL_MAXIMUM_TRANCHE_2);
        }
    }

    /// <summary>
    /// Résultat détaillé du calcul de timbre fiscal
    /// </summary>
    public class TimbreFiscalResult
    {
        public decimal MontantOriginal { get; set; }
        public decimal TimbreFiscal { get; set; }
        public decimal MontantTotal { get; set; }
        public string TrancheApplicable { get; set; }
        public string ExplicationCalcul { get; set; }
        public DateTime DateCalcul { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// Information sur les seuils de timbre fiscal
    /// </summary>
    public class TimbreFiscalSeuils
    {
        public decimal SeuilMinimum { get; set; }
        public decimal SeuilMaximumTranche1 { get; set; }
        public decimal SeuilMaximumTranche2 { get; set; }
        public decimal TimbreTranche1 { get; set; }
        public decimal TimbreTranche2Par100DA { get; set; }
        public decimal TrancheCalcul { get; set; }
    }

    /// <summary>
    /// Utilitaires pour les calculs de TVA algérienne
    /// </summary>
    public static class TVACalculator
    {
        // Taux de TVA algériens
        public const decimal TVA_NORMALE = 19.00m;
        public const decimal TVA_REDUITE = 9.00m;
        public const decimal TVA_NULLE = 0.00m;

        /// <summary>
        /// Calcule le montant HT à partir du TTC
        /// </summary>
        /// <param name="montantTTC">Montant TTC</param>
        /// <param name="tauxTVA">Taux de TVA en pourcentage</param>
        /// <returns>Montant HT</returns>
        public static decimal CalculerMontantHT(decimal montantTTC, decimal tauxTVA)
        {
            if (tauxTVA <= 0)
                return montantTTC;

            return montantTTC / (1 + tauxTVA / 100);
        }

        /// <summary>
        /// Calcule le montant TTC à partir du HT
        /// </summary>
        /// <param name="montantHT">Montant HT</param>
        /// <param name="tauxTVA">Taux de TVA en pourcentage</param>
        /// <returns>Montant TTC</returns>
        public static decimal CalculerMontantTTC(decimal montantHT, decimal tauxTVA)
        {
            return montantHT * (1 + tauxTVA / 100);
        }

        /// <summary>
        /// Calcule le montant de TVA
        /// </summary>
        /// <param name="montantHT">Montant HT</param>
        /// <param name="tauxTVA">Taux de TVA en pourcentage</param>
        /// <returns>Montant de TVA</returns>
        public static decimal CalculerMontantTVA(decimal montantHT, decimal tauxTVA)
        {
            return montantHT * (tauxTVA / 100);
        }

        /// <summary>
        /// Calcul détaillé de TVA
        /// </summary>
        /// <param name="montantHT">Montant HT</param>
        /// <param name="tauxTVA">Taux de TVA</param>
        /// <returns>Résultat détaillé</returns>
        public static TVAResult CalculerTVADetaille(decimal montantHT, decimal tauxTVA)
        {
            var montantTVA = CalculerMontantTVA(montantHT, tauxTVA);
            var montantTTC = montantHT + montantTVA;

            return new TVAResult
            {
                MontantHT = montantHT,
                TauxTVA = tauxTVA,
                MontantTVA = montantTVA,
                MontantTTC = montantTTC,
                TypeTVA = DeterminerTypeTVA(tauxTVA)
            };
        }

        /// <summary>
        /// Détermine le type de TVA selon le taux
        /// </summary>
        /// <param name="tauxTVA">Taux de TVA</param>
        /// <returns>Type de TVA</returns>
        public static string DeterminerTypeTVA(decimal tauxTVA)
        {
            if (tauxTVA == TVA_NULLE)
                return "TVA nulle";
            else if (tauxTVA == TVA_REDUITE)
                return "TVA réduite (9%)";
            else if (tauxTVA == TVA_NORMALE)
                return "TVA normale (19%)";
            else
                return $"TVA personnalisée ({tauxTVA}%)";
        }
    }

    /// <summary>
    /// Résultat du calcul de TVA
    /// </summary>
    public class TVAResult
    {
        public decimal MontantHT { get; set; }
        public decimal TauxTVA { get; set; }
        public decimal MontantTVA { get; set; }
        public decimal MontantTTC { get; set; }
        public string TypeTVA { get; set; }
    }
}
