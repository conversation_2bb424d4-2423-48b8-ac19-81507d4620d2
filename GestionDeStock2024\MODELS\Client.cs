using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using static GestionDeStock2024.MODELS.Enums;

namespace GestionDeStock2024.MODELS
{
    /// <summary>
    /// Modèle pour les clients
    /// </summary>
    public class Client : BaseEntity, IStatutEntity, ICodeEntity
    {
        [Required(ErrorMessage = "Le code client est obligatoire")]
        [StringLength(50)]
        public string Code { get; set; }

        [Required(ErrorMessage = "Le nom du client est obligatoire")]
        [StringLength(255)]
        public string NomClient { get; set; }

        [StringLength(255)]
        public string PrenomClient { get; set; }

        /// <summary>
        /// Pour les entreprises clientes
        /// </summary>
        [StringLength(255)]
        public string RaisonSociale { get; set; }

        public string TypeClient { get; set; } = TypeClient.Particulier.ToString();

        public string Adresse { get; set; }

        [StringLength(100)]
        public string Ville { get; set; }

        [StringLength(100)]
        public string Wilaya { get; set; }

        [StringLength(10)]
        public string CodePostal { get; set; }

        [StringLength(20)]
        [Phone(ErrorMessage = "Format de téléphone invalide")]
        public string Telephone { get; set; }

        [StringLength(100)]
        [EmailAddress(ErrorMessage = "Format d'email invalide")]
        public string Email { get; set; }

        /// <summary>
        /// Numéro d'Identification Fiscale (pour les entreprises)
        /// </summary>
        [StringLength(20)]
        public string NIF { get; set; }

        /// <summary>
        /// Numéro d'Identification Statistique (pour les entreprises)
        /// </summary>
        [StringLength(20)]
        public string NIS { get; set; }

        /// <summary>
        /// Registre de Commerce (pour les entreprises)
        /// </summary>
        [StringLength(20)]
        public string RC { get; set; }

        /// <summary>
        /// Article d'Imposition (pour les entreprises)
        /// </summary>
        [StringLength(20)]
        public string ART { get; set; }

        public decimal SoldeInitial { get; set; } = 0;

        public decimal SoldeActuel { get; set; } = 0;

        [Range(0, double.MaxValue, ErrorMessage = "La limite de crédit doit être positive")]
        public decimal CreditLimite { get; set; } = 0;

        [Range(0, 100, ErrorMessage = "La remise doit être entre 0 et 100%")]
        public decimal RemiseHabituelle { get; set; } = 0;

        public DateTime? DateNaissance { get; set; }

        public string Statut { get; set; } = StatutGeneral.Actif.ToString();

        // Navigation
        public virtual ICollection<FactureVente> FacturesVente { get; set; }
        public virtual ICollection<RetourClient> RetoursClient { get; set; }

        // Propriétés calculées
        public string NomComplet => string.IsNullOrEmpty(PrenomClient) ? 
            NomClient : $"{NomClient} {PrenomClient}";

        public string NomAffichage => TypeClient == TypeClient.Entreprise.ToString() && 
                                     !string.IsNullOrEmpty(RaisonSociale) ? 
                                     RaisonSociale : NomComplet;

        public bool EstEntreprise => TypeClient == TypeClient.Entreprise.ToString();

        public bool EstRevendeur => TypeClient == TypeClient.Revendeur.ToString();

        public bool EstParticulier => TypeClient == TypeClient.Particulier.ToString();

        public bool ACredit => SoldeActuel > 0;

        public bool CreditDepasse => SoldeActuel > CreditLimite && CreditLimite > 0;

        public decimal CreditDisponible => Math.Max(0, CreditLimite - SoldeActuel);

        public string AdresseComplete => $"{Adresse}, {Ville}, {Wilaya} {CodePostal}".Trim(' ', ',');

        public bool EstValideEntreprise => EstEntreprise && 
                                          !string.IsNullOrEmpty(NIF) && 
                                          !string.IsNullOrEmpty(RaisonSociale);

        public int? Age
        {
            get
            {
                if (!DateNaissance.HasValue) return null;
                var today = DateTime.Today;
                var age = today.Year - DateNaissance.Value.Year;
                if (DateNaissance.Value.Date > today.AddYears(-age)) age--;
                return age;
            }
        }

        /// <summary>
        /// Vérifie si le client peut effectuer un achat à crédit
        /// </summary>
        public bool PeutAcheterACredit(decimal montant)
        {
            if (CreditLimite <= 0) return false;
            return (SoldeActuel + montant) <= CreditLimite;
        }

        /// <summary>
        /// Calcule la remise applicable pour un montant donné
        /// </summary>
        public decimal CalculerRemise(decimal montant)
        {
            if (RemiseHabituelle <= 0) return 0;
            return montant * (RemiseHabituelle / 100);
        }
    }

    /// <summary>
    /// Modèle pour les fournisseurs
    /// </summary>
    public class Fournisseur : BaseEntity, IStatutEntity, ICodeEntity
    {
        [Required(ErrorMessage = "Le code fournisseur est obligatoire")]
        [StringLength(50)]
        public string Code { get; set; }

        [Required(ErrorMessage = "Le nom du fournisseur est obligatoire")]
        [StringLength(255)]
        public string NomFournisseur { get; set; }

        [StringLength(255)]
        public string RaisonSociale { get; set; }

        public string Adresse { get; set; }

        [StringLength(100)]
        public string Ville { get; set; }

        [StringLength(100)]
        public string Wilaya { get; set; }

        [StringLength(10)]
        public string CodePostal { get; set; }

        [StringLength(20)]
        [Phone(ErrorMessage = "Format de téléphone invalide")]
        public string Telephone { get; set; }

        [StringLength(20)]
        public string Fax { get; set; }

        [StringLength(100)]
        [EmailAddress(ErrorMessage = "Format d'email invalide")]
        public string Email { get; set; }

        [StringLength(255)]
        public string SiteWeb { get; set; }

        /// <summary>
        /// Numéro d'Identification Fiscale
        /// </summary>
        [StringLength(20)]
        public string NIF { get; set; }

        /// <summary>
        /// Numéro d'Identification Statistique
        /// </summary>
        [StringLength(20)]
        public string NIS { get; set; }

        /// <summary>
        /// Registre de Commerce
        /// </summary>
        [StringLength(20)]
        public string RC { get; set; }

        /// <summary>
        /// Article d'Imposition
        /// </summary>
        [StringLength(20)]
        public string ART { get; set; }

        public decimal SoldeInitial { get; set; } = 0;

        public decimal SoldeActuel { get; set; } = 0;

        [Range(0, double.MaxValue, ErrorMessage = "La limite de crédit doit être positive")]
        public decimal CreditLimite { get; set; } = 0;

        [Range(1, 365, ErrorMessage = "Le délai de paiement doit être entre 1 et 365 jours")]
        public int DelaiPaiement { get; set; } = 30;

        [Range(0, 100, ErrorMessage = "La remise doit être entre 0 et 100%")]
        public decimal RemiseHabituelle { get; set; } = 0;

        [StringLength(255)]
        public string ContactPrincipal { get; set; }

        [StringLength(20)]
        [Phone(ErrorMessage = "Format de téléphone invalide")]
        public string TelephoneContact { get; set; }

        public string Statut { get; set; } = StatutGeneral.Actif.ToString();

        // Navigation
        public virtual ICollection<FactureAchat> FacturesAchat { get; set; }
        public virtual ICollection<RetourFournisseur> RetoursFournisseur { get; set; }

        // Propriétés calculées
        public string NomAffichage => !string.IsNullOrEmpty(RaisonSociale) ? 
                                     RaisonSociale : NomFournisseur;

        public bool ADette => SoldeActuel > 0;

        public decimal CreditDisponible => Math.Max(0, CreditLimite - SoldeActuel);

        public string AdresseComplete => $"{Adresse}, {Ville}, {Wilaya} {CodePostal}".Trim(' ', ',');

        public bool EstValide => !string.IsNullOrEmpty(NomFournisseur) && 
                                !string.IsNullOrEmpty(NIF) && 
                                !string.IsNullOrEmpty(NIS);

        /// <summary>
        /// Calcule la date d'échéance pour un achat
        /// </summary>
        public DateTime CalculerDateEcheance(DateTime dateAchat)
        {
            return dateAchat.AddDays(DelaiPaiement);
        }

        /// <summary>
        /// Vérifie si le fournisseur peut accorder un crédit
        /// </summary>
        public bool PeutAccorderCredit(decimal montant)
        {
            if (CreditLimite <= 0) return false;
            return (SoldeActuel + montant) <= CreditLimite;
        }
    }
}
