using System;
using System.Threading.Tasks;
using MySqlConnector;
using GestionDeStock2024.UTILS;

namespace GestionDeStock2024.DATA
{
    /// <summary>
    /// Factory pour créer des connexions à la base de données
    /// </summary>
    public static class DatabaseConnectionFactory
    {
        /// <summary>
        /// Crée une nouvelle connexion à la base de données
        /// </summary>
        /// <returns>Connexion MySQL ouverte</returns>
        public static async Task<MySqlConnection> CreateAsync()
        {
            var connectionString = ConfigurationManager.ConnectionString;
            
            if (string.IsNullOrEmpty(connectionString))
            {
                throw new InvalidOperationException("Chaîne de connexion non configurée");
            }

            var connection = new MySqlConnection(connectionString);
            await connection.OpenAsync();
            
            return connection;
        }

        /// <summary>
        /// Crée une nouvelle connexion synchrone à la base de données
        /// </summary>
        /// <returns>Connexion MySQL ouverte</returns>
        public static MySqlConnection Create()
        {
            var connectionString = ConfigurationManager.ConnectionString;
            
            if (string.IsNullOrEmpty(connectionString))
            {
                throw new InvalidOperationException("Chaîne de connexion non configurée");
            }

            var connection = new MySqlConnection(connectionString);
            connection.Open();
            
            return connection;
        }

        /// <summary>
        /// Teste la connexion à la base de données
        /// </summary>
        /// <returns>True si la connexion est possible</returns>
        public static async Task<bool> TestConnectionAsync()
        {
            try
            {
                using var connection = await CreateAsync();
                return connection.State == System.Data.ConnectionState.Open;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Teste la connexion à la base de données de manière synchrone
        /// </summary>
        /// <returns>True si la connexion est possible</returns>
        public static bool TestConnection()
        {
            try
            {
                using var connection = Create();
                return connection.State == System.Data.ConnectionState.Open;
            }
            catch
            {
                return false;
            }
        }
    }
}
