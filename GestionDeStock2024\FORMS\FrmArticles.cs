using System;
using System.Drawing;
using System.Windows.Forms;
using GestionDeStock2024.MODELS;

namespace GestionDeStock2024.FORMS
{
    /// <summary>
    /// Formulaire de gestion des articles
    /// </summary>
    public partial class FrmArticles : Form
    {
        private Utilisateur currentUser;
        private DataGridView dgvArticles;
        private TextBox txtSearch;
        private Button btnAdd, btnEdit, btnDelete, btnRefresh;
        private ComboBox cmbCategory, cmbStatus;

        public FrmArticles()
        {
            InitializeComponent();
            InitializeForm();
        }

        public FrmArticles(Utilisateur user) : this()
        {
            currentUser = user;
            LoadArticles();
        }

        private void InitializeForm()
        {
            this.Text = "Gestion des Articles";
            this.BackColor = Color.White;
            this.Size = new Size(1200, 800);
            this.WindowState = FormWindowState.Maximized;

            CreateLayout();
            ApplyModernTheme();
        }

        private void CreateLayout()
        {
            // Panel principal
            var mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20),
                BackColor = Color.FromArgb(248, 249, 250)
            };
            this.Controls.Add(mainPanel);

            // Titre
            var titleLabel = new Label
            {
                Text = "📦 Gestion des Articles",
                Font = new Font("Segoe UI", 24, FontStyle.Bold),
                ForeColor = Color.FromArgb(33, 37, 41),
                AutoSize = true,
                Location = new Point(0, 0)
            };
            mainPanel.Controls.Add(titleLabel);

            // Panel des filtres et boutons
            var toolbarPanel = new Panel
            {
                Location = new Point(0, 60),
                Size = new Size(mainPanel.Width - 40, 60),
                Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right,
                BackColor = Color.White
            };
            mainPanel.Controls.Add(toolbarPanel);

            CreateToolbar(toolbarPanel);

            // DataGridView
            dgvArticles = new DataGridView
            {
                Location = new Point(0, 140),
                Size = new Size(mainPanel.Width - 40, mainPanel.Height - 180),
                Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
            };
            mainPanel.Controls.Add(dgvArticles);

            SetupDataGridView();
        }

        private void CreateToolbar(Panel parent)
        {
            // Recherche
            var lblSearch = new Label
            {
                Text = "🔍 Rechercher:",
                Font = new Font("Segoe UI", 10, FontStyle.Bold),
                ForeColor = Color.FromArgb(73, 80, 87),
                Location = new Point(10, 20),
                AutoSize = true
            };
            parent.Controls.Add(lblSearch);

            txtSearch = new TextBox
            {
                Location = new Point(110, 17),
                Size = new Size(200, 25),
                Font = new Font("Segoe UI", 10),
            };
            txtSearch.TextChanged += TxtSearch_TextChanged;
            parent.Controls.Add(txtSearch);

            // Catégorie
            var lblCategory = new Label
            {
                Text = "Catégorie:",
                Font = new Font("Segoe UI", 10),
                ForeColor = Color.FromArgb(73, 80, 87),
                Location = new Point(330, 20),
                AutoSize = true
            };
            parent.Controls.Add(lblCategory);

            cmbCategory = new ComboBox
            {
                Location = new Point(400, 17),
                Size = new Size(150, 25),
                Font = new Font("Segoe UI", 10),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            cmbCategory.Items.AddRange(new[] { "Toutes", "Alimentaire", "Boissons", "Hygiène", "Électronique" });
            cmbCategory.SelectedIndex = 0;
            cmbCategory.SelectedIndexChanged += CmbCategory_SelectedIndexChanged;
            parent.Controls.Add(cmbCategory);

            // Statut
            var lblStatus = new Label
            {
                Text = "Statut:",
                Font = new Font("Segoe UI", 10),
                ForeColor = Color.FromArgb(73, 80, 87),
                Location = new Point(570, 20),
                AutoSize = true
            };
            parent.Controls.Add(lblStatus);

            cmbStatus = new ComboBox
            {
                Location = new Point(620, 17),
                Size = new Size(100, 25),
                Font = new Font("Segoe UI", 10),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            cmbStatus.Items.AddRange(new[] { "Tous", "Actif", "Inactif" });
            cmbStatus.SelectedIndex = 0;
            cmbStatus.SelectedIndexChanged += CmbStatus_SelectedIndexChanged;
            parent.Controls.Add(cmbStatus);

            // Boutons
            btnAdd = CreateButton("➕ Ajouter", new Point(750, 15), Color.FromArgb(40, 167, 69));
            btnAdd.Click += BtnAdd_Click;
            parent.Controls.Add(btnAdd);

            btnEdit = CreateButton("✏️ Modifier", new Point(860, 15), Color.FromArgb(0, 123, 255));
            btnEdit.Click += BtnEdit_Click;
            parent.Controls.Add(btnEdit);

            btnDelete = CreateButton("🗑️ Supprimer", new Point(970, 15), Color.FromArgb(220, 53, 69));
            btnDelete.Click += BtnDelete_Click;
            parent.Controls.Add(btnDelete);

            btnRefresh = CreateButton("🔄", new Point(1090, 15), Color.FromArgb(108, 117, 125));
            btnRefresh.Size = new Size(35, 30);
            btnRefresh.Click += BtnRefresh_Click;
            parent.Controls.Add(btnRefresh);
        }

        private Button CreateButton(string text, Point location, Color backColor)
        {
            return new Button
            {
                Text = text,
                Location = location,
                Size = new Size(100, 30),
                Font = new Font("Segoe UI", 9, FontStyle.Bold),
                BackColor = backColor,
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                FlatAppearance = { BorderSize = 0 },
                Cursor = Cursors.Hand
            };
        }

        private void SetupDataGridView()
        {
            dgvArticles.Columns.Clear();

            // Configuration des colonnes
            dgvArticles.Columns.Add("Code", "Code");
            dgvArticles.Columns.Add("Nom", "Nom de l'article");
            dgvArticles.Columns.Add("CodeBarre", "Code-barres");
            dgvArticles.Columns.Add("Categorie", "Catégorie");
            dgvArticles.Columns.Add("PrixAchat", "Prix d'achat");
            dgvArticles.Columns.Add("PrixVente", "Prix de vente");
            dgvArticles.Columns.Add("Stock", "Stock");
            dgvArticles.Columns.Add("StockMin", "Stock min.");
            dgvArticles.Columns.Add("Statut", "Statut");

            // Style des colonnes
            dgvArticles.Columns["Code"].Width = 80;
            dgvArticles.Columns["Nom"].Width = 200;
            dgvArticles.Columns["CodeBarre"].Width = 120;
            dgvArticles.Columns["Categorie"].Width = 100;
            dgvArticles.Columns["PrixAchat"].Width = 100;
            dgvArticles.Columns["PrixVente"].Width = 100;
            dgvArticles.Columns["Stock"].Width = 80;
            dgvArticles.Columns["StockMin"].Width = 80;
            dgvArticles.Columns["Statut"].Width = 80;

            // Alignement des colonnes numériques
            dgvArticles.Columns["PrixAchat"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
            dgvArticles.Columns["PrixVente"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
            dgvArticles.Columns["Stock"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dgvArticles.Columns["StockMin"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;

            // Format des prix
            dgvArticles.Columns["PrixAchat"].DefaultCellStyle.Format = "N2";
            dgvArticles.Columns["PrixVente"].DefaultCellStyle.Format = "N2";
        }

        private void ApplyModernTheme()
        {
            // Style du DataGridView
            dgvArticles.EnableHeadersVisualStyles = false;
            dgvArticles.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(52, 58, 64);
            dgvArticles.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvArticles.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 10, FontStyle.Bold);
            dgvArticles.ColumnHeadersHeight = 40;

            dgvArticles.DefaultCellStyle.BackColor = Color.White;
            dgvArticles.DefaultCellStyle.ForeColor = Color.FromArgb(33, 37, 41);
            dgvArticles.DefaultCellStyle.Font = new Font("Segoe UI", 9);
            dgvArticles.DefaultCellStyle.SelectionBackColor = Color.FromArgb(0, 123, 255);
            dgvArticles.DefaultCellStyle.SelectionForeColor = Color.White;

            dgvArticles.AlternatingRowsDefaultCellStyle.BackColor = Color.FromArgb(248, 249, 250);
            dgvArticles.GridColor = Color.FromArgb(222, 226, 230);
            dgvArticles.RowHeadersVisible = false;
            dgvArticles.CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal;
        }

        private void LoadArticles()
        {
            // Données fictives pour la démonstration
            dgvArticles.Rows.Clear();
            
            var sampleData = new[]
            {
                new object[] { "ART001", "Coca Cola 1.5L", "1234567890123", "Boissons", 120.00, 150.00, 45, 10, "Actif" },
                new object[] { "ART002", "Pain de mie", "2345678901234", "Alimentaire", 80.00, 100.00, 23, 5, "Actif" },
                new object[] { "ART003", "Shampoing Head & Shoulders", "3456789012345", "Hygiène", 450.00, 550.00, 12, 8, "Actif" },
                new object[] { "ART004", "Lait UHT 1L", "4567890123456", "Alimentaire", 90.00, 110.00, 67, 15, "Actif" },
                new object[] { "ART005", "Biscuits Oreo", "5678901234567", "Alimentaire", 180.00, 220.00, 34, 10, "Actif" }
            };

            foreach (var row in sampleData)
            {
                dgvArticles.Rows.Add(row);
            }

            // Colorer les lignes selon le stock
            foreach (DataGridViewRow row in dgvArticles.Rows)
            {
                var stock = Convert.ToInt32(row.Cells["Stock"].Value);
                var stockMin = Convert.ToInt32(row.Cells["StockMin"].Value);

                if (stock <= stockMin)
                {
                    row.DefaultCellStyle.BackColor = Color.FromArgb(255, 243, 205);
                    row.DefaultCellStyle.ForeColor = Color.FromArgb(133, 100, 4);
                }
                else if (stock <= stockMin * 2)
                {
                    row.DefaultCellStyle.BackColor = Color.FromArgb(255, 248, 220);
                }
            }
        }

        #region Event Handlers

        private void TxtSearch_TextChanged(object sender, EventArgs e)
        {
            // Implémenter la recherche
            FilterArticles();
        }

        private void CmbCategory_SelectedIndexChanged(object sender, EventArgs e)
        {
            FilterArticles();
        }

        private void CmbStatus_SelectedIndexChanged(object sender, EventArgs e)
        {
            FilterArticles();
        }

        private void FilterArticles()
        {
            // Ici, vous implémenteriez le filtrage réel
            // Pour le moment, on recharge simplement les données
            LoadArticles();
        }

        private void BtnAdd_Click(object sender, EventArgs e)
        {
            MessageBox.Show("Fonctionnalité d'ajout d'article à implémenter", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void BtnEdit_Click(object sender, EventArgs e)
        {
            if (dgvArticles.SelectedRows.Count > 0)
            {
                MessageBox.Show("Fonctionnalité de modification d'article à implémenter", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            else
            {
                MessageBox.Show("Veuillez sélectionner un article à modifier", "Attention", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private void BtnDelete_Click(object sender, EventArgs e)
        {
            if (dgvArticles.SelectedRows.Count > 0)
            {
                var result = MessageBox.Show("Êtes-vous sûr de vouloir supprimer cet article ?", "Confirmation", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                if (result == DialogResult.Yes)
                {
                    MessageBox.Show("Fonctionnalité de suppression d'article à implémenter", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            else
            {
                MessageBox.Show("Veuillez sélectionner un article à supprimer", "Attention", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private void BtnRefresh_Click(object sender, EventArgs e)
        {
            LoadArticles();
        }

        #endregion

        private void FrmArticles_Load(object sender, EventArgs e)
        {
            LoadArticles();
        }
    }
}
