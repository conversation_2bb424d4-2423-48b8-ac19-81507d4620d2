<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <startup>
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.6.2" />
  </startup>
  <connectionStrings>
    <add name="DefaultConnection" connectionString="Server=localhost;Database=gestion_supermarche_dz;Uid=root;Pwd=;Charset=utf8mb4;" providerName="MySql.Data.MySqlClient" />
  </connectionStrings>
  <appSettings>
    <add key="TVA_Normale" value="19.00" />
    <add key="TVA_Reduite" value="9.00" />
    <add key="Timbre_Seuil_Min" value="300.00" />
    <add key="Timbre_Seuil_Max" value="30000.00" />
    <add key="Timbre_Taux_1" value="1.00" />
    <add key="Timbre_Taux_2" value="1.50" />
    <add key="Devise_Defaut" value="DA" />
    <add key="Langue_Defaut" value="fr" />
    <add key="Format_Date" value="dd/MM/yyyy" />
    <add key="Format_Heure" value="HH:mm" />
    <add key="Precision_Decimale" value="2" />
    <add key="Taille_Page_Defaut" value="20" />
    <add key="Theme_Defaut" value="Default" />
    <add key="Sauvegarde_Auto" value="true" />
    <add key="Frequence_Sauvegarde" value="7" />
    <add key="Alertes_Stock_Actives" value="true" />
    <add key="Timeout_Session" value="30" />
    <add key="Logging_Active" value="true" />
    <add key="Niveau_Log" value="Info" />
    <add key="Chemin_Logs" value=".\Logs" />
    <add key="Imprimante_Defaut" value="" />
    <add key="Format_Papier" value="A4" />
    <add key="Imprimer_Logo" value="true" />
    <add key="Imprimer_CodeBarre" value="true" />
    <add key="Imprimer_QRCode" value="false" />
    <add key="Nombre_Copies" value="1" />
    <add key="Longueur_MotDePasse_Min" value="6" />
    <add key="MotDePasse_Complexe" value="false" />
    <add key="Tentatives_Connexion_Max" value="3" />
    <add key="Duree_Verrouillage" value="15" />
    <add key="Chiffrement_Donnees" value="false" />
    <add key="Cle_Chiffrement" value="" />
    <add key="ClientSettingsProvider.ServiceUri" value="" />
    <add key="ConnectionString" value="Server=127.0.0.1;Port=3306;User ID=root;Password=********;Database=gestion_supermarche_dz;Character Set=utf8mb4" />
  </appSettings>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-6.0.0.0" newVersion="6.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Bcl.AsyncInterfaces" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-9.0.0.1" newVersion="9.0.0.1" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="MySql.Data" publicKeyToken="c5687fc88969c44d" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-********" newVersion="********" />
      </dependentAssembly>
    </assemblyBinding>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="System.ValueTuple" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
  <system.web>
    <membership defaultProvider="ClientAuthenticationMembershipProvider">
      <providers>
        <add name="ClientAuthenticationMembershipProvider" type="System.Web.ClientServices.Providers.ClientFormsAuthenticationMembershipProvider, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" serviceUri="" />
      </providers>
    </membership>
    <roleManager defaultProvider="ClientRoleProvider" enabled="true">
      <providers>
        <add name="ClientRoleProvider" type="System.Web.ClientServices.Providers.ClientRoleProvider, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" serviceUri="" cacheTimeout="86400" />
      </providers>
    </roleManager>
  </system.web>
</configuration>