using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using static GestionDeStock2024.MODELS.Enums;

namespace GestionDeStock2024.MODELS
{
    /// <summary>
    /// Modèle pour les catégories d'articles
    /// </summary>
    public class Categorie : BaseEntity, IStatutEntity, ICodeEntity
    {
        [Required(ErrorMessage = "Le nom de la catégorie est obligatoire")]
        [StringLength(255)]
        public string NomCategorie { get; set; }

        public string Description { get; set; }

        public int? CategorieParentId { get; set; }

        [StringLength(20)]
        public string Code { get; set; }

        [Range(0, 100, ErrorMessage = "La TVA doit être entre 0 et 100%")]
        public decimal TvaApplicable { get; set; } = 19.00m;

        public string Statut { get; set; } = StatutGeneral.Actif.ToString();

        // Navigation
        public virtual Categorie CategorieParent { get; set; }
        public virtual ICollection<Categorie> SousCategories { get; set; }
        public virtual ICollection<Article> Articles { get; set; }

        // Propriétés calculées
        public bool EstSousCategorie => CategorieParentId.HasValue;
        public string NomComplet => EstSousCategorie && CategorieParent != null ? 
            $"{CategorieParent.NomCategorie} > {NomCategorie}" : NomCategorie;
    }

    /// <summary>
    /// Modèle pour les unités de mesure
    /// </summary>
    public class UniteMesure : BaseEntity, IStatutEntity
    {
        [Required(ErrorMessage = "Le nom de l'unité est obligatoire")]
        [StringLength(50)]
        public string NomUnite { get; set; }

        [Required(ErrorMessage = "L'abréviation est obligatoire")]
        [StringLength(10)]
        public string Abreviation { get; set; }

        [Required(ErrorMessage = "Le type d'unité est obligatoire")]
        public string TypeUnite { get; set; }

        [Range(0.0001, double.MaxValue, ErrorMessage = "Le facteur de conversion doit être positif")]
        public decimal FacteurConversion { get; set; } = 1.0000m;

        public int? UniteBaseId { get; set; }

        public string Statut { get; set; } = StatutGeneral.Actif.ToString();

        // Navigation
        public virtual UniteMesure UniteBase { get; set; }
        public virtual ICollection<Article> Articles { get; set; }
    }

    /// <summary>
    /// Modèle principal pour les articles/produits
    /// </summary>
    public class Article : BaseEntity, IStatutEntity, ICodeEntity
    {
        [Required(ErrorMessage = "Le code article est obligatoire")]
        [StringLength(50)]
        public string Code { get; set; }

        [Required(ErrorMessage = "Le nom de l'article est obligatoire")]
        [StringLength(255)]
        public string NomArticle { get; set; }

        public string Description { get; set; }

        [Required(ErrorMessage = "La catégorie est obligatoire")]
        public int CategorieId { get; set; }

        [Required(ErrorMessage = "L'unité de mesure est obligatoire")]
        public int UniteMesureId { get; set; }

        [Range(0, double.MaxValue, ErrorMessage = "Le prix d'achat doit être positif")]
        public decimal PrixAchatUnitaire { get; set; } = 0;

        [Range(0, double.MaxValue, ErrorMessage = "Le prix de vente doit être positif")]
        public decimal PrixVenteUnitaire { get; set; } = 0;

        [Range(0, double.MaxValue, ErrorMessage = "Le prix de vente en gros doit être positif")]
        public decimal PrixVenteGros { get; set; } = 0;

        [Range(0, 100, ErrorMessage = "La marge doit être entre 0 et 100%")]
        public decimal MargeBeneficiaire { get; set; } = 0;

        [Range(0, 100, ErrorMessage = "La TVA achat doit être entre 0 et 100%")]
        public decimal TvaAchat { get; set; } = 19.00m;

        [Range(0, 100, ErrorMessage = "La TVA vente doit être entre 0 et 100%")]
        public decimal TvaVente { get; set; } = 19.00m;

        [Range(0, double.MaxValue, ErrorMessage = "Le stock minimum doit être positif")]
        public decimal StockMinimum { get; set; } = 0;

        [Range(0, double.MaxValue, ErrorMessage = "Le stock maximum doit être positif")]
        public decimal StockMaximum { get; set; } = 0;

        [Range(0, double.MaxValue, ErrorMessage = "Le stock actuel doit être positif")]
        public decimal StockActuel { get; set; } = 0;

        [Range(0, double.MaxValue, ErrorMessage = "Le stock réservé doit être positif")]
        public decimal StockReserve { get; set; } = 0;

        /// <summary>
        /// Prix Moyen Pondéré
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "Le PMP doit être positif")]
        public decimal PMP { get; set; } = 0;

        [Range(1, int.MaxValue, ErrorMessage = "Le nombre de pièces par fardeau doit être au moins 1")]
        public int PiecesParFardeau { get; set; } = 1;

        [Range(0, double.MaxValue, ErrorMessage = "Le prix du fardeau doit être positif")]
        public decimal PrixFardeau { get; set; } = 0;

        public DateTime? DateExpiration { get; set; }

        [StringLength(100)]
        public string LotNumero { get; set; }

        [StringLength(100)]
        public string Emplacement { get; set; }

        public byte[] Image { get; set; }

        public string Statut { get; set; } = StatutGeneral.Actif.ToString();

        // Navigation
        public virtual Categorie Categorie { get; set; }
        public virtual UniteMesure UniteMesure { get; set; }
        public virtual ICollection<CodeBarre> CodesBarres { get; set; }
        public virtual ICollection<MouvementStock> MouvementsStock { get; set; }

        // Propriétés calculées
        public decimal StockDisponible => StockActuel - StockReserve;
        
        public bool EstEnRuptureStock => StockActuel <= 0;
        
        public bool EstStockFaible => StockActuel <= StockMinimum && StockActuel > 0;
        
        public string AlerteStock
        {
            get
            {
                if (EstEnRuptureStock) return "Rupture de stock";
                if (EstStockFaible) return "Stock faible";
                return "Stock normal";
            }
        }

        public bool EstPerime => DateExpiration.HasValue && DateExpiration.Value < DateTime.Today;
        
        public bool ExpireBientot => DateExpiration.HasValue && 
                                    DateExpiration.Value <= DateTime.Today.AddDays(30) && 
                                    !EstPerime;

        public decimal ValeurStock => StockActuel * PMP;

        public decimal PrixVenteHT => PrixVenteUnitaire / (1 + TvaVente / 100);

        public decimal MontantTvaVente => PrixVenteUnitaire - PrixVenteHT;

        /// <summary>
        /// Calcule le prix de vente basé sur la marge bénéficiaire
        /// </summary>
        public decimal CalculerPrixVenteAvecMarge()
        {
            if (MargeBeneficiaire > 0)
            {
                return PrixAchatUnitaire * (1 + MargeBeneficiaire / 100);
            }
            return PrixVenteUnitaire;
        }

        /// <summary>
        /// Calcule la marge réelle basée sur les prix actuels
        /// </summary>
        public decimal CalculerMargeReelle()
        {
            if (PrixAchatUnitaire > 0)
            {
                return ((PrixVenteUnitaire - PrixAchatUnitaire) / PrixAchatUnitaire) * 100;
            }
            return 0;
        }
    }

    /// <summary>
    /// Modèle pour les codes-barres multiples par article
    /// </summary>
    public class CodeBarre : BaseEntity, IStatutEntity
    {
        [Required(ErrorMessage = "L'article est obligatoire")]
        public int ArticleId { get; set; }

        [Required(ErrorMessage = "Le code-barre est obligatoire")]
        [StringLength(100)]
        public string CodeBarreValeur { get; set; }

        public string TypeCode { get; set; } = "EAN13";

        [Range(0.01, double.MaxValue, ErrorMessage = "La quantité unitaire doit être positive")]
        public decimal QuantiteUnitaire { get; set; } = 1;

        public decimal? PrixUnitaire { get; set; }

        public string Statut { get; set; } = StatutGeneral.Actif.ToString();

        // Navigation
        public virtual Article Article { get; set; }

        // Propriétés calculées
        public bool APrixSpecifique => PrixUnitaire.HasValue && PrixUnitaire.Value > 0;
    }
}
