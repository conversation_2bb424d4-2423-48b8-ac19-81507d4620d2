﻿using System;
using System.Windows.Forms;
using GestionDeStock2024.FORMS;

namespace GestionDeStock2024
{
    /// <summary>
    /// Point d'entrée principal de l'application
    /// </summary>
    internal static class Program
    {
        /// <summary>
        /// Point d'entrée principal de l'application.
        /// </summary>
        [STAThread]
        static void Main()
        {
            // Configuration de l'application
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);

            // Gestion des exceptions non gérées
            Application.SetUnhandledExceptionMode(UnhandledExceptionMode.CatchException);
            Application.ThreadException += Application_ThreadException;
            AppDomain.CurrentDomain.UnhandledException += CurrentDomain_UnhandledException;

            try
            {
                // Démarrer avec le formulaire Splash
                Application.Run(new FrmSplash());
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"Erreur fatale lors du démarrage de l'application :\n\n{ex.Message}",
                    "Erreur fatale",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Gestionnaire d'exceptions pour les threads UI
        /// </summary>
        private static void Application_ThreadException(object sender, System.Threading.ThreadExceptionEventArgs e)
        {
            MessageBox.Show(
                $"Une erreur inattendue s'est produite :\n\n{e.Exception.Message}\n\nDétails :\n{e.Exception.StackTrace}",
                "Erreur de l'application",
                MessageBoxButtons.OK,
                MessageBoxIcon.Error);
        }

        /// <summary>
        /// Gestionnaire d'exceptions pour les threads non-UI
        /// </summary>
        private static void CurrentDomain_UnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            var exception = e.ExceptionObject as Exception;
            MessageBox.Show(
                $"Une erreur critique s'est produite :\n\n{exception?.Message ?? "Erreur inconnue"}\n\nL'application va se fermer.",
                "Erreur critique",
                MessageBoxButtons.OK,
                MessageBoxIcon.Error);
        }
    }
}
