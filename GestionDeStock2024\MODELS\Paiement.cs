using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using static GestionDeStock2024.MODELS.Enums;

namespace GestionDeStock2024.MODELS
{
    /// <summary>
    /// Modèle pour les banques
    /// </summary>
    public class Banque : BaseEntity, IStatutEntity
    {
        [Required(ErrorMessage = "Le nom de la banque est obligatoire")]
        [StringLength(255)]
        public string NomBanque { get; set; }

        [StringLength(10)]
        public string CodeBanque { get; set; }

        public string Adresse { get; set; }

        [StringLength(20)]
        [Phone(ErrorMessage = "Format de téléphone invalide")]
        public string Telephone { get; set; }

        public string Statut { get; set; } = StatutGeneral.Actif.ToString();

        // Navigation
        public virtual ICollection<CompteBancaire> ComptesBancaires { get; set; }
    }

    /// <summary>
    /// Modèle pour les comptes bancaires
    /// </summary>
    public class CompteBancaire : BaseEntity, IStatutEntity
    {
        [Required(ErrorMessage = "La banque est obligatoire")]
        public int BanqueId { get; set; }

        [Required(ErrorMessage = "Le numéro de compte est obligatoire")]
        [StringLength(50)]
        public string NumeroCompte { get; set; }

        /// <summary>
        /// RIB algérien (23 chiffres)
        /// </summary>
        [StringLength(23)]
        public string RIB { get; set; }

        [StringLength(34)]
        public string IBAN { get; set; }

        [Required(ErrorMessage = "Le nom du titulaire est obligatoire")]
        [StringLength(255)]
        public string NomTitulaire { get; set; }

        public decimal SoldeInitial { get; set; } = 0;

        public decimal SoldeActuel { get; set; } = 0;

        public string TypeCompte { get; set; } = "Courant";

        public string Statut { get; set; } = StatutGeneral.Actif.ToString();

        // Navigation
        public virtual Banque Banque { get; set; }
        public virtual ICollection<MouvementBancaire> MouvementsBancaires { get; set; }
        public virtual ICollection<Paiement> Paiements { get; set; }

        // Propriétés calculées
        public bool EstCourant => TypeCompte == "Courant";
        public bool EstEpargne => TypeCompte == "Épargne";
        public bool EstProfessionnel => TypeCompte == "Professionnel";

        public bool EstActif => Statut == StatutGeneral.Actif.ToString();
        public bool EstFerme => Statut == "Fermé";

        public string NumeroCompteAffichage => $"{Banque?.NomBanque} - {NumeroCompte}";

        /// <summary>
        /// Valide le format du RIB algérien
        /// </summary>
        public bool RIBEstValide()
        {
            if (string.IsNullOrEmpty(RIB) || RIB.Length != 23) return false;
            return RIB.All(char.IsDigit);
        }
    }

    /// <summary>
    /// Modèle pour les paiements
    /// </summary>
    public class Paiement : BaseEntity, IStatutEntity
    {
        [Required(ErrorMessage = "Le numéro de paiement est obligatoire")]
        [StringLength(50)]
        public string NumeroPaiement { get; set; }

        [Required(ErrorMessage = "Le type de paiement est obligatoire")]
        public string TypePaiement { get; set; }

        [Required(ErrorMessage = "L'ID du document est obligatoire")]
        public int DocumentId { get; set; }

        [Required(ErrorMessage = "Le montant du paiement est obligatoire")]
        [Range(0.01, double.MaxValue, ErrorMessage = "Le montant doit être positif")]
        public decimal MontantPaiement { get; set; }

        [Required(ErrorMessage = "Le mode de paiement est obligatoire")]
        public int ModePaiementId { get; set; }

        public int? CompteBancaireId { get; set; }

        public int? CaisseId { get; set; }

        [StringLength(50)]
        public string NumeroCheque { get; set; }

        public DateTime? DateEcheanceCheque { get; set; }

        [StringLength(255)]
        public string BanqueCheque { get; set; }

        public decimal TimbreFiscal { get; set; } = 0;

        [Required(ErrorMessage = "La date de paiement est obligatoire")]
        public DateTime DatePaiement { get; set; } = DateTime.Today;

        [Required(ErrorMessage = "L'heure de paiement est obligatoire")]
        public TimeSpan HeurePaiement { get; set; } = DateTime.Now.TimeSpan;

        [Required(ErrorMessage = "L'utilisateur est obligatoire")]
        public int UtilisateurId { get; set; }

        public string Notes { get; set; }

        public string Statut { get; set; } = "Validé";

        // Navigation
        public virtual ModePaiement ModePaiement { get; set; }
        public virtual CompteBancaire CompteBancaire { get; set; }
        public virtual Caisse Caisse { get; set; }
        public virtual Utilisateur Utilisateur { get; set; }

        // Propriétés calculées
        public bool EstValide => Statut == "Validé";
        public bool EstEnAttente => Statut == "En_attente";
        public bool EstRejete => Statut == "Rejeté";
        public bool EstAnnule => Statut == "Annulé";

        public bool EstVente => TypePaiement == "Vente";
        public bool EstAchat => TypePaiement == "Achat";
        public bool EstDepense => TypePaiement == "Dépense";
        public bool EstRevenu => TypePaiement == "Revenu";

        public bool EstCheque => ModePaiement?.EstCheque == true;
        public bool EstEspeces => ModePaiement?.EstEspeces == true;

        public bool ChequeEchu => EstCheque && DateEcheanceCheque.HasValue && 
                                 DateEcheanceCheque.Value < DateTime.Today;

        public decimal MontantTotal => MontantPaiement + TimbreFiscal;

        /// <summary>
        /// Calcule le timbre fiscal selon la réglementation algérienne
        /// </summary>
        public void CalculerTimbreFiscal()
        {
            if (ModePaiement?.TimbreFiscalRequis == true)
            {
                TimbreFiscal = CalculerTimbreFiscalAlgerien(MontantPaiement);
            }
            else
            {
                TimbreFiscal = 0;
            }
        }

        private decimal CalculerTimbreFiscalAlgerien(decimal montant)
        {
            if (montant <= 300) return 0;
            if (montant <= 30000) return 1;
            if (montant <= 100000)
            {
                return Math.Ceiling((montant - 30000) / 100) * 1.5m;
            }
            return Math.Ceiling((100000 - 30000) / 100) * 1.5m;
        }
    }

    /// <summary>
    /// Modèle pour les mouvements de caisse
    /// </summary>
    public class MouvementCaisse : BaseEntity
    {
        [Required(ErrorMessage = "La caisse est obligatoire")]
        public int CaisseId { get; set; }

        [Required(ErrorMessage = "Le type de mouvement est obligatoire")]
        public string TypeMouvement { get; set; }

        [Required(ErrorMessage = "Le montant est obligatoire")]
        public decimal Montant { get; set; }

        [Required(ErrorMessage = "Le solde avant est obligatoire")]
        public decimal SoldeAvant { get; set; }

        [Required(ErrorMessage = "Le solde après est obligatoire")]
        public decimal SoldeApres { get; set; }

        [StringLength(100)]
        public string ReferenceDocument { get; set; }

        public string TypeDocument { get; set; }

        public int? DocumentId { get; set; }

        [Required(ErrorMessage = "L'utilisateur est obligatoire")]
        public int UtilisateurId { get; set; }

        [Required(ErrorMessage = "La date de mouvement est obligatoire")]
        public DateTime DateMouvement { get; set; } = DateTime.Today;

        [Required(ErrorMessage = "L'heure de mouvement est obligatoire")]
        public TimeSpan HeureMouvement { get; set; } = DateTime.Now.TimeSpan;

        [StringLength(255)]
        public string Description { get; set; }

        public string Notes { get; set; }

        // Navigation
        public virtual Caisse Caisse { get; set; }
        public virtual Utilisateur Utilisateur { get; set; }

        // Propriétés calculées
        public bool EstEntree => TypeMouvement == "Entrée";
        public bool EstSortie => TypeMouvement == "Sortie";
        public bool EstOuverture => TypeMouvement == "Ouverture";
        public bool EstFermeture => TypeMouvement == "Fermeture";
        public bool EstTransfert => TypeMouvement == "Transfert";

        public decimal EcartMontant => SoldeApres - SoldeAvant;

        public string DescriptionMouvement => $"{TypeMouvement} - {Montant:C}";
    }

    /// <summary>
    /// Modèle pour les mouvements bancaires
    /// </summary>
    public class MouvementBancaire : BaseEntity
    {
        [Required(ErrorMessage = "Le compte bancaire est obligatoire")]
        public int CompteBancaireId { get; set; }

        [Required(ErrorMessage = "Le type de mouvement est obligatoire")]
        public string TypeMouvement { get; set; }

        [Required(ErrorMessage = "Le montant est obligatoire")]
        public decimal Montant { get; set; }

        [Required(ErrorMessage = "Le solde avant est obligatoire")]
        public decimal SoldeAvant { get; set; }

        [Required(ErrorMessage = "Le solde après est obligatoire")]
        public decimal SoldeApres { get; set; }

        [StringLength(100)]
        public string ReferenceDocument { get; set; }

        public string TypeDocument { get; set; }

        public int? DocumentId { get; set; }

        [StringLength(50)]
        public string NumeroOperation { get; set; }

        [Required(ErrorMessage = "L'utilisateur est obligatoire")]
        public int UtilisateurId { get; set; }

        [Required(ErrorMessage = "La date de mouvement est obligatoire")]
        public DateTime DateMouvement { get; set; } = DateTime.Today;

        [Required(ErrorMessage = "La date de valeur est obligatoire")]
        public DateTime DateValeur { get; set; } = DateTime.Today;

        [StringLength(255)]
        public string Description { get; set; }

        public string Notes { get; set; }

        // Navigation
        public virtual CompteBancaire CompteBancaire { get; set; }
        public virtual Utilisateur Utilisateur { get; set; }

        // Propriétés calculées
        public bool EstCredit => TypeMouvement == "Crédit";
        public bool EstDebit => TypeMouvement == "Débit";
        public bool EstVirementEntrant => TypeMouvement == "Virement_entrant";
        public bool EstVirementSortant => TypeMouvement == "Virement_sortant";
        public bool EstFraisBancaires => TypeMouvement == "Frais_bancaires";

        public decimal EcartMontant => SoldeApres - SoldeAvant;

        public string DescriptionMouvement => $"{TypeMouvement} - {Montant:C}";

        public bool EstDiffere => DateValeur > DateMouvement;

        public int JoursDiffere => EstDiffere ? (DateValeur - DateMouvement).Days : 0;
    }

    /// <summary>
    /// Modèle pour les catégories de dépenses
    /// </summary>
    public class CategorieDepense : BaseEntity, IStatutEntity
    {
        [Required(ErrorMessage = "Le nom de la catégorie est obligatoire")]
        [StringLength(255)]
        public string NomCategorie { get; set; }

        public string Description { get; set; }

        [StringLength(20)]
        public string CodeComptable { get; set; }

        public string Statut { get; set; } = StatutGeneral.Actif.ToString();

        // Navigation
        public virtual ICollection<Depense> Depenses { get; set; }
    }

    /// <summary>
    /// Modèle pour les dépenses
    /// </summary>
    public class Depense : BaseEntity, IStatutEntity
    {
        [Required(ErrorMessage = "Le numéro de dépense est obligatoire")]
        [StringLength(50)]
        public string NumeroDepense { get; set; }

        [Required(ErrorMessage = "La catégorie de dépense est obligatoire")]
        public int CategorieDepenseId { get; set; }

        public int? FournisseurId { get; set; }

        [Required(ErrorMessage = "L'utilisateur est obligatoire")]
        public int UtilisateurId { get; set; }

        [Required(ErrorMessage = "La date de dépense est obligatoire")]
        public DateTime DateDepense { get; set; } = DateTime.Today;

        [Required(ErrorMessage = "La description est obligatoire")]
        [StringLength(255)]
        public string Description { get; set; }

        public decimal MontantHT { get; set; } = 0;

        public decimal MontantTVA { get; set; } = 0;

        [Required(ErrorMessage = "Le montant TTC est obligatoire")]
        [Range(0.01, double.MaxValue, ErrorMessage = "Le montant TTC doit être positif")]
        public decimal MontantTTC { get; set; }

        public int? ModePaiementId { get; set; }

        public int? CompteBancaireId { get; set; }

        public int? CaisseId { get; set; }

        [StringLength(100)]
        public string NumeroPiece { get; set; }

        public string Statut { get; set; } = "En_attente";

        public string Notes { get; set; }

        // Navigation
        public virtual CategorieDepense CategorieDepense { get; set; }
        public virtual Fournisseur Fournisseur { get; set; }
        public virtual Utilisateur Utilisateur { get; set; }
        public virtual ModePaiement ModePaiement { get; set; }
        public virtual CompteBancaire CompteBancaire { get; set; }
        public virtual Caisse Caisse { get; set; }

        // Propriétés calculées
        public bool EstEnAttente => Statut == "En_attente";
        public bool EstPayee => Statut == "Payé";
        public bool EstAnnulee => Statut == "Annulé";

        public string FournisseurNom => Fournisseur?.NomAffichage ?? "N/A";

        /// <summary>
        /// Calcule les montants HT et TVA à partir du TTC
        /// </summary>
        public void CalculerMontantsHT(decimal tauxTVA = 19.00m)
        {
            if (tauxTVA > 0)
            {
                MontantHT = MontantTTC / (1 + tauxTVA / 100);
                MontantTVA = MontantTTC - MontantHT;
            }
            else
            {
                MontantHT = MontantTTC;
                MontantTVA = 0;
            }
        }
    }

    /// <summary>
    /// Modèle pour les catégories de revenus
    /// </summary>
    public class CategorieRevenu : BaseEntity, IStatutEntity
    {
        [Required(ErrorMessage = "Le nom de la catégorie est obligatoire")]
        [StringLength(255)]
        public string NomCategorie { get; set; }

        public string Description { get; set; }

        [StringLength(20)]
        public string CodeComptable { get; set; }

        public string Statut { get; set; } = StatutGeneral.Actif.ToString();

        // Navigation
        public virtual ICollection<Revenu> Revenus { get; set; }
    }

    /// <summary>
    /// Modèle pour les revenus (autres que ventes)
    /// </summary>
    public class Revenu : BaseEntity, IStatutEntity
    {
        [Required(ErrorMessage = "Le numéro de revenu est obligatoire")]
        [StringLength(50)]
        public string NumeroRevenu { get; set; }

        [Required(ErrorMessage = "La catégorie de revenu est obligatoire")]
        public int CategorieRevenuId { get; set; }

        public int? ClientId { get; set; }

        [Required(ErrorMessage = "L'utilisateur est obligatoire")]
        public int UtilisateurId { get; set; }

        [Required(ErrorMessage = "La date de revenu est obligatoire")]
        public DateTime DateRevenu { get; set; } = DateTime.Today;

        [Required(ErrorMessage = "La description est obligatoire")]
        [StringLength(255)]
        public string Description { get; set; }

        public decimal MontantHT { get; set; } = 0;

        public decimal MontantTVA { get; set; } = 0;

        [Required(ErrorMessage = "Le montant TTC est obligatoire")]
        [Range(0.01, double.MaxValue, ErrorMessage = "Le montant TTC doit être positif")]
        public decimal MontantTTC { get; set; }

        public int? ModePaiementId { get; set; }

        public int? CompteBancaireId { get; set; }

        public int? CaisseId { get; set; }

        [StringLength(100)]
        public string NumeroPiece { get; set; }

        public string Statut { get; set; } = "En_attente";

        public string Notes { get; set; }

        // Navigation
        public virtual CategorieRevenu CategorieRevenu { get; set; }
        public virtual Client Client { get; set; }
        public virtual Utilisateur Utilisateur { get; set; }
        public virtual ModePaiement ModePaiement { get; set; }
        public virtual CompteBancaire CompteBancaire { get; set; }
        public virtual Caisse Caisse { get; set; }

        // Propriétés calculées
        public bool EstEnAttente => Statut == "En_attente";
        public bool EstEncaisse => Statut == "Encaissé";
        public bool EstAnnule => Statut == "Annulé";

        public string ClientNom => Client?.NomAffichage ?? "N/A";

        /// <summary>
        /// Calcule les montants HT et TVA à partir du TTC
        /// </summary>
        public void CalculerMontantsHT(decimal tauxTVA = 19.00m)
        {
            if (tauxTVA > 0)
            {
                MontantHT = MontantTTC / (1 + tauxTVA / 100);
                MontantTVA = MontantTTC - MontantHT;
            }
            else
            {
                MontantHT = MontantTTC;
                MontantTVA = 0;
            }
        }
    }
}
