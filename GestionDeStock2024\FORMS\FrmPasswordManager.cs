using System;
using System.Drawing;
using System.Windows.Forms;
using GestionDeStock2024.UTILS;

namespace GestionDeStock2024.FORMS
{
    /// <summary>
    /// Formulaire pour tester et gérer les mots de passe BCrypt
    /// </summary>
    public partial class FrmPasswordManager : Form
    {
        private TextBox txtPassword;
        private TextBox txtHash;
        private TextBox txtVerifyPassword;
        private Button btnHash, btnVerify, btnGenerate, btnMigrate;
        private Label lblResult, lblStrength;
        private ProgressBar pbStrength;

        public FrmPasswordManager()
        {
            InitializeComponent();
            InitializeForm();
        }

        private void InitializeForm()
        {
            this.Text = "Gestionnaire de Mots de Passe BCrypt";
            this.Size = new Size(600, 500);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            CreateLayout();
        }

        private void CreateLayout()
        {
            var mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20),
                BackColor = Color.FromArgb(248, 249, 250)
            };
            this.Controls.Add(mainPanel);

            // Titre
            var titleLabel = new Label
            {
                Text = "🔐 Gestionnaire de Mots de Passe",
                Font = new Font("Segoe UI", 16, FontStyle.Bold),
                ForeColor = Color.FromArgb(33, 37, 41),
                AutoSize = true,
                Location = new Point(0, 0)
            };
            mainPanel.Controls.Add(titleLabel);

            // Section Hachage
            var hashGroup = new GroupBox
            {
                Text = "Hacher un mot de passe",
                Location = new Point(0, 50),
                Size = new Size(mainPanel.Width - 40, 120),
                Font = new Font("Segoe UI", 10, FontStyle.Bold)
            };
            mainPanel.Controls.Add(hashGroup);

            var lblPassword = new Label
            {
                Text = "Mot de passe :",
                Location = new Point(10, 25),
                AutoSize = true
            };
            hashGroup.Controls.Add(lblPassword);

            txtPassword = new TextBox
            {
                Location = new Point(10, 45),
                Size = new Size(300, 25),
                UseSystemPasswordChar = true
            };
            txtPassword.TextChanged += TxtPassword_TextChanged;
            hashGroup.Controls.Add(txtPassword);

            btnHash = new Button
            {
                Text = "Hacher",
                Location = new Point(320, 43),
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(0, 123, 255),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            btnHash.Click += BtnHash_Click;
            hashGroup.Controls.Add(btnHash);

            btnGenerate = new Button
            {
                Text = "Générer",
                Location = new Point(410, 43),
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            btnGenerate.Click += BtnGenerate_Click;
            hashGroup.Controls.Add(btnGenerate);

            // Indicateur de force
            lblStrength = new Label
            {
                Text = "Force : ",
                Location = new Point(10, 80),
                AutoSize = true
            };
            hashGroup.Controls.Add(lblStrength);

            pbStrength = new ProgressBar
            {
                Location = new Point(80, 78),
                Size = new Size(200, 20),
                Minimum = 0,
                Maximum = 4
            };
            hashGroup.Controls.Add(pbStrength);

            // Section Hash
            var hashResultGroup = new GroupBox
            {
                Text = "Hash généré",
                Location = new Point(0, 180),
                Size = new Size(mainPanel.Width - 40, 80),
                Font = new Font("Segoe UI", 10, FontStyle.Bold)
            };
            mainPanel.Controls.Add(hashResultGroup);

            txtHash = new TextBox
            {
                Location = new Point(10, 25),
                Size = new Size(hashResultGroup.Width - 30, 25),
                ReadOnly = true,
                BackColor = Color.White,
                Font = new Font("Consolas", 9)
            };
            hashResultGroup.Controls.Add(txtHash);

            // Section Vérification
            var verifyGroup = new GroupBox
            {
                Text = "Vérifier un mot de passe",
                Location = new Point(0, 270),
                Size = new Size(mainPanel.Width - 40, 100),
                Font = new Font("Segoe UI", 10, FontStyle.Bold)
            };
            mainPanel.Controls.Add(verifyGroup);

            var lblVerifyPassword = new Label
            {
                Text = "Mot de passe à vérifier :",
                Location = new Point(10, 25),
                AutoSize = true
            };
            verifyGroup.Controls.Add(lblVerifyPassword);

            txtVerifyPassword = new TextBox
            {
                Location = new Point(10, 45),
                Size = new Size(300, 25),
                UseSystemPasswordChar = true
            };
            verifyGroup.Controls.Add(txtVerifyPassword);

            btnVerify = new Button
            {
                Text = "Vérifier",
                Location = new Point(320, 43),
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(255, 193, 7),
                ForeColor = Color.Black,
                FlatStyle = FlatStyle.Flat
            };
            btnVerify.Click += BtnVerify_Click;
            verifyGroup.Controls.Add(btnVerify);

            lblResult = new Label
            {
                Text = "",
                Location = new Point(10, 75),
                AutoSize = true,
                Font = new Font("Segoe UI", 10, FontStyle.Bold)
            };
            verifyGroup.Controls.Add(lblResult);

            // Section Migration
            var migrateGroup = new GroupBox
            {
                Text = "Migration de la base de données",
                Location = new Point(0, 380),
                Size = new Size(mainPanel.Width - 40, 60),
                Font = new Font("Segoe UI", 10, FontStyle.Bold)
            };
            mainPanel.Controls.Add(migrateGroup);

            btnMigrate = new Button
            {
                Text = "Migrer tous les mots de passe vers BCrypt",
                Location = new Point(10, 25),
                Size = new Size(300, 30),
                BackColor = Color.FromArgb(220, 53, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            btnMigrate.Click += BtnMigrate_Click;
            migrateGroup.Controls.Add(btnMigrate);
        }

        private void TxtPassword_TextChanged(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(txtPassword.Text))
            {
                lblStrength.Text = "Force : ";
                pbStrength.Value = 0;
                return;
            }

            var validation = PasswordHelper.ValidatePasswordStrength(txtPassword.Text);
            
            lblStrength.Text = $"Force : {validation.Strength}";
            pbStrength.Value = (int)validation.Strength;

            switch (validation.Strength)
            {
                case PasswordStrength.Weak:
                    pbStrength.ForeColor = Color.Red;
                    lblStrength.ForeColor = Color.Red;
                    break;
                case PasswordStrength.Medium:
                    pbStrength.ForeColor = Color.Orange;
                    lblStrength.ForeColor = Color.Orange;
                    break;
                case PasswordStrength.Strong:
                    pbStrength.ForeColor = Color.Blue;
                    lblStrength.ForeColor = Color.Blue;
                    break;
                case PasswordStrength.VeryStrong:
                    pbStrength.ForeColor = Color.Green;
                    lblStrength.ForeColor = Color.Green;
                    break;
            }
        }

        private void BtnHash_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(txtPassword.Text))
            {
                MessageBox.Show("Veuillez saisir un mot de passe", "Attention", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                var hash = PasswordHelper.HashPassword(txtPassword.Text);
                txtHash.Text = hash;
                MessageBox.Show("Mot de passe haché avec succès !", "Succès", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors du hachage : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnGenerate_Click(object sender, EventArgs e)
        {
            var password = PasswordHelper.GenerateTemporaryPassword(12);
            txtPassword.Text = password;
            MessageBox.Show($"Mot de passe généré : {password}\n\nCopiez-le avant de continuer !", "Mot de passe généré", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void BtnVerify_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(txtVerifyPassword.Text) || string.IsNullOrEmpty(txtHash.Text))
            {
                MessageBox.Show("Veuillez saisir un mot de passe et un hash", "Attention", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                bool isValid = PasswordHelper.VerifyPassword(txtVerifyPassword.Text, txtHash.Text);
                
                if (isValid)
                {
                    lblResult.Text = "✓ Mot de passe valide";
                    lblResult.ForeColor = Color.Green;
                }
                else
                {
                    lblResult.Text = "✗ Mot de passe invalide";
                    lblResult.ForeColor = Color.Red;
                }
            }
            catch (Exception ex)
            {
                lblResult.Text = $"Erreur : {ex.Message}";
                lblResult.ForeColor = Color.Red;
            }
        }

        private async void BtnMigrate_Click(object sender, EventArgs e)
        {
            var result = MessageBox.Show(
                "Cette opération va migrer tous les mots de passe de la base de données vers BCrypt.\n\n" +
                "ATTENTION : Cette opération est irréversible !\n\n" +
                "Voulez-vous continuer ?",
                "Confirmation de migration",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Warning);

            if (result == DialogResult.Yes)
            {
                try
                {
                    btnMigrate.Enabled = false;
                    btnMigrate.Text = "Migration en cours...";

                    await DatabaseInitializer.MigratePasswordsToBCryptAsync();

                    MessageBox.Show("Migration terminée avec succès !", "Succès", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Erreur lors de la migration : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
                finally
                {
                    btnMigrate.Enabled = true;
                    btnMigrate.Text = "Migrer tous les mots de passe vers BCrypt";
                }
            }
        }

        private void FrmPasswordManager_Load(object sender, EventArgs e)
        {
            // Test automatique avec "admin123"
            TestAdminPassword();
        }

        private void TestAdminPassword()
        {
            try
            {
                // Générer un nouveau hash pour admin123
                string password = "admin123";
                string hash = PasswordHelper.HashPassword(password);

                // Afficher le hash généré
                txtHash.Text = hash;

                // Vérifier que le hash fonctionne
                bool isValid = PasswordHelper.VerifyPassword(password, hash);

                if (isValid)
                {
                    lblResult.Text = "✓ Test BCrypt réussi pour admin123";
                    lblResult.ForeColor = Color.Green;
                }
                else
                {
                    lblResult.Text = "✗ Échec du test BCrypt";
                    lblResult.ForeColor = Color.Red;
                }

                // Pré-remplir le champ de vérification
                txtVerifyPassword.Text = password;
            }
            catch (Exception ex)
            {
                lblResult.Text = $"Erreur lors du test : {ex.Message}";
                lblResult.ForeColor = Color.Red;
            }
        }
    }
}
