using System;
using System.Drawing;
using System.Windows.Forms;
using GestionDeStock2024.MODELS;

namespace GestionDeStock2024.FORMS
{
    /// <summary>
    /// Formulaire de point de vente
    /// </summary>
    public partial class FrmVentes : Form
    {
        private Utilisateur currentUser;
        private DataGridView dgvPanier;
        private TextBox txtCodeBarre, txtQuantite;
        private Label lblTotal, lblTVA, lblTimbre, lblTotalTTC;
        private Button btnAjouter, btnSupprimer, btnValider, btnAnnuler;
        private ComboBox cmbClient, cmbModePaiement;

        public FrmVentes()
        {
            InitializeComponent();
            InitializeForm();
        }

        public FrmVentes(Utilisateur user) : this()
        {
            currentUser = user;
            InitializeVente();
        }

        private void InitializeForm()
        {
            this.Text = "Point de Vente";
            this.BackColor = Color.White;
            this.Size = new Size(1400, 900);
            this.WindowState = FormWindowState.Maximized;

            CreateLayout();
            ApplyModernTheme();
        }

        private void CreateLayout()
        {
            // Panel principal
            var mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20),
                BackColor = Color.FromArgb(248, 249, 250)
            };
            this.Controls.Add(mainPanel);

            // Titre
            var titleLabel = new Label
            {
                Text = "🛍️ Point de Vente",
                Font = new Font("Segoe UI", 24, FontStyle.Bold),
                ForeColor = Color.FromArgb(33, 37, 41),
                AutoSize = true,
                Location = new Point(0, 0)
            };
            mainPanel.Controls.Add(titleLabel);

            // Panel de saisie
            var inputPanel = new Panel
            {
                Location = new Point(0, 60),
                Size = new Size(mainPanel.Width - 40, 100),
                Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right,
                BackColor = Color.White
            };
            mainPanel.Controls.Add(inputPanel);

            CreateInputSection(inputPanel);

            // Panel du panier (gauche)
            var panierPanel = new Panel
            {
                Location = new Point(0, 180),
                Size = new Size((mainPanel.Width - 60) * 2 / 3, mainPanel.Height - 220),
                Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right,
                BackColor = Color.White
            };
            mainPanel.Controls.Add(panierPanel);

            CreatePanierSection(panierPanel);

            // Panel de total (droite)
            var totalPanel = new Panel
            {
                Location = new Point((mainPanel.Width - 60) * 2 / 3 + 20, 180),
                Size = new Size((mainPanel.Width - 60) / 3, mainPanel.Height - 220),
                Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Right,
                BackColor = Color.White
            };
            mainPanel.Controls.Add(totalPanel);

            CreateTotalSection(totalPanel);
        }

        private void CreateInputSection(Panel parent)
        {
            // Code-barres
            var lblCodeBarre = new Label
            {
                Text = "Code-barres / Référence:",
                Font = new Font("Segoe UI", 10, FontStyle.Bold),
                ForeColor = Color.FromArgb(73, 80, 87),
                Location = new Point(20, 20),
                AutoSize = true
            };
            parent.Controls.Add(lblCodeBarre);

            txtCodeBarre = new TextBox
            {
                Location = new Point(20, 45),
                Size = new Size(200, 25),
                Font = new Font("Segoe UI", 12),
            };
            txtCodeBarre.KeyDown += TxtCodeBarre_KeyDown;
            parent.Controls.Add(txtCodeBarre);

            // Quantité
            var lblQuantite = new Label
            {
                Text = "Quantité:",
                Font = new Font("Segoe UI", 10, FontStyle.Bold),
                ForeColor = Color.FromArgb(73, 80, 87),
                Location = new Point(240, 20),
                AutoSize = true
            };
            parent.Controls.Add(lblQuantite);

            txtQuantite = new TextBox
            {
                Location = new Point(240, 45),
                Size = new Size(80, 25),
                Font = new Font("Segoe UI", 12),
                Text = "1",
                TextAlign = HorizontalAlignment.Center
            };
            parent.Controls.Add(txtQuantite);

            // Bouton ajouter
            btnAjouter = new Button
            {
                Text = "➕ Ajouter",
                Location = new Point(340, 43),
                Size = new Size(100, 30),
                Font = new Font("Segoe UI", 10, FontStyle.Bold),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                FlatAppearance = { BorderSize = 0 },
                Cursor = Cursors.Hand
            };
            btnAjouter.Click += BtnAjouter_Click;
            parent.Controls.Add(btnAjouter);

            // Client
            var lblClient = new Label
            {
                Text = "Client:",
                Font = new Font("Segoe UI", 10, FontStyle.Bold),
                ForeColor = Color.FromArgb(73, 80, 87),
                Location = new Point(460, 20),
                AutoSize = true
            };
            parent.Controls.Add(lblClient);

            cmbClient = new ComboBox
            {
                Location = new Point(460, 45),
                Size = new Size(200, 25),
                Font = new Font("Segoe UI", 10),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            cmbClient.Items.AddRange(new[] { "Client de passage", "Ahmed Benali", "Fatima Khelifi", "SARL ALPHA" });
            cmbClient.SelectedIndex = 0;
            parent.Controls.Add(cmbClient);
        }

        private void CreatePanierSection(Panel parent)
        {
            var titleLabel = new Label
            {
                Text = "🛒 Panier",
                Font = new Font("Segoe UI", 16, FontStyle.Bold),
                ForeColor = Color.FromArgb(33, 37, 41),
                Location = new Point(20, 20),
                AutoSize = true
            };
            parent.Controls.Add(titleLabel);

            // DataGridView du panier
            dgvPanier = new DataGridView
            {
                Location = new Point(20, 60),
                Size = new Size(parent.Width - 40, parent.Height - 120),
                Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
            };
            parent.Controls.Add(dgvPanier);

            SetupPanierDataGridView();

            // Bouton supprimer
            btnSupprimer = new Button
            {
                Text = "🗑️ Supprimer ligne",
                Location = new Point(20, parent.Height - 40),
                Size = new Size(150, 30),
                Anchor = AnchorStyles.Bottom | AnchorStyles.Left,
                Font = new Font("Segoe UI", 10, FontStyle.Bold),
                BackColor = Color.FromArgb(220, 53, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                FlatAppearance = { BorderSize = 0 },
                Cursor = Cursors.Hand
            };
            btnSupprimer.Click += BtnSupprimer_Click;
            parent.Controls.Add(btnSupprimer);
        }

        private void CreateTotalSection(Panel parent)
        {
            var titleLabel = new Label
            {
                Text = "💰 Total",
                Font = new Font("Segoe UI", 16, FontStyle.Bold),
                ForeColor = Color.FromArgb(33, 37, 41),
                Location = new Point(20, 20),
                AutoSize = true
            };
            parent.Controls.Add(titleLabel);

            // Sous-total
            var lblSousTotal = new Label
            {
                Text = "Sous-total HT:",
                Font = new Font("Segoe UI", 12),
                ForeColor = Color.FromArgb(73, 80, 87),
                Location = new Point(20, 70),
                AutoSize = true
            };
            parent.Controls.Add(lblSousTotal);

            lblTotal = new Label
            {
                Text = "0,00 DA",
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                ForeColor = Color.FromArgb(33, 37, 41),
                Location = new Point(parent.Width - 120, 70),
                Size = new Size(100, 20),
                TextAlign = ContentAlignment.MiddleRight
            };
            parent.Controls.Add(lblTotal);

            // TVA
            var lblTVALabel = new Label
            {
                Text = "TVA (19%):",
                Font = new Font("Segoe UI", 12),
                ForeColor = Color.FromArgb(73, 80, 87),
                Location = new Point(20, 100),
                AutoSize = true
            };
            parent.Controls.Add(lblTVALabel);

            lblTVA = new Label
            {
                Text = "0,00 DA",
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                ForeColor = Color.FromArgb(33, 37, 41),
                Location = new Point(parent.Width - 120, 100),
                Size = new Size(100, 20),
                TextAlign = ContentAlignment.MiddleRight
            };
            parent.Controls.Add(lblTVA);

            // Timbre fiscal
            var lblTimbreLabel = new Label
            {
                Text = "Timbre fiscal:",
                Font = new Font("Segoe UI", 12),
                ForeColor = Color.FromArgb(73, 80, 87),
                Location = new Point(20, 130),
                AutoSize = true
            };
            parent.Controls.Add(lblTimbreLabel);

            lblTimbre = new Label
            {
                Text = "0,00 DA",
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                ForeColor = Color.FromArgb(33, 37, 41),
                Location = new Point(parent.Width - 120, 130),
                Size = new Size(100, 20),
                TextAlign = ContentAlignment.MiddleRight
            };
            parent.Controls.Add(lblTimbre);

            // Ligne de séparation
            var separator = new Panel
            {
                Location = new Point(20, 160),
                Size = new Size(parent.Width - 40, 2),
                BackColor = Color.FromArgb(222, 226, 230)
            };
            parent.Controls.Add(separator);

            // Total TTC
            var lblTotalTTCLabel = new Label
            {
                Text = "TOTAL TTC:",
                Font = new Font("Segoe UI", 16, FontStyle.Bold),
                ForeColor = Color.FromArgb(33, 37, 41),
                Location = new Point(20, 180),
                AutoSize = true
            };
            parent.Controls.Add(lblTotalTTCLabel);

            lblTotalTTC = new Label
            {
                Text = "0,00 DA",
                Font = new Font("Segoe UI", 20, FontStyle.Bold),
                ForeColor = Color.FromArgb(40, 167, 69),
                Location = new Point(parent.Width - 150, 175),
                Size = new Size(130, 30),
                TextAlign = ContentAlignment.MiddleRight
            };
            parent.Controls.Add(lblTotalTTC);

            // Mode de paiement
            var lblModePaiement = new Label
            {
                Text = "Mode de paiement:",
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                ForeColor = Color.FromArgb(73, 80, 87),
                Location = new Point(20, 230),
                AutoSize = true
            };
            parent.Controls.Add(lblModePaiement);

            cmbModePaiement = new ComboBox
            {
                Location = new Point(20, 255),
                Size = new Size(parent.Width - 40, 25),
                Font = new Font("Segoe UI", 11),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            cmbModePaiement.Items.AddRange(new[] { "Espèces", "Carte bancaire", "Chèque", "Virement" });
            cmbModePaiement.SelectedIndex = 0;
            parent.Controls.Add(cmbModePaiement);

            // Boutons d'action
            btnValider = new Button
            {
                Text = "✅ Valider la vente",
                Location = new Point(20, parent.Height - 80),
                Size = new Size(parent.Width - 40, 40),
                Anchor = AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right,
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                FlatAppearance = { BorderSize = 0 },
                Cursor = Cursors.Hand
            };
            btnValider.Click += BtnValider_Click;
            parent.Controls.Add(btnValider);

            btnAnnuler = new Button
            {
                Text = "❌ Annuler",
                Location = new Point(20, parent.Height - 35),
                Size = new Size(parent.Width - 40, 30),
                Anchor = AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right,
                Font = new Font("Segoe UI", 10),
                BackColor = Color.FromArgb(108, 117, 125),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                FlatAppearance = { BorderSize = 0 },
                Cursor = Cursors.Hand
            };
            btnAnnuler.Click += BtnAnnuler_Click;
            parent.Controls.Add(btnAnnuler);
        }

        private void SetupPanierDataGridView()
        {
            dgvPanier.Columns.Clear();

            dgvPanier.Columns.Add("Article", "Article");
            dgvPanier.Columns.Add("PrixUnitaire", "Prix unitaire");
            dgvPanier.Columns.Add("Quantite", "Quantité");
            dgvPanier.Columns.Add("Total", "Total");

            dgvPanier.Columns["Article"].Width = 300;
            dgvPanier.Columns["PrixUnitaire"].Width = 100;
            dgvPanier.Columns["Quantite"].Width = 80;
            dgvPanier.Columns["Total"].Width = 100;

            dgvPanier.Columns["PrixUnitaire"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
            dgvPanier.Columns["Quantite"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dgvPanier.Columns["Total"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;

            dgvPanier.Columns["PrixUnitaire"].DefaultCellStyle.Format = "N2";
            dgvPanier.Columns["Total"].DefaultCellStyle.Format = "N2";
        }

        private void ApplyModernTheme()
        {
            // Style du DataGridView
            dgvPanier.EnableHeadersVisualStyles = false;
            dgvPanier.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(52, 58, 64);
            dgvPanier.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvPanier.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 11, FontStyle.Bold);
            dgvPanier.ColumnHeadersHeight = 40;

            dgvPanier.DefaultCellStyle.BackColor = Color.White;
            dgvPanier.DefaultCellStyle.ForeColor = Color.FromArgb(33, 37, 41);
            dgvPanier.DefaultCellStyle.Font = new Font("Segoe UI", 10);
            dgvPanier.DefaultCellStyle.SelectionBackColor = Color.FromArgb(0, 123, 255);
            dgvPanier.DefaultCellStyle.SelectionForeColor = Color.White;

            dgvPanier.AlternatingRowsDefaultCellStyle.BackColor = Color.FromArgb(248, 249, 250);
            dgvPanier.GridColor = Color.FromArgb(222, 226, 230);
            dgvPanier.RowHeadersVisible = false;
            dgvPanier.CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal;
        }

        private void InitializeVente()
        {
            txtCodeBarre.Focus();
            CalculerTotal();
        }

        private void CalculerTotal()
        {
            decimal sousTotal = 0;
            foreach (DataGridViewRow row in dgvPanier.Rows)
            {
                if (row.Cells["Total"].Value != null)
                {
                    sousTotal += Convert.ToDecimal(row.Cells["Total"].Value);
                }
            }

            decimal tva = sousTotal * 0.19m;
            decimal timbre = sousTotal > 300 ? (sousTotal <= 30000 ? 1.00m : Math.Ceiling(sousTotal / 100) * 1.5m) : 0;
            decimal totalTTC = sousTotal + tva + timbre;

            lblTotal.Text = $"{sousTotal:N2} DA";
            lblTVA.Text = $"{tva:N2} DA";
            lblTimbre.Text = $"{timbre:N2} DA";
            lblTotalTTC.Text = $"{totalTTC:N2} DA";
        }

        #region Event Handlers

        private void TxtCodeBarre_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                BtnAjouter_Click(sender, e);
            }
        }

        private void BtnAjouter_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtCodeBarre.Text))
            {
                MessageBox.Show("Veuillez saisir un code-barres ou une référence", "Attention", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // Simulation d'ajout d'article
            var article = "Article simulé - " + txtCodeBarre.Text;
            var prix = 150.00m;
            var quantite = Convert.ToDecimal(txtQuantite.Text);
            var total = prix * quantite;

            dgvPanier.Rows.Add(article, prix, quantite, total);
            
            txtCodeBarre.Clear();
            txtQuantite.Text = "1";
            txtCodeBarre.Focus();
            
            CalculerTotal();
        }

        private void BtnSupprimer_Click(object sender, EventArgs e)
        {
            if (dgvPanier.SelectedRows.Count > 0)
            {
                dgvPanier.Rows.RemoveAt(dgvPanier.SelectedRows[0].Index);
                CalculerTotal();
            }
        }

        private void BtnValider_Click(object sender, EventArgs e)
        {
            if (dgvPanier.Rows.Count == 0)
            {
                MessageBox.Show("Le panier est vide", "Attention", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var result = MessageBox.Show("Confirmer la vente ?", "Confirmation", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            if (result == DialogResult.Yes)
            {
                MessageBox.Show("Vente validée avec succès !\n(Fonctionnalité complète à implémenter)", "Succès", MessageBoxButtons.OK, MessageBoxIcon.Information);
                
                // Vider le panier
                dgvPanier.Rows.Clear();
                CalculerTotal();
                txtCodeBarre.Focus();
            }
        }

        private void BtnAnnuler_Click(object sender, EventArgs e)
        {
            if (dgvPanier.Rows.Count > 0)
            {
                var result = MessageBox.Show("Annuler la vente en cours ?", "Confirmation", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                if (result == DialogResult.Yes)
                {
                    dgvPanier.Rows.Clear();
                    CalculerTotal();
                    txtCodeBarre.Focus();
                }
            }
        }

        #endregion

        private void FrmVentes_Load(object sender, EventArgs e)
        {
            InitializeVente();
        }
    }
}
