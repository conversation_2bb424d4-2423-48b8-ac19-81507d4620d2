using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text.RegularExpressions;

namespace GestionDeStock2024.UTILS
{
    /// <summary>
    /// Utilitaires de validation pour les données algériennes
    /// </summary>
    public static class ValidationHelper
    {
        #region Validation des identifiants fiscaux algériens

        /// <summary>
        /// Valide un NIF (Numéro d'Identification Fiscale) algérien
        /// </summary>
        /// <param name="nif">NIF à valider</param>
        /// <returns>True si le NIF est valide</returns>
        public static bool IsValidNIF(string nif)
        {
            if (string.IsNullOrWhiteSpace(nif))
                return false;

            // Le NIF algérien est composé de 15 chiffres
            var pattern = @"^\d{15}$";
            return Regex.IsMatch(nif.Trim(), pattern);
        }

        /// <summary>
        /// Valide un NIS (Numéro d'Identification Statistique) algérien
        /// </summary>
        /// <param name="nis">NIS à valider</param>
        /// <returns>True si le NIS est valide</returns>
        public static bool IsValidNIS(string nis)
        {
            if (string.IsNullOrWhiteSpace(nis))
                return false;

            // Le NIS algérien est composé de 20 chiffres
            var pattern = @"^\d{20}$";
            return Regex.IsMatch(nis.Trim(), pattern);
        }

        /// <summary>
        /// Valide un RC (Registre de Commerce) algérien
        /// </summary>
        /// <param name="rc">RC à valider</param>
        /// <returns>True si le RC est valide</returns>
        public static bool IsValidRC(string rc)
        {
            if (string.IsNullOrWhiteSpace(rc))
                return false;

            // Format: XX/XX-XXXXXXX-XX/XX (où X = chiffre)
            var pattern = @"^\d{2}/\d{2}-\d{7}-\d{2}/\d{2}$";
            return Regex.IsMatch(rc.Trim(), pattern);
        }

        /// <summary>
        /// Valide un ART (Article d'Imposition) algérien
        /// </summary>
        /// <param name="art">ART à valider</param>
        /// <returns>True si l'ART est valide</returns>
        public static bool IsValidART(string art)
        {
            if (string.IsNullOrWhiteSpace(art))
                return false;

            // L'ART algérien est généralement composé de 13 chiffres
            var pattern = @"^\d{13}$";
            return Regex.IsMatch(art.Trim(), pattern);
        }

        /// <summary>
        /// Valide un RIB (Relevé d'Identité Bancaire) algérien
        /// </summary>
        /// <param name="rib">RIB à valider</param>
        /// <returns>True si le RIB est valide</returns>
        public static bool IsValidRIB(string rib)
        {
            if (string.IsNullOrWhiteSpace(rib))
                return false;

            // Le RIB algérien est composé de 23 chiffres
            var pattern = @"^\d{23}$";
            if (!Regex.IsMatch(rib.Trim(), pattern))
                return false;

            // Validation de la clé de contrôle (algorithme spécifique)
            return ValidateRIBChecksum(rib.Trim());
        }

        /// <summary>
        /// Valide la clé de contrôle d'un RIB algérien
        /// </summary>
        /// <param name="rib">RIB de 23 chiffres</param>
        /// <returns>True si la clé est valide</returns>
        private static bool ValidateRIBChecksum(string rib)
        {
            try
            {
                // Extraire les parties du RIB
                var codeBanque = rib.Substring(0, 3);
                var codeAgence = rib.Substring(3, 5);
                var numeroCompte = rib.Substring(8, 13);
                var cleControle = rib.Substring(21, 2);

                // Calculer la clé de contrôle
                var baseCalcul = codeBanque + codeAgence + numeroCompte;
                var calculatedKey = CalculateRIBChecksum(baseCalcul);

                return cleControle == calculatedKey.ToString("D2");
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Calcule la clé de contrôle d'un RIB algérien
        /// </summary>
        /// <param name="baseNumber">Numéro de base (21 premiers chiffres)</param>
        /// <returns>Clé de contrôle</returns>
        private static int CalculateRIBChecksum(string baseNumber)
        {
            // Algorithme de calcul de la clé RIB algérien
            var sum = 0;
            var weights = new[] { 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1 };

            for (int i = 0; i < baseNumber.Length; i++)
            {
                var digit = int.Parse(baseNumber[i].ToString());
                var product = digit * weights[i];
                sum += product > 9 ? product - 9 : product;
            }

            return (10 - (sum % 10)) % 10;
        }

        #endregion

        #region Validation des codes-barres

        /// <summary>
        /// Valide un code-barre EAN13
        /// </summary>
        /// <param name="barcode">Code-barre à valider</param>
        /// <returns>True si le code-barre est valide</returns>
        public static bool IsValidEAN13(string barcode)
        {
            if (string.IsNullOrWhiteSpace(barcode) || barcode.Length != 13)
                return false;

            if (!barcode.All(char.IsDigit))
                return false;

            // Calcul de la clé de contrôle EAN13
            var sum = 0;
            for (int i = 0; i < 12; i++)
            {
                var digit = int.Parse(barcode[i].ToString());
                sum += (i % 2 == 0) ? digit : digit * 3;
            }

            var checkDigit = (10 - (sum % 10)) % 10;
            return checkDigit == int.Parse(barcode[12].ToString());
        }

        /// <summary>
        /// Valide un code-barre EAN8
        /// </summary>
        /// <param name="barcode">Code-barre à valider</param>
        /// <returns>True si le code-barre est valide</returns>
        public static bool IsValidEAN8(string barcode)
        {
            if (string.IsNullOrWhiteSpace(barcode) || barcode.Length != 8)
                return false;

            if (!barcode.All(char.IsDigit))
                return false;

            // Calcul de la clé de contrôle EAN8
            var sum = 0;
            for (int i = 0; i < 7; i++)
            {
                var digit = int.Parse(barcode[i].ToString());
                sum += (i % 2 == 0) ? digit * 3 : digit;
            }

            var checkDigit = (10 - (sum % 10)) % 10;
            return checkDigit == int.Parse(barcode[7].ToString());
        }

        /// <summary>
        /// Génère un code-barre EAN13 valide
        /// </summary>
        /// <param name="baseCode">Code de base (12 chiffres)</param>
        /// <returns>Code-barre EAN13 complet</returns>
        public static string GenerateEAN13(string baseCode)
        {
            if (string.IsNullOrWhiteSpace(baseCode) || baseCode.Length != 12)
                throw new ArgumentException("Le code de base doit contenir exactement 12 chiffres");

            if (!baseCode.All(char.IsDigit))
                throw new ArgumentException("Le code de base ne doit contenir que des chiffres");

            var sum = 0;
            for (int i = 0; i < 12; i++)
            {
                var digit = int.Parse(baseCode[i].ToString());
                sum += (i % 2 == 0) ? digit : digit * 3;
            }

            var checkDigit = (10 - (sum % 10)) % 10;
            return baseCode + checkDigit.ToString();
        }

        #endregion

        #region Validation des données métier

        /// <summary>
        /// Valide un prix
        /// </summary>
        /// <param name="prix">Prix à valider</param>
        /// <param name="allowZero">Autoriser zéro</param>
        /// <returns>True si le prix est valide</returns>
        public static bool IsValidPrice(decimal prix, bool allowZero = false)
        {
            if (allowZero)
                return prix >= 0;
            else
                return prix > 0;
        }

        /// <summary>
        /// Valide une quantité
        /// </summary>
        /// <param name="quantite">Quantité à valider</param>
        /// <param name="allowZero">Autoriser zéro</param>
        /// <returns>True si la quantité est valide</returns>
        public static bool IsValidQuantity(decimal quantite, bool allowZero = false)
        {
            if (allowZero)
                return quantite >= 0;
            else
                return quantite > 0;
        }

        /// <summary>
        /// Valide un taux de TVA
        /// </summary>
        /// <param name="taux">Taux de TVA</param>
        /// <returns>True si le taux est valide</returns>
        public static bool IsValidTVARate(decimal taux)
        {
            return taux >= 0 && taux <= 100;
        }

        /// <summary>
        /// Valide un pourcentage de remise
        /// </summary>
        /// <param name="remise">Pourcentage de remise</param>
        /// <returns>True si la remise est valide</returns>
        public static bool IsValidDiscountRate(decimal remise)
        {
            return remise >= 0 && remise <= 100;
        }

        /// <summary>
        /// Valide une date de naissance
        /// </summary>
        /// <param name="dateNaissance">Date de naissance</param>
        /// <returns>True si la date est valide</returns>
        public static bool IsValidBirthDate(DateTime? dateNaissance)
        {
            if (!dateNaissance.HasValue)
                return true; // Optionnel

            var today = DateTime.Today;
            var age = today.Year - dateNaissance.Value.Year;
            if (dateNaissance.Value.Date > today.AddYears(-age))
                age--;

            return age >= 0 && age <= 150;
        }

        /// <summary>
        /// Valide une date d'expiration
        /// </summary>
        /// <param name="dateExpiration">Date d'expiration</param>
        /// <param name="allowPastDates">Autoriser les dates passées</param>
        /// <returns>True si la date est valide</returns>
        public static bool IsValidExpirationDate(DateTime? dateExpiration, bool allowPastDates = false)
        {
            if (!dateExpiration.HasValue)
                return true; // Optionnel

            if (allowPastDates)
                return true;

            return dateExpiration.Value.Date >= DateTime.Today;
        }

        #endregion

        #region Validation des formats

        /// <summary>
        /// Valide un numéro de téléphone algérien
        /// </summary>
        /// <param name="telephone">Numéro de téléphone</param>
        /// <returns>True si le numéro est valide</returns>
        public static bool IsValidAlgerianPhoneNumber(string telephone)
        {
            if (string.IsNullOrWhiteSpace(telephone))
                return false;

            // Formats acceptés:
            // 0X XX XX XX XX (fixe)
            // 0XXX XX XX XX (mobile)
            // +213 X XX XX XX XX (international fixe)
            // +213 XXX XX XX XX (international mobile)
            var patterns = new[]
            {
                @"^0[1-9] \d{2} \d{2} \d{2} \d{2}$", // Fixe national
                @"^0[567]\d{2} \d{2} \d{2} \d{2}$", // Mobile national
                @"^\+213 [1-9] \d{2} \d{2} \d{2} \d{2}$", // Fixe international
                @"^\+213 [567]\d{2} \d{2} \d{2} \d{2}$" // Mobile international
            };

            return patterns.Any(pattern => Regex.IsMatch(telephone.Trim(), pattern));
        }

        /// <summary>
        /// Valide une adresse email
        /// </summary>
        /// <param name="email">Adresse email</param>
        /// <returns>True si l'email est valide</returns>
        public static bool IsValidEmail(string email)
        {
            if (string.IsNullOrWhiteSpace(email))
                return false;

            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Valide un code postal algérien
        /// </summary>
        /// <param name="codePostal">Code postal</param>
        /// <returns>True si le code postal est valide</returns>
        public static bool IsValidAlgerianPostalCode(string codePostal)
        {
            if (string.IsNullOrWhiteSpace(codePostal))
                return false;

            // Code postal algérien: 5 chiffres
            var pattern = @"^\d{5}$";
            return Regex.IsMatch(codePostal.Trim(), pattern);
        }

        #endregion

        #region Utilitaires de validation

        /// <summary>
        /// Valide plusieurs conditions et retourne les erreurs
        /// </summary>
        /// <param name="validations">Dictionnaire des validations (condition, message d'erreur)</param>
        /// <returns>Liste des erreurs</returns>
        public static List<string> ValidateMultiple(Dictionary<bool, string> validations)
        {
            var errors = new List<string>();

            foreach (var validation in validations)
            {
                if (!validation.Key)
                {
                    errors.Add(validation.Value);
                }
            }

            return errors;
        }

        /// <summary>
        /// Nettoie et formate un numéro (supprime espaces, tirets, etc.)
        /// </summary>
        /// <param name="number">Numéro à nettoyer</param>
        /// <returns>Numéro nettoyé</returns>
        public static string CleanNumber(string number)
        {
            if (string.IsNullOrWhiteSpace(number))
                return string.Empty;

            return Regex.Replace(number, @"[^\d]", "");
        }

        /// <summary>
        /// Formate un numéro de téléphone algérien
        /// </summary>
        /// <param name="telephone">Numéro de téléphone</param>
        /// <returns>Numéro formaté</returns>
        public static string FormatAlgerianPhoneNumber(string telephone)
        {
            var cleaned = CleanNumber(telephone);

            if (cleaned.Length == 10 && cleaned.StartsWith("0"))
            {
                // Format: 0X XX XX XX XX ou 0XXX XX XX XX
                if (cleaned[1] >= '1' && cleaned[1] <= '4')
                {
                    // Fixe
                    return $"{cleaned.Substring(0, 2)} {cleaned.Substring(2, 2)} {cleaned.Substring(4, 2)} {cleaned.Substring(6, 2)} {cleaned.Substring(8, 2)}";
                }
                else if (cleaned[1] >= '5' && cleaned[1] <= '7')
                {
                    // Mobile
                    return $"{cleaned.Substring(0, 4)} {cleaned.Substring(4, 2)} {cleaned.Substring(6, 2)} {cleaned.Substring(8, 2)}";
                }
            }

            return telephone; // Retourner tel quel si format non reconnu
        }

        /// <summary>
        /// Formate un RIB algérien
        /// </summary>
        /// <param name="rib">RIB à formater</param>
        /// <returns>RIB formaté</returns>
        public static string FormatRIB(string rib)
        {
            var cleaned = CleanNumber(rib);

            if (cleaned.Length == 23)
            {
                // Format: XXX XXXXX XXXXXXXXXXXXX XX
                return $"{cleaned.Substring(0, 3)} {cleaned.Substring(3, 5)} {cleaned.Substring(8, 13)} {cleaned.Substring(21, 2)}";
            }

            return rib; // Retourner tel quel si format non reconnu
        }

        /// <summary>
        /// Valide et nettoie une chaîne de caractères
        /// </summary>
        /// <param name="input">Chaîne d'entrée</param>
        /// <param name="maxLength">Longueur maximale</param>
        /// <param name="allowEmpty">Autoriser vide</param>
        /// <returns>Chaîne nettoyée ou null si invalide</returns>
        public static string CleanAndValidateString(string input, int maxLength, bool allowEmpty = true)
        {
            if (string.IsNullOrWhiteSpace(input))
            {
                return allowEmpty ? string.Empty : null;
            }

            var cleaned = input.Trim();
            return cleaned.Length <= maxLength ? cleaned : null;
        }

        #endregion
    }

    /// <summary>
    /// Résultat de validation
    /// </summary>
    public class ValidationResult
    {
        public bool IsValid { get; set; }
        public List<string> Errors { get; set; } = new List<string>();
        public List<string> Warnings { get; set; } = new List<string>();

        public static ValidationResult Success()
        {
            return new ValidationResult { IsValid = true };
        }

        public static ValidationResult Failure(params string[] errors)
        {
            return new ValidationResult
            {
                IsValid = false,
                Errors = errors.ToList()
            };
        }

        public static ValidationResult Failure(List<string> errors)
        {
            return new ValidationResult
            {
                IsValid = false,
                Errors = errors
            };
        }

        public void AddError(string error)
        {
            Errors.Add(error);
            IsValid = false;
        }

        public void AddWarning(string warning)
        {
            Warnings.Add(warning);
        }
    }
}
