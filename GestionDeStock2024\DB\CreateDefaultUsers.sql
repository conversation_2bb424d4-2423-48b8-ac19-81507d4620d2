-- Script pour créer les utilisateurs par défaut avec mots de passe en clair
-- À exécuter après la création de la base de données

USE gestion_supermarche_dz;

-- Insérer les utilisateurs par défaut avec mots de passe en clair
INSERT IGNORE INTO utilisateurs (nom_utilisateur, mot_de_passe, nom_complet, email, role, statut, created_at, updated_at)
VALUES 
-- Administrateur avec mot de passe "admin123"
('admin', 'admin123', 'Administrateur Système', '<EMAIL>', 'Administrateur', 'Actif', NOW(), NOW()),

-- Gestionnaire avec mot de passe "manager123"
('gestionnaire', 'manager123', 'Gestionnaire Principal', '<EMAIL>', 'Gestionnaire', 'Actif', NOW(), NOW()),

-- Vendeur avec mot de passe "vendeur123"  
('vendeur', 'vendeur123', 'Vendeur Principal', '<EMAIL>', 'Vendeur', 'Actif', NOW(), NOW()),

-- Caissier avec mot de passe "caissier123"
('caissier', 'caissier123', 'Caissier Principal', '<EMAIL>', 'Caissier', 'Actif', NOW(), NOW()),

-- Magasinier avec mot de passe "stock123"
('magasinier', 'stock123', 'Responsable Stock', '<EMAIL>', 'Magasinier', 'Actif', NOW(), NOW());

-- Afficher un message de confirmation
SELECT 'Utilisateurs par défaut créés avec succès!' as message;

-- Afficher les utilisateurs créés
SELECT nom_utilisateur, nom_complet, role, statut, created_at 
FROM utilisateurs 
ORDER BY created_at;
