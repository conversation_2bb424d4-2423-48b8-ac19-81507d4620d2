using System;
using System.ComponentModel.DataAnnotations;

namespace GestionDeStock2024.MODELS
{
    /// <summary>
    /// Classe de base pour toutes les entités
    /// </summary>
    public abstract class BaseEntity
    {
        [Key]
        public int Id { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        public DateTime UpdatedAt { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// Interface pour les entités avec statut
    /// </summary>
    public interface IStatutEntity
    {
        string Statut { get; set; }
    }

    /// <summary>
    /// Interface pour les entités avec code
    /// </summary>
    public interface ICodeEntity
    {
        string Code { get; set; }
    }

    /// <summary>
    /// Énumérations communes
    /// </summary>
    public static class Enums
    {
        public enum StatutGeneral
        {
            Actif,
            Inactif
        }

        public enum StatutFacture
        {
            Brouillon,
            Validée,
            Annulée,
            Archivée
        }

        public enum StatutPaiement
        {
            Non_payé,
            Partiellement_payé,
            Payé,
            Remboursé
        }

        public enum TypeMouvement
        {
            Entrée,
            Sortie,
            Ajustement,
            Transfert,
            Inventaire
        }

        public enum TypeDocument
        {
            Facture_achat,
            Facture_vente,
            Retour_client,
            Retour_fournisseur,
            Ajustement_manuel,
            Inventaire,
            Transfert
        }

        public enum TypeClient
        {
            Particulier,
            Entreprise,
            Revendeur
        }

        public enum RoleUtilisateur
        {
            Admin,
            Manager,
            Vendeur,
            Comptable,
            Magasinier
        }

        public enum TypePromotion
        {
            Pourcentage,
            Montant_fixe,
            Achetez_X_Obtenez_Y,
            Pack
        }

        public enum ModePaiement
        {
            Espèces,
            Espèces_sans_timbre,
            Chèque,
            Virement,
            Carte_bancaire,
            Crédit,
            Autre
        }
    }
}
