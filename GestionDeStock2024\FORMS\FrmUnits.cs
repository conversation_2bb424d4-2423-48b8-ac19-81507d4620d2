using System;
using System.Drawing;
using System.Windows.Forms;
using GestionDeStock2024.MODELS;

namespace GestionDeStock2024.FORMS
{
    /// <summary>
    /// Formulaire de gestion des unités de mesure
    /// </summary>
    public partial class FrmUnits : Form
    {
        private Utilisateur currentUser;
        private DataGridView dgvUnits;
        private TextBox txtSearch;
        private Button btnAdd, btnEdit, btnDelete, btnRefresh;

        public FrmUnits()
        {
            InitializeComponent();
            InitializeForm();
        }

        public FrmUnits(Utilisateur user) : this()
        {
            currentUser = user;
            LoadUnits();
        }

        private void InitializeForm()
        {
            this.Text = "Gestion des Unités de Mesure";
            this.BackColor = Color.White;
            this.Size = new Size(800, 600);
            this.WindowState = FormWindowState.Maximized;

            CreateLayout();
            ApplyModernTheme();
        }

        private void CreateLayout()
        {
            var mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20),
                BackColor = Color.FromArgb(248, 249, 250)
            };
            this.Controls.Add(mainPanel);

            // Titre
            var titleLabel = new Label
            {
                Text = "📏 Gestion des Unités de Mesure",
                Font = new Font("Segoe UI", 24, FontStyle.Bold),
                ForeColor = Color.FromArgb(33, 37, 41),
                AutoSize = true,
                Location = new Point(0, 0)
            };
            mainPanel.Controls.Add(titleLabel);

            // Panel des filtres et boutons
            var toolbarPanel = new Panel
            {
                Location = new Point(0, 60),
                Size = new Size(mainPanel.Width - 40, 60),
                Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right,
                BackColor = Color.White
            };
            mainPanel.Controls.Add(toolbarPanel);

            CreateToolbar(toolbarPanel);

            // DataGridView
            dgvUnits = new DataGridView
            {
                Location = new Point(0, 140),
                Size = new Size(mainPanel.Width - 40, mainPanel.Height - 180),
                Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
            };
            mainPanel.Controls.Add(dgvUnits);

            SetupDataGridView();
        }

        private void CreateToolbar(Panel parent)
        {
            // Recherche
            var lblSearch = new Label
            {
                Text = "🔍 Rechercher:",
                Font = new Font("Segoe UI", 10, FontStyle.Bold),
                ForeColor = Color.FromArgb(73, 80, 87),
                Location = new Point(10, 20),
                AutoSize = true
            };
            parent.Controls.Add(lblSearch);

            txtSearch = new TextBox
            {
                Location = new Point(110, 17),
                Size = new Size(200, 25),
                Font = new Font("Segoe UI", 10),
            };
            txtSearch.TextChanged += TxtSearch_TextChanged;
            parent.Controls.Add(txtSearch);

            // Boutons
            btnAdd = CreateButton("➕ Ajouter", new Point(330, 15), Color.FromArgb(40, 167, 69));
            btnAdd.Click += BtnAdd_Click;
            parent.Controls.Add(btnAdd);

            btnEdit = CreateButton("✏️ Modifier", new Point(440, 15), Color.FromArgb(0, 123, 255));
            btnEdit.Click += BtnEdit_Click;
            parent.Controls.Add(btnEdit);

            btnDelete = CreateButton("🗑️ Supprimer", new Point(550, 15), Color.FromArgb(220, 53, 69));
            btnDelete.Click += BtnDelete_Click;
            parent.Controls.Add(btnDelete);

            btnRefresh = CreateButton("🔄", new Point(670, 15), Color.FromArgb(108, 117, 125));
            btnRefresh.Size = new Size(35, 30);
            btnRefresh.Click += BtnRefresh_Click;
            parent.Controls.Add(btnRefresh);
        }

        private Button CreateButton(string text, Point location, Color backColor)
        {
            return new Button
            {
                Text = text,
                Location = location,
                Size = new Size(100, 30),
                Font = new Font("Segoe UI", 9, FontStyle.Bold),
                BackColor = backColor,
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                FlatAppearance = { BorderSize = 0 },
                Cursor = Cursors.Hand
            };
        }

        private void SetupDataGridView()
        {
            dgvUnits.Columns.Clear();

            dgvUnits.Columns.Add("Code", "Code");
            dgvUnits.Columns.Add("Nom", "Nom");
            dgvUnits.Columns.Add("Abreviation", "Abréviation");
            dgvUnits.Columns.Add("Description", "Description");
            dgvUnits.Columns.Add("TypeUnite", "Type");
            dgvUnits.Columns.Add("Statut", "Statut");

            dgvUnits.Columns["Code"].Width = 80;
            dgvUnits.Columns["Nom"].Width = 150;
            dgvUnits.Columns["Abreviation"].Width = 100;
            dgvUnits.Columns["Description"].Width = 200;
            dgvUnits.Columns["TypeUnite"].Width = 120;
            dgvUnits.Columns["Statut"].Width = 80;
        }

        private void ApplyModernTheme()
        {
            dgvUnits.EnableHeadersVisualStyles = false;
            dgvUnits.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(52, 58, 64);
            dgvUnits.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvUnits.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 10, FontStyle.Bold);
            dgvUnits.ColumnHeadersHeight = 40;

            dgvUnits.DefaultCellStyle.BackColor = Color.White;
            dgvUnits.DefaultCellStyle.ForeColor = Color.FromArgb(33, 37, 41);
            dgvUnits.DefaultCellStyle.Font = new Font("Segoe UI", 9);
            dgvUnits.DefaultCellStyle.SelectionBackColor = Color.FromArgb(0, 123, 255);
            dgvUnits.DefaultCellStyle.SelectionForeColor = Color.White;

            dgvUnits.AlternatingRowsDefaultCellStyle.BackColor = Color.FromArgb(248, 249, 250);
            dgvUnits.GridColor = Color.FromArgb(222, 226, 230);
            dgvUnits.RowHeadersVisible = false;
            dgvUnits.CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal;
        }

        private void LoadUnits()
        {
            dgvUnits.Rows.Clear();
            
            var sampleData = new[]
            {
                new object[] { "KG", "Kilogramme", "kg", "Unité de poids", "Poids", "Actif" },
                new object[] { "G", "Gramme", "g", "Unité de poids", "Poids", "Actif" },
                new object[] { "L", "Litre", "l", "Unité de volume", "Volume", "Actif" },
                new object[] { "ML", "Millilitre", "ml", "Unité de volume", "Volume", "Actif" },
                new object[] { "PCS", "Pièce", "pcs", "Unité de comptage", "Quantité", "Actif" },
                new object[] { "BOX", "Boîte", "box", "Unité d'emballage", "Emballage", "Actif" },
                new object[] { "M", "Mètre", "m", "Unité de longueur", "Longueur", "Actif" },
                new object[] { "CM", "Centimètre", "cm", "Unité de longueur", "Longueur", "Actif" }
            };

            foreach (var row in sampleData)
            {
                dgvUnits.Rows.Add(row);
            }
        }

        #region Event Handlers

        private void TxtSearch_TextChanged(object sender, EventArgs e)
        {
            FilterUnits();
        }

        private void FilterUnits()
        {
            LoadUnits();
        }

        private void BtnAdd_Click(object sender, EventArgs e)
        {
            var frmUnitAddEdit = new FrmUnitAddEdit();
            if (frmUnitAddEdit.ShowDialog() == DialogResult.OK)
            {
                LoadUnits();
            }
        }

        private void BtnEdit_Click(object sender, EventArgs e)
        {
            if (dgvUnits.SelectedRows.Count > 0)
            {
                var selectedRow = dgvUnits.SelectedRows[0];
                var code = selectedRow.Cells["Code"].Value?.ToString();
                
                var frmUnitAddEdit = new FrmUnitAddEdit(code);
                if (frmUnitAddEdit.ShowDialog() == DialogResult.OK)
                {
                    LoadUnits();
                }
            }
            else
            {
                MessageBox.Show("Veuillez sélectionner une unité à modifier", "Attention", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private void BtnDelete_Click(object sender, EventArgs e)
        {
            if (dgvUnits.SelectedRows.Count > 0)
            {
                var result = MessageBox.Show("Êtes-vous sûr de vouloir supprimer cette unité ?", "Confirmation", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                if (result == DialogResult.Yes)
                {
                    LoadUnits();
                    MessageBox.Show("Unité supprimée avec succès", "Succès", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            else
            {
                MessageBox.Show("Veuillez sélectionner une unité à supprimer", "Attention", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private void BtnRefresh_Click(object sender, EventArgs e)
        {
            LoadUnits();
        }

        #endregion

        private void FrmUnits_Load(object sender, EventArgs e)
        {
            LoadUnits();
        }
    }
}
