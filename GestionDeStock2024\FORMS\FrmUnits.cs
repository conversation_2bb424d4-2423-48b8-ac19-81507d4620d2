using System;
using System.Drawing;
using System.Windows.Forms;
using GestionDeStock2024.MODELS;

namespace GestionDeStock2024.FORMS
{
    /// <summary>
    /// Formulaire de gestion des unités de mesure
    /// </summary>
    public partial class FrmUnits : Form
    {
        private Utilisateur currentUser;

        public FrmUnits()
        {
            InitializeComponent();
            SetupDataGridView();
            ApplyModernTheme();
        }

        public FrmUnits(Utilisateur user) : this()
        {
            currentUser = user;
        }



        private void SetupDataGridView()
        {
            dgvUnits.Columns.Clear();

            dgvUnits.Columns.Add("Code", "Code");
            dgvUnits.Columns.Add("Nom", "Nom");
            dgvUnits.Columns.Add("Abreviation", "Abréviation");
            dgvUnits.Columns.Add("Description", "Description");
            dgvUnits.Columns.Add("TypeUnite", "Type");
            dgvUnits.Columns.Add("Statut", "Statut");

            dgvUnits.Columns["Code"].Width = 80;
            dgvUnits.Columns["Nom"].Width = 150;
            dgvUnits.Columns["Abreviation"].Width = 100;
            dgvUnits.Columns["Description"].Width = 200;
            dgvUnits.Columns["TypeUnite"].Width = 120;
            dgvUnits.Columns["Statut"].Width = 80;
        }

        private void ApplyModernTheme()
        {
            dgvUnits.EnableHeadersVisualStyles = false;
            dgvUnits.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(52, 58, 64);
            dgvUnits.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvUnits.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 10, FontStyle.Bold);
            dgvUnits.ColumnHeadersHeight = 40;

            dgvUnits.DefaultCellStyle.BackColor = Color.White;
            dgvUnits.DefaultCellStyle.ForeColor = Color.FromArgb(33, 37, 41);
            dgvUnits.DefaultCellStyle.Font = new Font("Segoe UI", 9);
            dgvUnits.DefaultCellStyle.SelectionBackColor = Color.FromArgb(0, 123, 255);
            dgvUnits.DefaultCellStyle.SelectionForeColor = Color.White;

            dgvUnits.AlternatingRowsDefaultCellStyle.BackColor = Color.FromArgb(248, 249, 250);
            dgvUnits.GridColor = Color.FromArgb(222, 226, 230);
            dgvUnits.RowHeadersVisible = false;
            dgvUnits.CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal;
        }

        private void LoadUnits()
        {
            dgvUnits.Rows.Clear();

            var sampleData = new[]
            {
                new object[] { "KG", "Kilogramme", "kg", "Unité de poids", "Poids", "Actif" },
                new object[] { "G", "Gramme", "g", "Unité de poids", "Poids", "Actif" },
                new object[] { "L", "Litre", "l", "Unité de volume", "Volume", "Actif" },
                new object[] { "ML", "Millilitre", "ml", "Unité de volume", "Volume", "Actif" },
                new object[] { "PCS", "Pièce", "pcs", "Unité de comptage", "Quantité", "Actif" },
                new object[] { "BOX", "Boîte", "box", "Unité d'emballage", "Emballage", "Actif" },
                new object[] { "M", "Mètre", "m", "Unité de longueur", "Longueur", "Actif" },
                new object[] { "CM", "Centimètre", "cm", "Unité de longueur", "Longueur", "Actif" }
            };

            foreach (var row in sampleData)
            {
                dgvUnits.Rows.Add(row);
            }
        }

        private void TxtSearch_TextChanged(object sender, EventArgs e)
        {
            LoadUnits(); // Filtrage simple
        }

        private void BtnAdd_Click(object sender, EventArgs e)
        {
            var frmUnitAddEdit = new FrmUnitAddEdit();
            if (frmUnitAddEdit.ShowDialog() == DialogResult.OK)
            {
                LoadUnits();
            }
        }

        private void BtnEdit_Click(object sender, EventArgs e)
        {
            if (dgvUnits.SelectedRows.Count > 0)
            {
                var selectedRow = dgvUnits.SelectedRows[0];
                var code = selectedRow.Cells["Code"].Value?.ToString();

                var frmUnitAddEdit = new FrmUnitAddEdit(code);
                if (frmUnitAddEdit.ShowDialog() == DialogResult.OK)
                {
                    LoadUnits();
                }
            }
            else
            {
                MessageBox.Show("Veuillez sélectionner une unité à modifier", "Attention", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private void BtnDelete_Click(object sender, EventArgs e)
        {
            if (dgvUnits.SelectedRows.Count > 0)
            {
                var result = MessageBox.Show("Êtes-vous sûr de vouloir supprimer cette unité ?", "Confirmation", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                if (result == DialogResult.Yes)
                {
                    LoadUnits();
                    MessageBox.Show("Unité supprimée avec succès", "Succès", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            else
            {
                MessageBox.Show("Veuillez sélectionner une unité à supprimer", "Attention", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private void BtnRefresh_Click(object sender, EventArgs e)
        {
            LoadUnits();
        }

        private void FrmUnits_Load(object sender, EventArgs e)
        {
            LoadUnits();
        }
    }
}
