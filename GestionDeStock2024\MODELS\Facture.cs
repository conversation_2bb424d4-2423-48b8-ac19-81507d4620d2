using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using static GestionDeStock2024.MODELS.Enums;

namespace GestionDeStock2024.MODELS
{
    /// <summary>
    /// Modèle pour les modes de paiement
    /// </summary>
    public class ModePaiement : BaseEntity, IStatutEntity
    {
        [Required(ErrorMessage = "Le nom du mode de paiement est obligatoire")]
        [StringLength(100)]
        public string NomMode { get; set; }

        [Required(ErrorMessage = "Le type de mode est obligatoire")]
        public string TypeMode { get; set; }

        public bool TimbreFiscalRequis { get; set; } = false;

        [Range(0, 100, ErrorMessage = "Le taux de timbre doit être entre 0 et 100")]
        public decimal TauxTimbre { get; set; } = 0;

        [StringLength(20)]
        public string ComptComptable { get; set; }

        public string Statut { get; set; } = StatutGeneral.Actif.ToString();

        // Navigation
        public virtual ICollection<FactureVente> FacturesVente { get; set; }
        public virtual ICollection<FactureAchat> FacturesAchat { get; set; }
        public virtual ICollection<Paiement> Paiements { get; set; }

        // Propriétés calculées
        public bool EstEspeces => TypeMode == ModePaiement.Espèces.ToString();
        public bool EstEspecesSansTimbre => TypeMode == ModePaiement.Espèces_sans_timbre.ToString();
        public bool EstCheque => TypeMode == ModePaiement.Chèque.ToString();
        public bool EstVirement => TypeMode == ModePaiement.Virement.ToString();
        public bool EstCarteBancaire => TypeMode == ModePaiement.Carte_bancaire.ToString();
        public bool EstCredit => TypeMode == ModePaiement.Crédit.ToString();
    }

    /// <summary>
    /// Modèle pour les caisses
    /// </summary>
    public class Caisse : BaseEntity, IStatutEntity
    {
        [Required(ErrorMessage = "Le nom de la caisse est obligatoire")]
        [StringLength(100)]
        public string NomCaisse { get; set; }

        [Required(ErrorMessage = "Le code de la caisse est obligatoire")]
        [StringLength(20)]
        public string CodeCaisse { get; set; }

        public decimal SoldeInitial { get; set; } = 0;

        public decimal SoldeActuel { get; set; } = 0;

        public int? UtilisateurResponsableId { get; set; }

        public string Statut { get; set; } = "Fermée";

        // Navigation
        public virtual Utilisateur UtilisateurResponsable { get; set; }
        public virtual ICollection<FactureVente> FacturesVente { get; set; }
        public virtual ICollection<MouvementCaisse> MouvementsCaisse { get; set; }

        // Propriétés calculées
        public bool EstOuverte => Statut == "Ouverte";
        public bool EstFermee => Statut == "Fermée";
        public bool EstSuspendue => Statut == "Suspendue";
    }

    /// <summary>
    /// Modèle pour les factures de vente
    /// </summary>
    public class FactureVente : BaseEntity, IStatutEntity
    {
        [Required(ErrorMessage = "Le numéro de facture est obligatoire")]
        [StringLength(50)]
        public string NumeroFacture { get; set; }

        public string TypeFacture { get; set; } = "Facture";

        public int? ClientId { get; set; }

        [Required(ErrorMessage = "L'utilisateur est obligatoire")]
        public int UtilisateurId { get; set; }

        public int? CaisseId { get; set; }

        [Required(ErrorMessage = "La date de facture est obligatoire")]
        public DateTime DateFacture { get; set; } = DateTime.Today;

        [Required(ErrorMessage = "L'heure de facture est obligatoire")]
        public TimeSpan HeureFacture { get; set; } = DateTime.Now.TimeOfDay;

        public decimal MontantHT { get; set; } = 0;

        public decimal MontantTVA { get; set; } = 0;

        public decimal MontantTTC { get; set; } = 0;

        public decimal RemiseGlobale { get; set; } = 0;

        [Range(0, 100, ErrorMessage = "Le pourcentage de remise doit être entre 0 et 100")]
        public decimal RemisePourcentage { get; set; } = 0;

        public decimal TimbreFiscal { get; set; } = 0;

        public decimal MontantTotal { get; set; } = 0;

        public decimal MontantPaye { get; set; } = 0;

        public decimal MontantRestant { get; set; } = 0;

        public int? ModePaiementId { get; set; }

        public string StatutPaiement { get; set; } = StatutPaiement.Non_payé.ToString();

        public string Statut { get; set; } = StatutFacture.Brouillon.ToString();

        public string Notes { get; set; }

        // Navigation
        public virtual Client Client { get; set; }
        public virtual Utilisateur Utilisateur { get; set; }
        public virtual Caisse Caisse { get; set; }
        public virtual ModePaiement ModePaiement { get; set; }
        public virtual ICollection<DetailFactureVente> DetailsFacture { get; set; }
        public virtual ICollection<Paiement> Paiements { get; set; }
        public virtual ICollection<RetourClient> RetoursClient { get; set; }

        // Propriétés calculées
        public bool EstValidee => Statut == StatutFacture.Validée.ToString();
        public bool EstBrouillon => Statut == StatutFacture.Brouillon.ToString();
        public bool EstAnnulee => Statut == StatutFacture.Annulée.ToString();

        public bool EstPayee => StatutPaiement == StatutPaiement.Payé.ToString();
        public bool EstNonPayee => StatutPaiement == StatutPaiement.Non_payé.ToString();
        public bool EstPartiellementPayee => StatutPaiement == StatutPaiement.Partiellement_payé.ToString();

        public string NomClientAffichage => Client?.NomAffichage ?? "Client de passage";

        public int NombreArticles => DetailsFacture?.Count ?? 0;

        public decimal QuantiteTotale => DetailsFacture?.Sum(d => d.Quantite) ?? 0;

        /// <summary>
        /// Calcule les totaux de la facture
        /// </summary>
        public void CalculerTotaux()
        {
            if (DetailsFacture == null || !DetailsFacture.Any()) return;

            MontantHT = DetailsFacture.Sum(d => d.MontantHT);
            MontantTVA = DetailsFacture.Sum(d => d.MontantTVA);
            MontantTTC = DetailsFacture.Sum(d => d.MontantTTC);

            // Appliquer la remise globale
            if (RemisePourcentage > 0)
            {
                RemiseGlobale = MontantTTC * (RemisePourcentage / 100);
            }

            var montantApresRemise = MontantTTC - RemiseGlobale;

            // Calculer le timbre fiscal si nécessaire
            if (ModePaiement?.TimbreFiscalRequis == true)
            {
                TimbreFiscal = CalculerTimbreFiscal(montantApresRemise);
            }

            MontantTotal = montantApresRemise + TimbreFiscal;
            MontantRestant = MontantTotal - MontantPaye;
        }

        /// <summary>
        /// Calcule le timbre fiscal selon la réglementation algérienne
        /// </summary>
        private decimal CalculerTimbreFiscal(decimal montant)
        {
            if (montant <= 300) return 0;
            if (montant <= 30000) return 1;
            if (montant <= 100000)
            {
                return Math.Ceiling((montant - 30000) / 100) * 1.5m;
            }
            return Math.Ceiling((100000 - 30000) / 100) * 1.5m;
        }

        /// <summary>
        /// Met à jour le statut de paiement
        /// </summary>
        public void MettreAJourStatutPaiement()
        {
            if (MontantPaye <= 0)
            {
                StatutPaiement = StatutPaiement.Non_payé.ToString();
            }
            else if (MontantPaye >= MontantTotal)
            {
                StatutPaiement = StatutPaiement.Payé.ToString();
                MontantRestant = 0;
            }
            else
            {
                StatutPaiement = StatutPaiement.Partiellement_payé.ToString();
                MontantRestant = MontantTotal - MontantPaye;
            }
        }
    }

    /// <summary>
    /// Modèle pour les détails des factures de vente
    /// </summary>
    public class DetailFactureVente : BaseEntity
    {
        [Required(ErrorMessage = "La facture est obligatoire")]
        public int FactureVenteId { get; set; }

        [Required(ErrorMessage = "L'article est obligatoire")]
        public int ArticleId { get; set; }

        [StringLength(100)]
        public string CodeBarreUtilise { get; set; }

        [Required(ErrorMessage = "La quantité est obligatoire")]
        [Range(0.01, double.MaxValue, ErrorMessage = "La quantité doit être positive")]
        public decimal Quantite { get; set; }

        [Required(ErrorMessage = "Le prix unitaire HT est obligatoire")]
        [Range(0, double.MaxValue, ErrorMessage = "Le prix unitaire HT doit être positif")]
        public decimal PrixUnitaireHT { get; set; }

        [Required(ErrorMessage = "Le prix unitaire TTC est obligatoire")]
        [Range(0, double.MaxValue, ErrorMessage = "Le prix unitaire TTC doit être positif")]
        public decimal PrixUnitaireTTC { get; set; }

        public decimal RemiseLigne { get; set; } = 0;

        [Range(0, 100, ErrorMessage = "Le pourcentage de remise doit être entre 0 et 100")]
        public decimal RemisePourcentage { get; set; } = 0;

        [Range(0, 100, ErrorMessage = "Le taux de TVA doit être entre 0 et 100")]
        public decimal TvaTaux { get; set; } = 19.00m;

        public decimal MontantHT { get; set; }

        public decimal MontantTVA { get; set; }

        public decimal MontantTTC { get; set; }

        // Navigation
        public virtual FactureVente FactureVente { get; set; }
        public virtual Article Article { get; set; }

        // Propriétés calculées
        public decimal MontantRemise => RemiseLigne > 0 ? RemiseLigne : 
                                       (PrixUnitaireTTC * Quantite * RemisePourcentage / 100);

        public decimal MontantNetHT => MontantHT - (MontantRemise / (1 + TvaTaux / 100));

        /// <summary>
        /// Calcule les montants de la ligne
        /// </summary>
        public void CalculerMontants()
        {
            var montantBrut = PrixUnitaireTTC * Quantite;
            var remise = RemiseLigne > 0 ? RemiseLigne : (montantBrut * RemisePourcentage / 100);
            
            MontantTTC = montantBrut - remise;
            MontantHT = MontantTTC / (1 + TvaTaux / 100);
            MontantTVA = MontantTTC - MontantHT;
        }
    }
}
