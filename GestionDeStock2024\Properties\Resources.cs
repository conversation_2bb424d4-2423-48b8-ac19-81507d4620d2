using System.Drawing;

namespace GestionDeStock2024.Properties
{
    /// <summary>
    /// Classe de ressources simple sans utiliser le système .resx
    /// </summary>
    internal static class Resources
    {
        /// <summary>
        /// Image placeholder pour le logo
        /// </summary>
        internal static Bitmap logo_placeholder
        {
            get
            {
                // Créer une image simple de 100x100 pixels
                var bitmap = new Bitmap(100, 100);
                using (var graphics = Graphics.FromImage(bitmap))
                {
                    // Fond bleu
                    graphics.Clear(Color.FromArgb(0, 122, 204));
                    
                    // Texte blanc au centre
                    using (var font = new Font("Arial", 12, FontStyle.Bold))
                    using (var brush = new SolidBrush(Color.White))
                    {
                        var text = "LOGO";
                        var size = graphics.MeasureString(text, font);
                        var x = (bitmap.Width - size.Width) / 2;
                        var y = (bitmap.Height - size.Height) / 2;
                        graphics.DrawString(text, font, brush, x, y);
                    }
                }
                return bitmap;
            }
        }
    }
}
