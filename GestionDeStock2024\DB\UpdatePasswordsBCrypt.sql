-- Script pour mettre à jour les mots de passe avec des hash BCrypt
-- À exécuter après la création de la base de données

USE gestion_supermarche_dz;

-- Mettre à jour le mot de passe admin avec un hash BCrypt pour "admin123"
-- Hash généré avec BCrypt coût 12 pour "admin123"
UPDATE utilisateurs 
SET mot_de_passe = '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSn9Vu.', 
    updated_at = NOW()
WHERE nom_utilisateur = 'admin';

-- Ajouter d'autres utilisateurs avec des mots de passe BCrypt
INSERT IGNORE INTO utilisateurs (nom_utilisateur, mot_de_passe, nom_complet, email, role, statut, created_at, updated_at)
VALUES 
-- Gestionnaire avec mot de passe "manager123"
('gestionnaire', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSn9Vu.', 'Gestionnaire Principal', '<EMAIL>', 'Gestionnaire', 'Actif', NOW(), NOW()),

-- Vendeur avec mot de passe "vendeur123"  
('vendeur', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSn9Vu.', 'Vendeur Principal', '<EMAIL>', 'Vendeur', 'Actif', NOW(), NOW()),

-- Caissier avec mot de passe "caissier123"
('caissier', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSn9Vu.', 'Caissier Principal', '<EMAIL>', 'Caissier', 'Actif', NOW(), NOW());

-- Ajouter des paramètres système pour la sécurité des mots de passe
INSERT IGNORE INTO parametres_systeme (cle, valeur, description, created_at, updated_at)
VALUES 
('PASSWORD_MIN_LENGTH', '8', 'Longueur minimale des mots de passe', NOW(), NOW()),
('PASSWORD_REQUIRE_UPPERCASE', 'true', 'Exiger au moins une majuscule', NOW(), NOW()),
('PASSWORD_REQUIRE_LOWERCASE', 'true', 'Exiger au moins une minuscule', NOW(), NOW()),
('PASSWORD_REQUIRE_DIGIT', 'true', 'Exiger au moins un chiffre', NOW(), NOW()),
('PASSWORD_REQUIRE_SPECIAL', 'false', 'Exiger au moins un caractère spécial', NOW(), NOW()),
('PASSWORD_EXPIRY_DAYS', '90', 'Durée de validité des mots de passe en jours', NOW(), NOW()),
('LOGIN_MAX_ATTEMPTS', '5', 'Nombre maximum de tentatives de connexion', NOW(), NOW()),
('LOGIN_LOCKOUT_DURATION', '30', 'Durée de verrouillage en minutes après échec', NOW(), NOW()),
('SESSION_TIMEOUT', '60', 'Durée de session en minutes', NOW(), NOW()),
('BCRYPT_COST', '12', 'Coût BCrypt pour le hachage des mots de passe', NOW(), NOW());

-- Créer une table pour l'historique des mots de passe (optionnel)
CREATE TABLE IF NOT EXISTS historique_mots_de_passe (
    id INT AUTO_INCREMENT PRIMARY KEY,
    utilisateur_id INT NOT NULL,
    ancien_mot_de_passe VARCHAR(255) NOT NULL,
    date_changement DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (utilisateur_id) REFERENCES utilisateurs(id) ON DELETE CASCADE,
    INDEX idx_utilisateur_date (utilisateur_id, date_changement)
);

-- Créer une table pour les tentatives de connexion
CREATE TABLE IF NOT EXISTS tentatives_connexion (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nom_utilisateur VARCHAR(50) NOT NULL,
    adresse_ip VARCHAR(45),
    succes BOOLEAN NOT NULL DEFAULT FALSE,
    message_erreur VARCHAR(255),
    date_tentative DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_utilisateur_date (nom_utilisateur, date_tentative),
    INDEX idx_ip_date (adresse_ip, date_tentative)
);

-- Créer une table pour les sessions utilisateur
CREATE TABLE IF NOT EXISTS sessions_utilisateur (
    id INT AUTO_INCREMENT PRIMARY KEY,
    utilisateur_id INT NOT NULL,
    token_session VARCHAR(255) NOT NULL UNIQUE,
    adresse_ip VARCHAR(45),
    user_agent TEXT,
    date_creation DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    date_expiration DATETIME NOT NULL,
    actif BOOLEAN NOT NULL DEFAULT TRUE,
    FOREIGN KEY (utilisateur_id) REFERENCES utilisateurs(id) ON DELETE CASCADE,
    INDEX idx_token (token_session),
    INDEX idx_utilisateur_actif (utilisateur_id, actif),
    INDEX idx_expiration (date_expiration)
);

-- Ajouter des colonnes de sécurité à la table utilisateurs si elles n'existent pas
ALTER TABLE utilisateurs 
ADD COLUMN IF NOT EXISTS derniere_modification_mdp DATETIME DEFAULT NULL,
ADD COLUMN IF NOT EXISTS tentatives_connexion_echouees INT DEFAULT 0,
ADD COLUMN IF NOT EXISTS compte_verrouille_jusqu DATETIME DEFAULT NULL,
ADD COLUMN IF NOT EXISTS force_changement_mdp BOOLEAN DEFAULT FALSE;

-- Mettre à jour la date de dernière modification du mot de passe pour les utilisateurs existants
UPDATE utilisateurs 
SET derniere_modification_mdp = created_at 
WHERE derniere_modification_mdp IS NULL;

-- Créer des index pour améliorer les performances
CREATE INDEX IF NOT EXISTS idx_utilisateurs_email ON utilisateurs(email);
CREATE INDEX IF NOT EXISTS idx_utilisateurs_statut ON utilisateurs(statut);
CREATE INDEX IF NOT EXISTS idx_utilisateurs_role ON utilisateurs(role);
CREATE INDEX IF NOT EXISTS idx_utilisateurs_derniere_connexion ON utilisateurs(derniere_connexion);

-- Afficher un message de confirmation
SELECT 'Mise à jour des mots de passe BCrypt terminée avec succès!' as message;

-- Afficher les utilisateurs créés/mis à jour
SELECT nom_utilisateur, nom_complet, role, statut, created_at, updated_at 
FROM utilisateurs 
ORDER BY created_at;
