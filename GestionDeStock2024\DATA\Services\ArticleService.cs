using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using GestionDeStock2024.MODELS;
using GestionDeStock2024.DATA.Repositories;

namespace GestionDeStock2024.DATA.Services
{
    /// <summary>
    /// Interface pour le service de gestion des articles
    /// </summary>
    public interface IArticleService : IMasterService<Article>
    {
        // Gestion du stock
        Task<ServiceResult<bool>> UpdateStockAsync(int articleId, decimal newStock, int userId, string motif = "Ajustement manuel");
        Task<ServiceResult<bool>> ReserveStockAsync(int articleId, decimal quantity);
        Task<ServiceResult<bool>> ReleaseReservedStockAsync(int articleId, decimal quantity);
        Task<ServiceResult<bool>> UpdatePMPAsync(int articleId, decimal newPMP);
        
        // Recherche et filtrage spécialisés
        Task<IEnumerable<Article>> GetArticlesWithLowStockAsync();
        Task<IEnumerable<Article>> GetArticlesOutOfStockAsync();
        Task<IEnumerable<Article>> GetArticlesByCategoryAsync(int categoryId);
        Task<IEnumerable<Article>> GetArticlesExpiringAsync(int daysFromNow = 30);
        Task<Article> GetByBarcodeAsync(string barcode);
        
        // Statistiques et rapports
        Task<decimal> CalculateStockValueAsync();
        Task<IEnumerable<Article>> GetArticlesForInventoryAsync();
        Task<IEnumerable<object>> GetStockStatisticsAsync();
        Task<IEnumerable<object>> GetTopSellingArticlesAsync(int count = 10, DateTime? dateDebut = null, DateTime? dateFin = null);
        
        // Gestion des codes-barres
        Task<ServiceResult<bool>> AddBarcodeAsync(int articleId, string barcode, string typeCode = "EAN13");
        Task<ServiceResult<bool>> RemoveBarcodeAsync(int articleId, string barcode);
        Task<IEnumerable<CodeBarre>> GetBarcodesAsync(int articleId);
        
        // Validation métier
        Task<ServiceResult<bool>> ValidateStockOperationAsync(int articleId, decimal quantity, string operation);
        Task<ServiceResult<bool>> ValidatePricesAsync(Article article);
    }

    /// <summary>
    /// Service de gestion des articles
    /// </summary>
    public class ArticleService : BaseService<Article>, IArticleService
    {
        private readonly IArticleRepository _articleRepository;

        public ArticleService(IArticleRepository articleRepository) : base(articleRepository)
        {
            _articleRepository = articleRepository;
        }

        #region Gestion du stock

        public async Task<ServiceResult<bool>> UpdateStockAsync(int articleId, decimal newStock, int userId, string motif = "Ajustement manuel")
        {
            try
            {
                // Validation
                var validationResult = await ValidateStockOperationAsync(articleId, newStock, "Ajustement");
                if (!validationResult.Success)
                {
                    return validationResult;
                }

                var success = await _articleRepository.UpdateStockAsync(articleId, newStock, userId, motif);
                return success 
                    ? ServiceResult<bool>.SuccessResult(true, "Stock mis à jour avec succès")
                    : ServiceResult<bool>.ErrorResult("Échec de la mise à jour du stock");
            }
            catch (Exception ex)
            {
                return ServiceResult<bool>.ErrorResult(ex, "Erreur lors de la mise à jour du stock");
            }
        }

        public async Task<ServiceResult<bool>> ReserveStockAsync(int articleId, decimal quantity)
        {
            try
            {
                // Validation
                var validationResult = await ValidateStockOperationAsync(articleId, quantity, "Réservation");
                if (!validationResult.Success)
                {
                    return validationResult;
                }

                var success = await _articleRepository.ReserveStockAsync(articleId, quantity);
                return success 
                    ? ServiceResult<bool>.SuccessResult(true, "Stock réservé avec succès")
                    : ServiceResult<bool>.ErrorResult("Stock insuffisant pour la réservation");
            }
            catch (Exception ex)
            {
                return ServiceResult<bool>.ErrorResult(ex, "Erreur lors de la réservation du stock");
            }
        }

        public async Task<ServiceResult<bool>> ReleaseReservedStockAsync(int articleId, decimal quantity)
        {
            try
            {
                var success = await _articleRepository.ReleaseReservedStockAsync(articleId, quantity);
                return success 
                    ? ServiceResult<bool>.SuccessResult(true, "Stock libéré avec succès")
                    : ServiceResult<bool>.ErrorResult("Échec de la libération du stock");
            }
            catch (Exception ex)
            {
                return ServiceResult<bool>.ErrorResult(ex, "Erreur lors de la libération du stock");
            }
        }

        public async Task<ServiceResult<bool>> UpdatePMPAsync(int articleId, decimal newPMP)
        {
            try
            {
                if (newPMP < 0)
                {
                    return ServiceResult<bool>.ErrorResult("Le PMP ne peut pas être négatif");
                }

                var success = await _articleRepository.UpdatePMPAsync(articleId, newPMP);
                return success 
                    ? ServiceResult<bool>.SuccessResult(true, "PMP mis à jour avec succès")
                    : ServiceResult<bool>.ErrorResult("Échec de la mise à jour du PMP");
            }
            catch (Exception ex)
            {
                return ServiceResult<bool>.ErrorResult(ex, "Erreur lors de la mise à jour du PMP");
            }
        }

        #endregion

        #region Recherche et filtrage spécialisés

        public async Task<IEnumerable<Article>> GetArticlesWithLowStockAsync()
        {
            try
            {
                return await _articleRepository.GetArticlesWithLowStockAsync();
            }
            catch (Exception ex)
            {
                throw new ServiceException("Erreur lors de la récupération des articles avec stock faible", ex);
            }
        }

        public async Task<IEnumerable<Article>> GetArticlesOutOfStockAsync()
        {
            try
            {
                return await _articleRepository.GetArticlesOutOfStockAsync();
            }
            catch (Exception ex)
            {
                throw new ServiceException("Erreur lors de la récupération des articles en rupture", ex);
            }
        }

        public async Task<IEnumerable<Article>> GetArticlesByCategoryAsync(int categoryId)
        {
            try
            {
                return await _articleRepository.GetArticlesByCategoryAsync(categoryId);
            }
            catch (Exception ex)
            {
                throw new ServiceException($"Erreur lors de la récupération des articles de la catégorie {categoryId}", ex);
            }
        }

        public async Task<IEnumerable<Article>> GetArticlesExpiringAsync(int daysFromNow = 30)
        {
            try
            {
                return await _articleRepository.GetArticlesExpiringAsync(daysFromNow);
            }
            catch (Exception ex)
            {
                throw new ServiceException($"Erreur lors de la récupération des articles expirant dans {daysFromNow} jours", ex);
            }
        }

        public async Task<Article> GetByBarcodeAsync(string barcode)
        {
            try
            {
                if (string.IsNullOrEmpty(barcode))
                {
                    throw new ArgumentException("Le code-barre ne peut pas être vide");
                }

                return await _articleRepository.GetByBarcodeAsync(barcode);
            }
            catch (Exception ex)
            {
                throw new ServiceException($"Erreur lors de la recherche par code-barre '{barcode}'", ex);
            }
        }

        #endregion

        #region Statistiques et rapports

        public async Task<decimal> CalculateStockValueAsync()
        {
            try
            {
                return await _articleRepository.CalculateStockValueAsync();
            }
            catch (Exception ex)
            {
                throw new ServiceException("Erreur lors du calcul de la valeur du stock", ex);
            }
        }

        public async Task<IEnumerable<Article>> GetArticlesForInventoryAsync()
        {
            try
            {
                return await _articleRepository.GetArticlesForInventoryAsync();
            }
            catch (Exception ex)
            {
                throw new ServiceException("Erreur lors de la récupération des articles pour inventaire", ex);
            }
        }

        public async Task<IEnumerable<object>> GetStockStatisticsAsync()
        {
            try
            {
                // Implémentation des statistiques de stock
                var totalArticles = await GetCountAsync();
                var articlesActifs = (await GetActiveAsync()).Count();
                var articlesStockFaible = (await GetArticlesWithLowStockAsync()).Count();
                var articlesRupture = (await GetArticlesOutOfStockAsync()).Count();
                var valeurStock = await CalculateStockValueAsync();

                return new[]
                {
                    new { Nom = "Total Articles", Valeur = totalArticles },
                    new { Nom = "Articles Actifs", Valeur = articlesActifs },
                    new { Nom = "Stock Faible", Valeur = articlesStockFaible },
                    new { Nom = "Rupture de Stock", Valeur = articlesRupture },
                    new { Nom = "Valeur Stock (DA)", Valeur = valeurStock }
                };
            }
            catch (Exception ex)
            {
                throw new ServiceException("Erreur lors du calcul des statistiques de stock", ex);
            }
        }

        public async Task<IEnumerable<object>> GetTopSellingArticlesAsync(int count = 10, DateTime? dateDebut = null, DateTime? dateFin = null)
        {
            try
            {
                // Cette méthode nécessiterait une requête complexe impliquant les détails de factures
                // Pour l'instant, retourner une implémentation basique
                return new List<object>();
            }
            catch (Exception ex)
            {
                throw new ServiceException("Erreur lors de la récupération des articles les plus vendus", ex);
            }
        }

        #endregion

        #region Gestion des codes-barres

        public async Task<ServiceResult<bool>> AddBarcodeAsync(int articleId, string barcode, string typeCode = "EAN13")
        {
            try
            {
                if (string.IsNullOrEmpty(barcode))
                {
                    return ServiceResult<bool>.ErrorResult("Le code-barre ne peut pas être vide");
                }

                // Vérifier si l'article existe
                var article = await GetByIdAsync(articleId);
                if (article == null)
                {
                    return ServiceResult<bool>.ErrorResult("Article non trouvé");
                }

                // Vérifier si le code-barre existe déjà
                var existingArticle = await GetByBarcodeAsync(barcode);
                if (existingArticle != null)
                {
                    return ServiceResult<bool>.ErrorResult("Ce code-barre est déjà utilisé par un autre article");
                }

                // Ajouter le code-barre (implémentation à faire dans le repository)
                // Pour l'instant, retourner succès
                return ServiceResult<bool>.SuccessResult(true, "Code-barre ajouté avec succès");
            }
            catch (Exception ex)
            {
                return ServiceResult<bool>.ErrorResult(ex, "Erreur lors de l'ajout du code-barre");
            }
        }

        public async Task<ServiceResult<bool>> RemoveBarcodeAsync(int articleId, string barcode)
        {
            try
            {
                // Implémentation à faire
                return ServiceResult<bool>.SuccessResult(true, "Code-barre supprimé avec succès");
            }
            catch (Exception ex)
            {
                return ServiceResult<bool>.ErrorResult(ex, "Erreur lors de la suppression du code-barre");
            }
        }

        public async Task<IEnumerable<CodeBarre>> GetBarcodesAsync(int articleId)
        {
            try
            {
                // Implémentation à faire dans le repository
                return new List<CodeBarre>();
            }
            catch (Exception ex)
            {
                throw new ServiceException($"Erreur lors de la récupération des codes-barres pour l'article {articleId}", ex);
            }
        }

        #endregion

        #region Validation métier

        public async Task<ServiceResult<bool>> ValidateStockOperationAsync(int articleId, decimal quantity, string operation)
        {
            try
            {
                var errors = new List<string>();

                // Vérifier si l'article existe
                var article = await GetByIdAsync(articleId);
                if (article == null)
                {
                    errors.Add("Article non trouvé");
                    return ServiceResult<bool>.ErrorResult("Validation échouée", errors);
                }

                // Vérifier si l'article est actif
                if (article.Statut != "Actif")
                {
                    errors.Add("L'article n'est pas actif");
                }

                // Validation selon l'opération
                switch (operation.ToLower())
                {
                    case "réservation":
                        if (quantity <= 0)
                        {
                            errors.Add("La quantité à réserver doit être positive");
                        }
                        if (article.StockDisponible < quantity)
                        {
                            errors.Add($"Stock insuffisant. Disponible: {article.StockDisponible}, Demandé: {quantity}");
                        }
                        break;

                    case "ajustement":
                        if (quantity < 0)
                        {
                            errors.Add("Le stock ne peut pas être négatif");
                        }
                        break;
                }

                return errors.Count == 0 
                    ? ServiceResult<bool>.SuccessResult(true, "Validation réussie")
                    : ServiceResult<bool>.ErrorResult("Validation échouée", errors);
            }
            catch (Exception ex)
            {
                return ServiceResult<bool>.ErrorResult(ex, "Erreur lors de la validation de l'opération de stock");
            }
        }

        public async Task<ServiceResult<bool>> ValidatePricesAsync(Article article)
        {
            try
            {
                var errors = new List<string>();

                if (article.PrixAchatUnitaire < 0)
                {
                    errors.Add("Le prix d'achat ne peut pas être négatif");
                }

                if (article.PrixVenteUnitaire < 0)
                {
                    errors.Add("Le prix de vente ne peut pas être négatif");
                }

                if (article.PrixVenteUnitaire <= article.PrixAchatUnitaire)
                {
                    errors.Add("Le prix de vente doit être supérieur au prix d'achat");
                }

                if (article.PrixVenteGros > 0 && article.PrixVenteGros >= article.PrixVenteUnitaire)
                {
                    errors.Add("Le prix de vente en gros doit être inférieur au prix de vente unitaire");
                }

                return errors.Count == 0 
                    ? ServiceResult<bool>.SuccessResult(true, "Validation des prix réussie")
                    : ServiceResult<bool>.ErrorResult("Validation des prix échouée", errors);
            }
            catch (Exception ex)
            {
                return ServiceResult<bool>.ErrorResult(ex, "Erreur lors de la validation des prix");
            }
        }

        #endregion

        #region Validation métier spécialisée

        protected override async Task<ServiceResult<bool>> ValidateBusinessRulesForAddAsync(Article entity)
        {
            var errors = new List<string>();

            // Validation des prix
            var priceValidation = await ValidatePricesAsync(entity);
            if (!priceValidation.Success)
            {
                errors.AddRange(priceValidation.Errors);
            }

            // Validation du stock
            if (entity.StockMinimum < 0)
            {
                errors.Add("Le stock minimum ne peut pas être négatif");
            }

            if (entity.StockMaximum > 0 && entity.StockMaximum <= entity.StockMinimum)
            {
                errors.Add("Le stock maximum doit être supérieur au stock minimum");
            }

            // Validation de la marge
            if (entity.MargeBeneficiaire < 0 || entity.MargeBeneficiaire > 1000)
            {
                errors.Add("La marge bénéficiaire doit être entre 0 et 1000%");
            }

            return errors.Count == 0 
                ? ServiceResult<bool>.SuccessResult(true)
                : ServiceResult<bool>.ErrorResult("Validation métier échouée", errors);
        }

        protected override async Task<ServiceResult<bool>> ValidateBusinessRulesForUpdateAsync(Article entity)
        {
            return await ValidateBusinessRulesForAddAsync(entity);
        }

        protected override async Task<ServiceResult<bool>> ValidateBusinessRulesForDeleteAsync(int id)
        {
            var errors = new List<string>();

            // Vérifier s'il y a des mouvements de stock récents
            // Vérifier s'il y a des factures en cours
            // etc.

            return errors.Count == 0 
                ? ServiceResult<bool>.SuccessResult(true)
                : ServiceResult<bool>.ErrorResult("Validation métier pour suppression échouée", errors);
        }

        #endregion
    }
}
