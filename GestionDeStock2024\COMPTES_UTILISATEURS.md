# 👥 Comptes Utilisateurs - Gestion Supermarché DZ

## 🔐 Connexions Disponibles

### Administrateur
- **Nom d'utilisateur :** `admin`
- **Mot de passe :** `admin123`
- **R<PERSON>le :** Administrateur
- **Accès :** Complet (tous les modules)

### Gestionnaire
- **Nom d'utilisateur :** `gestionnaire`
- **Mot de passe :** `manager123`
- **Rôle :** Gestionnaire
- **Accès :** Gestion complète sauf paramètres système

### Vendeur
- **Nom d'utilisateur :** `vendeur`
- **Mot de passe :** `vendeur123`
- **Rôle :** Vendeur
- **Accès :** Ventes, clients, consultation stock

### Caissier
- **Nom d'utilisateur :** `caissier`
- **Mot de passe :** `caissier123`
- **Rôle :** Caissier
- **Accès :** Point de vente, encaissements

### Magasinier
- **Nom d'utilisateur :** `magasinier`
- **Mot de passe :** `stock123`
- **Rôle :** Magasinier
- **Accès :** Gestion stock, inventaires, réceptions

## ⚠️ Important

- **Mots de passe en clair :** Le système utilise des mots de passe non chiffrés pour simplifier les tests
- **Base de données :** Assurez-vous que la base de données est créée et que les utilisateurs sont insérés
- **Script SQL :** Utilisez `DB/CreateDefaultUsers.sql` pour créer les comptes

## 🚀 Première Connexion

1. Lancez l'application
2. Utilisez `admin` / `admin123` pour la première connexion
3. Configurez la base de données si nécessaire
4. Créez les autres utilisateurs via le script SQL

## 📝 Notes de Développement

- Aucun hachage de mot de passe
- Comparaison directe en base de données
- Système simplifié pour les tests et le développement
- Prêt pour l'ajout de sécurité en production si nécessaire
