using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using Dapper;
using System.Text;
using System.Reflection;
using System.ComponentModel.DataAnnotations;

namespace GestionDeStock2024.DATA
{
    /// <summary>
    /// Repository de base implémentant les opérations CRUD communes avec Dapper
    /// </summary>
    /// <typeparam name="T">Type de l'entité</typeparam>
    public abstract class BaseRepository<T> : IFullRepository<T> where T : class
    {
        protected readonly DatabaseConnection _dbConnection;
        protected readonly string _tableName;
        protected readonly string _primaryKey;

        protected BaseRepository(string tableName, string primaryKey = "Id")
        {
            _dbConnection = new DatabaseConnection();
            _tableName = tableName ?? throw new ArgumentNullException(nameof(tableName));
            _primaryKey = primaryKey ?? throw new ArgumentNullException(nameof(primaryKey));
        }

        #region Opérations de base

        public virtual async Task<T> GetByIdAsync(int id)
        {
            using var connection = await _dbConnection.GetConnectionAsync();
            var sql = $"SELECT * FROM {_tableName} WHERE {_primaryKey} = @Id";
            return await connection.QueryFirstOrDefaultAsync<T>(sql, new { Id = id });
        }

        public virtual async Task<IEnumerable<T>> GetAllAsync()
        {
            using var connection = await _dbConnection.GetConnectionAsync();
            var sql = $"SELECT * FROM {_tableName} ORDER BY {_primaryKey}";
            return await connection.QueryAsync<T>(sql);
        }

        public virtual async Task<IEnumerable<T>> GetPagedAsync(int page, int pageSize)
        {
            using var connection = await _dbConnection.GetConnectionAsync();
            var offset = (page - 1) * pageSize;
            var sql = $"SELECT * FROM {_tableName} ORDER BY {_primaryKey} LIMIT @PageSize OFFSET @Offset";
            return await connection.QueryAsync<T>(sql, new { PageSize = pageSize, Offset = offset });
        }

        public virtual async Task<int> GetCountAsync()
        {
            using var connection = await _dbConnection.GetConnectionAsync();
            var sql = $"SELECT COUNT(*) FROM {_tableName}";
            return await connection.QuerySingleAsync<int>(sql);
        }

        #endregion

        #region Opérations CRUD

        public virtual async Task<int> AddAsync(T entity)
        {
            using var connection = await _dbConnection.GetConnectionAsync();
            var insertSql = GenerateInsertSql();
            return await connection.QuerySingleAsync<int>(insertSql, entity);
        }

        public virtual async Task<bool> UpdateAsync(T entity)
        {
            using var connection = await _dbConnection.GetConnectionAsync();
            var updateSql = GenerateUpdateSql();
            var rowsAffected = await connection.ExecuteAsync(updateSql, entity);
            return rowsAffected > 0;
        }

        public virtual async Task<bool> DeleteAsync(int id)
        {
            using var connection = await _dbConnection.GetConnectionAsync();
            var sql = $"DELETE FROM {_tableName} WHERE {_primaryKey} = @Id";
            var rowsAffected = await connection.ExecuteAsync(sql, new { Id = id });
            return rowsAffected > 0;
        }

        public virtual async Task<bool> DeleteAsync(T entity)
        {
            var idProperty = typeof(T).GetProperty(_primaryKey);
            if (idProperty == null) return false;
            
            var id = idProperty.GetValue(entity);
            return await DeleteAsync(Convert.ToInt32(id));
        }

        #endregion

        #region Recherche et filtrage

        public virtual async Task<IEnumerable<T>> FindAsync(string whereClause, object parameters = null)
        {
            using var connection = await _dbConnection.GetConnectionAsync();
            var sql = $"SELECT * FROM {_tableName} WHERE {whereClause}";
            return await connection.QueryAsync<T>(sql, parameters);
        }

        public virtual async Task<T> FindFirstAsync(string whereClause, object parameters = null)
        {
            using var connection = await _dbConnection.GetConnectionAsync();
            var sql = $"SELECT * FROM {_tableName} WHERE {whereClause} LIMIT 1";
            return await connection.QueryFirstOrDefaultAsync<T>(sql, parameters);
        }

        public virtual async Task<int> CountAsync(string whereClause, object parameters = null)
        {
            using var connection = await _dbConnection.GetConnectionAsync();
            var sql = $"SELECT COUNT(*) FROM {_tableName} WHERE {whereClause}";
            return await connection.QuerySingleAsync<int>(sql, parameters);
        }

        #endregion

        #region Opérations en lot

        public virtual async Task<bool> AddRangeAsync(IEnumerable<T> entities)
        {
            using var connection = await _dbConnection.GetConnectionAsync();
            using var transaction = connection.BeginTransaction();
            
            try
            {
                var insertSql = GenerateInsertSql();
                foreach (var entity in entities)
                {
                    await connection.ExecuteAsync(insertSql, entity, transaction);
                }
                transaction.Commit();
                return true;
            }
            catch
            {
                transaction.Rollback();
                return false;
            }
        }

        public virtual async Task<bool> UpdateRangeAsync(IEnumerable<T> entities)
        {
            using var connection = await _dbConnection.GetConnectionAsync();
            using var transaction = connection.BeginTransaction();
            
            try
            {
                var updateSql = GenerateUpdateSql();
                foreach (var entity in entities)
                {
                    await connection.ExecuteAsync(updateSql, entity, transaction);
                }
                transaction.Commit();
                return true;
            }
            catch
            {
                transaction.Rollback();
                return false;
            }
        }

        public virtual async Task<bool> DeleteRangeAsync(IEnumerable<int> ids)
        {
            using var connection = await _dbConnection.GetConnectionAsync();
            var sql = $"DELETE FROM {_tableName} WHERE {_primaryKey} IN @Ids";
            var rowsAffected = await connection.ExecuteAsync(sql, new { Ids = ids });
            return rowsAffected > 0;
        }

        #endregion

        #region Vérifications d'existence

        public virtual async Task<bool> ExistsAsync(int id)
        {
            using var connection = await _dbConnection.GetConnectionAsync();
            var sql = $"SELECT COUNT(*) FROM {_tableName} WHERE {_primaryKey} = @Id";
            var count = await connection.QuerySingleAsync<int>(sql, new { Id = id });
            return count > 0;
        }

        public virtual async Task<bool> ExistsAsync(string whereClause, object parameters = null)
        {
            using var connection = await _dbConnection.GetConnectionAsync();
            var sql = $"SELECT COUNT(*) FROM {_tableName} WHERE {whereClause}";
            var count = await connection.QuerySingleAsync<int>(sql, parameters);
            return count > 0;
        }

        #endregion

        #region ICodeRepository

        public virtual async Task<T> GetByCodeAsync(string code)
        {
            return await FindFirstAsync("Code = @Code", new { Code = code });
        }

        public virtual async Task<bool> CodeExistsAsync(string code, int? excludeId = null)
        {
            var whereClause = "Code = @Code";
            object parameters = new { Code = code };

            if (excludeId.HasValue)
            {
                whereClause += $" AND {_primaryKey} != @ExcludeId";
                parameters = new { Code = code, ExcludeId = excludeId.Value };
            }

            return await ExistsAsync(whereClause, parameters);
        }

        public virtual async Task<string> GenerateNextCodeAsync(string prefix = "")
        {
            using var connection = await _dbConnection.GetConnectionAsync();
            var sql = $"SELECT COALESCE(MAX(CAST(SUBSTRING(Code, {prefix.Length + 1}) AS UNSIGNED)), 0) + 1 FROM {_tableName} WHERE Code LIKE @Pattern";
            var nextNumber = await connection.QuerySingleAsync<int>(sql, new { Pattern = $"{prefix}%" });
            return $"{prefix}{nextNumber:D6}";
        }

        #endregion

        #region IStatutRepository

        public virtual async Task<IEnumerable<T>> GetActiveAsync()
        {
            return await FindAsync("Statut = @Statut", new { Statut = "Actif" });
        }

        public virtual async Task<IEnumerable<T>> GetInactiveAsync()
        {
            return await FindAsync("Statut = @Statut", new { Statut = "Inactif" });
        }

        public virtual async Task<bool> ActivateAsync(int id)
        {
            return await ChangeStatusAsync(id, "Actif");
        }

        public virtual async Task<bool> DeactivateAsync(int id)
        {
            return await ChangeStatusAsync(id, "Inactif");
        }

        public virtual async Task<bool> ChangeStatusAsync(int id, string newStatus)
        {
            using var connection = await _dbConnection.GetConnectionAsync();
            var sql = $"UPDATE {_tableName} SET Statut = @Status, UpdatedAt = @UpdatedAt WHERE {_primaryKey} = @Id";
            var rowsAffected = await connection.ExecuteAsync(sql, new { Status = newStatus, UpdatedAt = DateTime.Now, Id = id });
            return rowsAffected > 0;
        }

        public virtual async Task<IEnumerable<T>> GetActiveByCodeAsync(string codePattern)
        {
            return await FindAsync("Statut = @Statut AND Code LIKE @Pattern", new { Statut = "Actif", Pattern = $"%{codePattern}%" });
        }

        #endregion

        #region ISearchableRepository

        public virtual async Task<IEnumerable<T>> SearchAsync(string searchTerm, string[] searchFields = null)
        {
            if (string.IsNullOrEmpty(searchTerm)) return await GetAllAsync();

            var fields = searchFields ?? GetSearchableFields();
            var whereClause = BuildSearchWhereClause(fields);
            var parameters = new { SearchTerm = $"%{searchTerm}%" };

            return await FindAsync(whereClause, parameters);
        }

        public virtual async Task<IEnumerable<T>> SearchPagedAsync(string searchTerm, int page, int pageSize, string[] searchFields = null)
        {
            if (string.IsNullOrEmpty(searchTerm)) return await GetPagedAsync(page, pageSize);

            using var connection = await _dbConnection.GetConnectionAsync();
            var fields = searchFields ?? GetSearchableFields();
            var whereClause = BuildSearchWhereClause(fields);
            var offset = (page - 1) * pageSize;
            
            var sql = $"SELECT * FROM {_tableName} WHERE {whereClause} ORDER BY {_primaryKey} LIMIT @PageSize OFFSET @Offset";
            var parameters = new { SearchTerm = $"%{searchTerm}%", PageSize = pageSize, Offset = offset };

            return await connection.QueryAsync<T>(sql, parameters);
        }

        public virtual async Task<int> SearchCountAsync(string searchTerm, string[] searchFields = null)
        {
            if (string.IsNullOrEmpty(searchTerm)) return await GetCountAsync();

            var fields = searchFields ?? GetSearchableFields();
            var whereClause = BuildSearchWhereClause(fields);
            var parameters = new { SearchTerm = $"%{searchTerm}%" };

            return await CountAsync(whereClause, parameters);
        }

        #endregion

        #region ISortableRepository

        public virtual async Task<IEnumerable<T>> GetSortedAsync(string sortField, bool ascending = true)
        {
            using var connection = await _dbConnection.GetConnectionAsync();
            var direction = ascending ? "ASC" : "DESC";
            var sql = $"SELECT * FROM {_tableName} ORDER BY {sortField} {direction}";
            return await connection.QueryAsync<T>(sql);
        }

        public virtual async Task<IEnumerable<T>> GetPagedSortedAsync(int page, int pageSize, string sortField, bool ascending = true)
        {
            using var connection = await _dbConnection.GetConnectionAsync();
            var direction = ascending ? "ASC" : "DESC";
            var offset = (page - 1) * pageSize;
            var sql = $"SELECT * FROM {_tableName} ORDER BY {sortField} {direction} LIMIT @PageSize OFFSET @Offset";
            return await connection.QueryAsync<T>(sql, new { PageSize = pageSize, Offset = offset });
        }

        #endregion

        #region Méthodes utilitaires

        protected virtual string GenerateInsertSql()
        {
            var properties = GetInsertableProperties();
            var columns = string.Join(", ", properties.Select(p => GetColumnName(p)));
            var values = string.Join(", ", properties.Select(p => $"@{p.Name}"));
            
            return $"INSERT INTO {_tableName} ({columns}) VALUES ({values}); SELECT LAST_INSERT_ID();";
        }

        protected virtual string GenerateUpdateSql()
        {
            var properties = GetUpdatableProperties();
            var setClause = string.Join(", ", properties.Select(p => $"{GetColumnName(p)} = @{p.Name}"));
            
            return $"UPDATE {_tableName} SET {setClause} WHERE {_primaryKey} = @{_primaryKey}";
        }

        protected virtual PropertyInfo[] GetInsertableProperties()
        {
            return typeof(T).GetProperties()
                .Where(p => p.Name != _primaryKey && p.CanWrite && !IsNavigationProperty(p))
                .ToArray();
        }

        protected virtual PropertyInfo[] GetUpdatableProperties()
        {
            var properties = GetInsertableProperties().ToList();
            
            // Ajouter UpdatedAt si elle existe
            var updatedAtProperty = typeof(T).GetProperty("UpdatedAt");
            if (updatedAtProperty != null && !properties.Contains(updatedAtProperty))
            {
                properties.Add(updatedAtProperty);
            }

            return properties.ToArray();
        }

        protected virtual string[] GetSearchableFields()
        {
            return typeof(T).GetProperties()
                .Where(p => p.PropertyType == typeof(string) && !IsNavigationProperty(p))
                .Select(p => GetColumnName(p))
                .ToArray();
        }

        protected virtual string BuildSearchWhereClause(string[] fields)
        {
            if (fields == null || fields.Length == 0) return "1=1";
            
            var conditions = fields.Select(field => $"{field} LIKE @SearchTerm");
            return $"({string.Join(" OR ", conditions)})";
        }

        protected virtual string GetColumnName(PropertyInfo property)
        {
            // Convertir PascalCase en snake_case pour MySQL
            var name = property.Name;
            var result = new StringBuilder();
            
            for (int i = 0; i < name.Length; i++)
            {
                if (i > 0 && char.IsUpper(name[i]))
                {
                    result.Append('_');
                }
                result.Append(char.ToLower(name[i]));
            }
            
            return result.ToString();
        }

        protected virtual bool IsNavigationProperty(PropertyInfo property)
        {
            return property.PropertyType.IsClass && 
                   property.PropertyType != typeof(string) && 
                   property.PropertyType != typeof(byte[]) &&
                   !property.PropertyType.IsValueType;
        }

        #endregion

        #region IRelationalRepository - Implémentation de base

        public virtual async Task<T> GetWithRelationsAsync(int id, params string[] includes)
        {
            // Implémentation de base - peut être surchargée dans les repositories spécifiques
            return await GetByIdAsync(id);
        }

        public virtual async Task<IEnumerable<T>> GetAllWithRelationsAsync(params string[] includes)
        {
            // Implémentation de base - peut être surchargée dans les repositories spécifiques
            return await GetAllAsync();
        }

        public virtual async Task<IEnumerable<T>> GetPagedWithRelationsAsync(int page, int pageSize, params string[] includes)
        {
            // Implémentation de base - peut être surchargée dans les repositories spécifiques
            return await GetPagedAsync(page, pageSize);
        }

        #endregion

        #region ICacheableRepository - Implémentation de base

        public virtual async Task<T> GetByIdCachedAsync(int id, TimeSpan? cacheExpiry = null)
        {
            // Implémentation simple sans cache - peut être améliorée
            return await GetByIdAsync(id);
        }

        public virtual async Task<IEnumerable<T>> GetAllCachedAsync(TimeSpan? cacheExpiry = null)
        {
            // Implémentation simple sans cache - peut être améliorée
            return await GetAllAsync();
        }

        public virtual async Task ClearCacheAsync()
        {
            // Implémentation vide - peut être surchargée
            await Task.CompletedTask;
        }

        public virtual async Task RefreshCacheAsync()
        {
            // Implémentation vide - peut être surchargée
            await Task.CompletedTask;
        }

        #endregion

        #region IAuditableRepository - Implémentation de base

        public virtual async Task<int> AddWithAuditAsync(T entity, int userId)
        {
            // TODO: Implémenter l'audit
            return await AddAsync(entity);
        }

        public virtual async Task<bool> UpdateWithAuditAsync(T entity, int userId)
        {
            // TODO: Implémenter l'audit
            return await UpdateAsync(entity);
        }

        public virtual async Task<bool> DeleteWithAuditAsync(int id, int userId)
        {
            // TODO: Implémenter l'audit
            return await DeleteAsync(id);
        }

        public virtual async Task<IEnumerable<object>> GetAuditHistoryAsync(int entityId)
        {
            // TODO: Implémenter la récupération de l'historique d'audit
            return new List<object>();
        }

        #endregion

        #region IValidatableRepository - Implémentation de base

        public virtual async Task<(bool IsValid, List<string> Errors)> ValidateAsync(T entity)
        {
            var errors = new List<string>();

            // Validation des attributs de validation
            var validationContext = new ValidationContext(entity);
            var validationResults = new List<ValidationResult>();
            var isValid = Validator.TryValidateObject(entity, validationContext, validationResults, true);

            if (!isValid)
            {
                errors.AddRange(validationResults.Select(vr => vr.ErrorMessage));
            }

            return (isValid, errors);
        }

        public virtual async Task<(bool IsValid, List<string> Errors)> ValidateForAddAsync(T entity)
        {
            return await ValidateAsync(entity);
        }

        public virtual async Task<(bool IsValid, List<string> Errors)> ValidateForUpdateAsync(T entity)
        {
            return await ValidateAsync(entity);
        }

        public virtual async Task<(bool IsValid, List<string> Errors)> ValidateForDeleteAsync(int id)
        {
            var errors = new List<string>();
            var exists = await ExistsAsync(id);

            if (!exists)
            {
                errors.Add("L'entité n'existe pas");
            }

            return (exists, errors);
        }

        #endregion

        #region ITransactionalRepository - Implémentation de base

        public virtual async Task<TResult> ExecuteInTransactionAsync<TResult>(Func<Task<TResult>> operation)
        {
            return await _dbConnection.ExecuteInTransactionAsync(async (connection, transaction) =>
            {
                return await operation();
            });
        }

        public virtual async Task ExecuteInTransactionAsync(Func<Task> operation)
        {
            await _dbConnection.ExecuteInTransactionAsync(async (connection, transaction) =>
            {
                await operation();
            });
        }

        public virtual async Task<bool> BulkOperationAsync(Func<Task<bool>> operation)
        {
            return await ExecuteInTransactionAsync(operation);
        }

        #endregion

        public void Dispose()
        {
            _dbConnection?.Dispose();
        }
    }
}
