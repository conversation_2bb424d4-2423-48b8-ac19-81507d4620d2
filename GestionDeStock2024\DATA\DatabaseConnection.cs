using MySqlConnector;
using System;
using System.Configuration;
using System.Data;
using System.Threading.Tasks;

namespace GestionDeStock2024.DATA
{
    /// <summary>
    /// Gestionnaire de connexion à la base de données MySQL
    /// </summary>
    public class DatabaseConnection : IDisposable
    {
        private readonly string _connectionString;
        private MySqlConnection _connection;
        private MySqlTransaction _transaction;
        private bool _disposed = false;

        public DatabaseConnection()
        {
            _connectionString = GetConnectionString();
        }

        public DatabaseConnection(string connectionString)
        {
            _connectionString = connectionString ?? throw new ArgumentNullException(nameof(connectionString));
        }

        /// <summary>
        /// Obtient la chaîne de connexion depuis le fichier de configuration
        /// </summary>
        private string GetConnectionString()
        {
            var connectionString = ConfigurationManager.ConnectionStrings["DefaultConnection"]?.ConnectionString;

            if (string.IsNullOrEmpty(connectionString))
            {
                // Chaîne de connexion par défaut pour le développement
                connectionString = "Server=localhost;Database=gestion_supermarche_dz;Uid=root;Pwd=;Charset=utf8mb4;";
            }

            return connectionString;
        }

        /// <summary>
        /// Obtient une connexion ouverte à la base de données
        /// </summary>
        public async Task<IDbConnection> GetConnectionAsync()
        {
            if (_connection == null)
            {
                _connection = new MySqlConnection(_connectionString);
            }

            if (_connection.State != ConnectionState.Open)
            {
                await _connection.OpenAsync();
            }

            return _connection;
        }

        /// <summary>
        /// Obtient une connexion synchrone (pour compatibilité)
        /// </summary>
        public IDbConnection GetConnection()
        {
            if (_connection == null)
            {
                _connection = new MySqlConnection(_connectionString);
            }

            if (_connection.State != ConnectionState.Open)
            {
                _connection.Open();
            }

            return _connection;
        }

        /// <summary>
        /// Démarre une transaction
        /// </summary>
        public async Task<IDbTransaction> BeginTransactionAsync()
        {
            var connection = await GetConnectionAsync();
            _transaction = (MySqlTransaction)connection.BeginTransaction();
            return _transaction;
        }

        /// <summary>
        /// Démarre une transaction synchrone
        /// </summary>
        public IDbTransaction BeginTransaction()
        {
            var connection = GetConnection();
            _transaction = (MySqlTransaction)connection.BeginTransaction();
            return _transaction;
        }

        /// <summary>
        /// Valide la transaction en cours
        /// </summary>
        public async Task CommitTransactionAsync()
        {
            if (_transaction != null)
            {
                await _transaction.CommitAsync();
                _transaction.Dispose();
                _transaction = null;
            }
        }

        /// <summary>
        /// Annule la transaction en cours
        /// </summary>
        public async Task RollbackTransactionAsync()
        {
            if (_transaction != null)
            {
                await _transaction.RollbackAsync();
                _transaction.Dispose();
                _transaction = null;
            }
        }

        /// <summary>
        /// Teste la connexion à la base de données
        /// </summary>
        public async Task<bool> TestConnectionAsync()
        {
            try
            {
                using (var connection = new MySqlConnection(_connectionString))
                {
                    await connection.OpenAsync();
                    return connection.State == ConnectionState.Open;
                }
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Obtient les informations sur la base de données
        /// </summary>
        public async Task<DatabaseInfo> GetDatabaseInfoAsync()
        {
            try
            {
                using (var connection = new MySqlConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    var info = new DatabaseInfo
                    {
                        ServerVersion = connection.ServerVersion,
                        Database = connection.Database,
                        DataSource = connection.DataSource,
                        ConnectionTimeout = connection.ConnectionTimeout,
                        State = connection.State.ToString()
                    };

                    return info;
                }
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException("Impossible d'obtenir les informations de la base de données", ex);
            }
        }

        /// <summary>
        /// Exécute une requête de test pour vérifier la structure de la base
        /// </summary>
        public async Task<bool> ValidateDatabaseStructureAsync()
        {
            try
            {
                using (var connection = new MySqlConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    // Vérifier l'existence de quelques tables principales
                    var command = new MySqlCommand(@"
                        SELECT COUNT(*) FROM information_schema.tables 
                        WHERE table_schema = DATABASE() 
                        AND table_name IN ('articles', 'clients', 'fournisseurs', 'factures_vente', 'factures_achat')",
                        connection);

                    var count = Convert.ToInt32(await command.ExecuteScalarAsync());
                    return count >= 5; // Au moins 5 tables principales doivent exister
                }
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Crée une nouvelle instance de connexion (pour utilisation dans les repositories)
        /// </summary>
        public static async Task<IDbConnection> CreateConnectionAsync()
        {
            var dbConnection = new DatabaseConnection();
            return await dbConnection.GetConnectionAsync();
        }

        /// <summary>
        /// Crée une nouvelle instance de connexion synchrone
        /// </summary>
        public static IDbConnection CreateConnection()
        {
            var dbConnection = new DatabaseConnection();
            return dbConnection.GetConnection();
        }

        /// <summary>
        /// Exécute une opération dans une transaction
        /// </summary>
        public async Task<T> ExecuteInTransactionAsync<T>(Func<IDbConnection, IDbTransaction, Task<T>> operation)
        {
            var connection = await GetConnectionAsync();
            var transaction = await BeginTransactionAsync();

            try
            {
                var result = await operation(connection, transaction);
                await CommitTransactionAsync();
                return result;
            }
            catch
            {
                await RollbackTransactionAsync();
                throw;
            }
        }

        /// <summary>
        /// Exécute une opération dans une transaction (sans retour)
        /// </summary>
        public async Task ExecuteInTransactionAsync(Func<IDbConnection, IDbTransaction, Task> operation)
        {
            var connection = await GetConnectionAsync();
            var transaction = await BeginTransactionAsync();

            try
            {
                await operation(connection, transaction);
                await CommitTransactionAsync();
            }
            catch
            {
                await RollbackTransactionAsync();
                throw;
            }
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                _transaction?.Dispose();
                _connection?.Dispose();
                _disposed = true;
            }
        }
    }

    /// <summary>
    /// Informations sur la base de données
    /// </summary>
    public class DatabaseInfo
    {
        public string ServerVersion { get; set; }
        public string Database { get; set; }
        public string DataSource { get; set; }
        public int ConnectionTimeout { get; set; }
        public string State { get; set; }
        public DateTime RetrievedAt { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// Factory pour créer des connexions à la base de données
    /// </summary>
    public static class DatabaseConnectionFactory
    {
        private static string _connectionString;

        /// <summary>
        /// Configure la chaîne de connexion globale
        /// </summary>
        public static void Configure(string connectionString)
        {
            _connectionString = connectionString ?? throw new ArgumentNullException(nameof(connectionString));
        }

        /// <summary>
        /// Crée une nouvelle connexion
        /// </summary>
        public static async Task<IDbConnection> CreateAsync()
        {
            var connection = new MySqlConnection(_connectionString ?? GetDefaultConnectionString());
            await connection.OpenAsync();
            return connection;
        }

        /// <summary>
        /// Crée une nouvelle connexion synchrone
        /// </summary>
        public static IDbConnection Create()
        {
            var connection = new MySqlConnection(_connectionString ?? GetDefaultConnectionString());
            connection.Open();
            return connection;
        }

        /// <summary>
        /// Obtient la chaîne de connexion par défaut
        /// </summary>
        private static string GetDefaultConnectionString()
        {
            return ConfigurationManager.ConnectionStrings["DefaultConnection"]?.ConnectionString
                ?? "Server=localhost;Database=gestion_supermarche_dz;Uid=root;Pwd=;Charset=utf8mb4;";
        }

        /// <summary>
        /// Teste la connexion
        /// </summary>
        public static async Task<bool> TestConnectionAsync()
        {
            try
            {
                using (var connection = await CreateAsync())
                {
                    return connection.State == ConnectionState.Open;
                }
            }
            catch
            {
                return false;
            }
        }
    }
}
