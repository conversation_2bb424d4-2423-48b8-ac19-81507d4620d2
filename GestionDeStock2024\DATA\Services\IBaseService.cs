using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace GestionDeStock2024.DATA.Services
{
    /// <summary>
    /// Interface de base pour tous les services
    /// </summary>
    /// <typeparam name="T">Type de l'entité</typeparam>
    public interface IBaseService<T> where T : class
    {
        // Opérations CRUD de base
        Task<T> GetByIdAsync(int id);
        Task<IEnumerable<T>> GetAllAsync();
        Task<IEnumerable<T>> GetPagedAsync(int page, int pageSize);
        Task<int> GetCountAsync();
        
        Task<ServiceResult<int>> AddAsync(T entity);
        Task<ServiceResult<bool>> UpdateAsync(T entity);
        Task<ServiceResult<bool>> DeleteAsync(int id);
        
        // Recherche et filtrage
        Task<IEnumerable<T>> SearchAsync(string searchTerm);
        Task<IEnumerable<T>> GetActiveAsync();
        
        // Validation
        Task<ServiceResult<bool>> ValidateAsync(T entity);
    }

    /// <summary>
    /// Résultat d'une opération de service
    /// </summary>
    /// <typeparam name="T">Type du résultat</typeparam>
    public class ServiceResult<T>
    {
        public bool Success { get; set; }
        public T Data { get; set; }
        public string Message { get; set; }
        public List<string> Errors { get; set; } = new List<string>();
        public Exception Exception { get; set; }

        public static ServiceResult<T> SuccessResult(T data, string message = null)
        {
            return new ServiceResult<T>
            {
                Success = true,
                Data = data,
                Message = message
            };
        }

        public static ServiceResult<T> ErrorResult(string message, List<string> errors = null)
        {
            return new ServiceResult<T>
            {
                Success = false,
                Message = message,
                Errors = errors ?? new List<string>()
            };
        }

        public static ServiceResult<T> ErrorResult(Exception exception, string message = null)
        {
            return new ServiceResult<T>
            {
                Success = false,
                Message = message ?? exception.Message,
                Exception = exception,
                Errors = new List<string> { exception.Message }
            };
        }
    }

    /// <summary>
    /// Résultat simple sans données
    /// </summary>
    public class ServiceResult
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public List<string> Errors { get; set; } = new List<string>();
        public Exception Exception { get; set; }

        public static ServiceResult SuccessResult(string message = null)
        {
            return new ServiceResult
            {
                Success = true,
                Message = message
            };
        }

        public static ServiceResult ErrorResult(string message, List<string> errors = null)
        {
            return new ServiceResult
            {
                Success = false,
                Message = message,
                Errors = errors ?? new List<string>()
            };
        }

        public static ServiceResult ErrorResult(Exception exception, string message = null)
        {
            return new ServiceResult
            {
                Success = false,
                Message = message ?? exception.Message,
                Exception = exception,
                Errors = new List<string> { exception.Message }
            };
        }
    }

    /// <summary>
    /// Interface pour les services avec gestion des codes
    /// </summary>
    /// <typeparam name="T">Type de l'entité</typeparam>
    public interface ICodeService<T> : IBaseService<T> where T : class
    {
        Task<T> GetByCodeAsync(string code);
        Task<bool> CodeExistsAsync(string code, int? excludeId = null);
        Task<string> GenerateNextCodeAsync(string prefix = "");
    }

    /// <summary>
    /// Interface pour les services avec gestion des statuts
    /// </summary>
    /// <typeparam name="T">Type de l'entité</typeparam>
    public interface IStatutService<T> : IBaseService<T> where T : class
    {
        Task<ServiceResult<bool>> ActivateAsync(int id);
        Task<ServiceResult<bool>> DeactivateAsync(int id);
        Task<ServiceResult<bool>> ChangeStatusAsync(int id, string newStatus);
        Task<IEnumerable<T>> GetInactiveAsync();
    }

    /// <summary>
    /// Interface complète combinant toutes les fonctionnalités
    /// </summary>
    /// <typeparam name="T">Type de l'entité</typeparam>
    public interface IFullService<T> : ICodeService<T>, IStatutService<T> where T : class
    {
        // Interface complète héritant de toutes les autres
    }

    /// <summary>
    /// Interface pour les services avec audit
    /// </summary>
    /// <typeparam name="T">Type de l'entité</typeparam>
    public interface IAuditableService<T> : IBaseService<T> where T : class
    {
        Task<ServiceResult<int>> AddWithAuditAsync(T entity, int userId);
        Task<ServiceResult<bool>> UpdateWithAuditAsync(T entity, int userId);
        Task<ServiceResult<bool>> DeleteWithAuditAsync(int id, int userId);
        Task<IEnumerable<object>> GetAuditHistoryAsync(int entityId);
    }

    /// <summary>
    /// Interface pour les services avec cache
    /// </summary>
    /// <typeparam name="T">Type de l'entité</typeparam>
    public interface ICacheableService<T> : IBaseService<T> where T : class
    {
        Task<T> GetByIdCachedAsync(int id);
        Task<IEnumerable<T>> GetAllCachedAsync();
        Task RefreshCacheAsync();
        Task ClearCacheAsync();
    }

    /// <summary>
    /// Interface pour les services avec transactions
    /// </summary>
    /// <typeparam name="T">Type de l'entité</typeparam>
    public interface ITransactionalService<T> : IBaseService<T> where T : class
    {
        Task<ServiceResult<TResult>> ExecuteInTransactionAsync<TResult>(Func<Task<TResult>> operation);
        Task<ServiceResult> ExecuteInTransactionAsync(Func<Task> operation);
    }

    /// <summary>
    /// Interface maître combinant toutes les fonctionnalités
    /// </summary>
    /// <typeparam name="T">Type de l'entité</typeparam>
    public interface IMasterService<T> : 
        IFullService<T>, 
        IAuditableService<T>, 
        ICacheableService<T>, 
        ITransactionalService<T> 
        where T : class
    {
        // Interface maître héritant de toutes les fonctionnalités
    }

    /// <summary>
    /// Contexte d'opération pour les services
    /// </summary>
    public class OperationContext
    {
        public int? UserId { get; set; }
        public string UserName { get; set; }
        public DateTime OperationTime { get; set; } = DateTime.Now;
        public string OperationType { get; set; }
        public Dictionary<string, object> AdditionalData { get; set; } = new Dictionary<string, object>();

        public static OperationContext Create(int userId, string userName, string operationType = null)
        {
            return new OperationContext
            {
                UserId = userId,
                UserName = userName,
                OperationType = operationType
            };
        }
    }

    /// <summary>
    /// Options de pagination
    /// </summary>
    public class PaginationOptions
    {
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 20;
        public string SortField { get; set; }
        public bool SortAscending { get; set; } = true;
        public string SearchTerm { get; set; }
        public Dictionary<string, object> Filters { get; set; } = new Dictionary<string, object>();

        public int Skip => (Page - 1) * PageSize;
    }

    /// <summary>
    /// Résultat paginé
    /// </summary>
    /// <typeparam name="T">Type des éléments</typeparam>
    public class PagedResult<T>
    {
        public IEnumerable<T> Items { get; set; }
        public int TotalCount { get; set; }
        public int Page { get; set; }
        public int PageSize { get; set; }
        public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
        public bool HasNextPage => Page < TotalPages;
        public bool HasPreviousPage => Page > 1;

        public static PagedResult<T> Create(IEnumerable<T> items, int totalCount, int page, int pageSize)
        {
            return new PagedResult<T>
            {
                Items = items,
                TotalCount = totalCount,
                Page = page,
                PageSize = pageSize
            };
        }
    }

    /// <summary>
    /// Interface pour les services avec pagination avancée
    /// </summary>
    /// <typeparam name="T">Type de l'entité</typeparam>
    public interface IPagedService<T> : IBaseService<T> where T : class
    {
        Task<PagedResult<T>> GetPagedAsync(PaginationOptions options);
        Task<PagedResult<T>> SearchPagedAsync(string searchTerm, PaginationOptions options);
    }
}
