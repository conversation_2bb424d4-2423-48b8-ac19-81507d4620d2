using System;
using System.Drawing;
using System.Windows.Forms;

namespace GestionDeStock2024.FORMS
{
    /// <summary>
    /// Formulaire d'ajout/modification d'entrepôt/magasin
    /// </summary>
    public partial class FrmWarehouseAddEdit : Form
    {
        private TextBox txtCode, txtNom, txtAdresse, txtTelephone, txtResponsable, txtCapacite, txtDescription;
        private ComboBox cmbType, cmbStatut;
        private Button btnSave, btnCancel;
        private bool isEditMode = false;
        private string editCode;

        public FrmWarehouseAddEdit()
        {
            InitializeComponent();
            InitializeForm();
        }

        public FrmWarehouseAddEdit(string code) : this()
        {
            isEditMode = true;
            editCode = code;
            LoadWarehouseData();
        }

        private void InitializeForm()
        {
            this.Text = isEditMode ? "Modifier l'entrepôt" : "Ajouter un entrepôt";
            this.Size = new Size(600, 500);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            CreateLayout();
        }

        private void CreateLayout()
        {
            var mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20),
                BackColor = Color.FromArgb(248, 249, 250)
            };
            this.Controls.Add(mainPanel);

            // Titre
            var titleLabel = new Label
            {
                Text = isEditMode ? "✏️ Modifier l'entrepôt" : "➕ Ajouter un entrepôt",
                Font = new Font("Segoe UI", 16, FontStyle.Bold),
                ForeColor = Color.FromArgb(33, 37, 41),
                AutoSize = true,
                Location = new Point(0, 0)
            };
            mainPanel.Controls.Add(titleLabel);

            // Code
            var lblCode = new Label
            {
                Text = "Code *:",
                Location = new Point(0, 50),
                Size = new Size(120, 20),
                Font = new Font("Segoe UI", 10, FontStyle.Bold)
            };
            mainPanel.Controls.Add(lblCode);

            txtCode = new TextBox
            {
                Location = new Point(130, 47),
                Size = new Size(200, 25),
                Font = new Font("Segoe UI", 10),
                MaxLength = 20
            };
            mainPanel.Controls.Add(txtCode);

            // Type
            var lblType = new Label
            {
                Text = "Type *:",
                Location = new Point(350, 50),
                Size = new Size(80, 20),
                Font = new Font("Segoe UI", 10, FontStyle.Bold)
            };
            mainPanel.Controls.Add(lblType);

            cmbType = new ComboBox
            {
                Location = new Point(440, 47),
                Size = new Size(120, 25),
                Font = new Font("Segoe UI", 10),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            cmbType.Items.AddRange(new[] { "Magasin", "Entrepôt", "Dépôt" });
            mainPanel.Controls.Add(cmbType);

            // Nom
            var lblNom = new Label
            {
                Text = "Nom *:",
                Location = new Point(0, 85),
                Size = new Size(120, 20),
                Font = new Font("Segoe UI", 10, FontStyle.Bold)
            };
            mainPanel.Controls.Add(lblNom);

            txtNom = new TextBox
            {
                Location = new Point(130, 82),
                Size = new Size(430, 25),
                Font = new Font("Segoe UI", 10),
                MaxLength = 100
            };
            mainPanel.Controls.Add(txtNom);

            // Adresse
            var lblAdresse = new Label
            {
                Text = "Adresse *:",
                Location = new Point(0, 120),
                Size = new Size(120, 20),
                Font = new Font("Segoe UI", 10, FontStyle.Bold)
            };
            mainPanel.Controls.Add(lblAdresse);

            txtAdresse = new TextBox
            {
                Location = new Point(130, 117),
                Size = new Size(430, 60),
                Font = new Font("Segoe UI", 10),
                Multiline = true,
                MaxLength = 500
            };
            mainPanel.Controls.Add(txtAdresse);

            // Téléphone
            var lblTelephone = new Label
            {
                Text = "Téléphone:",
                Location = new Point(0, 190),
                Size = new Size(120, 20),
                Font = new Font("Segoe UI", 10, FontStyle.Bold)
            };
            mainPanel.Controls.Add(lblTelephone);

            txtTelephone = new TextBox
            {
                Location = new Point(130, 187),
                Size = new Size(200, 25),
                Font = new Font("Segoe UI", 10),
                MaxLength = 20
            };
            mainPanel.Controls.Add(txtTelephone);

            // Capacité
            var lblCapacite = new Label
            {
                Text = "Capacité (m²):",
                Location = new Point(350, 190),
                Size = new Size(80, 20),
                Font = new Font("Segoe UI", 10, FontStyle.Bold)
            };
            mainPanel.Controls.Add(lblCapacite);

            txtCapacite = new TextBox
            {
                Location = new Point(440, 187),
                Size = new Size(120, 25),
                Font = new Font("Segoe UI", 10),
                MaxLength = 10
            };
            mainPanel.Controls.Add(txtCapacite);

            // Responsable
            var lblResponsable = new Label
            {
                Text = "Responsable:",
                Location = new Point(0, 225),
                Size = new Size(120, 20),
                Font = new Font("Segoe UI", 10, FontStyle.Bold)
            };
            mainPanel.Controls.Add(lblResponsable);

            txtResponsable = new TextBox
            {
                Location = new Point(130, 222),
                Size = new Size(200, 25),
                Font = new Font("Segoe UI", 10),
                MaxLength = 100
            };
            mainPanel.Controls.Add(txtResponsable);

            // Statut
            var lblStatut = new Label
            {
                Text = "Statut *:",
                Location = new Point(350, 225),
                Size = new Size(80, 20),
                Font = new Font("Segoe UI", 10, FontStyle.Bold)
            };
            mainPanel.Controls.Add(lblStatut);

            cmbStatut = new ComboBox
            {
                Location = new Point(440, 222),
                Size = new Size(120, 25),
                Font = new Font("Segoe UI", 10),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            cmbStatut.Items.AddRange(new[] { "Actif", "Inactif" });
            cmbStatut.SelectedIndex = 0;
            mainPanel.Controls.Add(cmbStatut);

            // Description
            var lblDescription = new Label
            {
                Text = "Description:",
                Location = new Point(0, 260),
                Size = new Size(120, 20),
                Font = new Font("Segoe UI", 10, FontStyle.Bold)
            };
            mainPanel.Controls.Add(lblDescription);

            txtDescription = new TextBox
            {
                Location = new Point(130, 257),
                Size = new Size(430, 80),
                Font = new Font("Segoe UI", 10),
                Multiline = true,
                MaxLength = 500
            };
            mainPanel.Controls.Add(txtDescription);

            // Boutons
            btnSave = new Button
            {
                Text = "💾 Enregistrer",
                Location = new Point(340, 360),
                Size = new Size(120, 35),
                Font = new Font("Segoe UI", 10, FontStyle.Bold),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                FlatAppearance = { BorderSize = 0 },
                Cursor = Cursors.Hand
            };
            btnSave.Click += BtnSave_Click;
            mainPanel.Controls.Add(btnSave);

            btnCancel = new Button
            {
                Text = "❌ Annuler",
                Location = new Point(470, 360),
                Size = new Size(100, 35),
                Font = new Font("Segoe UI", 10),
                BackColor = Color.FromArgb(108, 117, 125),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                FlatAppearance = { BorderSize = 0 },
                Cursor = Cursors.Hand
            };
            btnCancel.Click += BtnCancel_Click;
            mainPanel.Controls.Add(btnCancel);

            // Note obligatoire
            var lblNote = new Label
            {
                Text = "* Champs obligatoires",
                Location = new Point(0, 410),
                Size = new Size(200, 15),
                Font = new Font("Segoe UI", 8, FontStyle.Italic),
                ForeColor = Color.FromArgb(108, 117, 125)
            };
            mainPanel.Controls.Add(lblNote);
        }

        private void LoadWarehouseData()
        {
            if (isEditMode && !string.IsNullOrEmpty(editCode))
            {
                txtCode.Text = editCode;
                txtCode.ReadOnly = true; // Le code ne peut pas être modifié
                
                // Données fictives pour la démonstration
                switch (editCode)
                {
                    case "MAG001":
                        txtNom.Text = "Magasin Principal Alger";
                        cmbType.SelectedItem = "Magasin";
                        txtAdresse.Text = "Rue Didouche Mourad, Alger Centre";
                        txtTelephone.Text = "021-123456";
                        txtResponsable.Text = "Ahmed Benali";
                        txtCapacite.Text = "500";
                        txtDescription.Text = "Magasin principal situé au centre d'Alger";
                        break;
                    case "ENT001":
                        txtNom.Text = "Entrepôt Central";
                        cmbType.SelectedItem = "Entrepôt";
                        txtAdresse.Text = "Zone Industrielle Rouiba, Alger";
                        txtTelephone.Text = "021-789012";
                        txtResponsable.Text = "Fatima Khelifi";
                        txtCapacite.Text = "2000";
                        txtDescription.Text = "Entrepôt central pour la distribution";
                        break;
                    default:
                        txtNom.Text = "Entrepôt exemple";
                        cmbType.SelectedIndex = 0;
                        txtAdresse.Text = "Adresse exemple";
                        txtTelephone.Text = "";
                        txtResponsable.Text = "";
                        txtCapacite.Text = "0";
                        txtDescription.Text = "";
                        break;
                }
                cmbStatut.SelectedItem = "Actif";
            }
        }

        private bool ValidateForm()
        {
            if (string.IsNullOrWhiteSpace(txtCode.Text))
            {
                MessageBox.Show("Le code est obligatoire", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtCode.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtNom.Text))
            {
                MessageBox.Show("Le nom est obligatoire", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtNom.Focus();
                return false;
            }

            if (cmbType.SelectedIndex == -1)
            {
                MessageBox.Show("Le type est obligatoire", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                cmbType.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtAdresse.Text))
            {
                MessageBox.Show("L'adresse est obligatoire", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtAdresse.Focus();
                return false;
            }

            if (!string.IsNullOrWhiteSpace(txtCapacite.Text))
            {
                if (!decimal.TryParse(txtCapacite.Text, out decimal capacite) || capacite < 0)
                {
                    MessageBox.Show("La capacité doit être un nombre positif", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtCapacite.Focus();
                    return false;
                }
            }

            return true;
        }

        private void BtnSave_Click(object sender, EventArgs e)
        {
            if (ValidateForm())
            {
                // Ici, vous ajouteriez la logique pour sauvegarder en base de données
                MessageBox.Show(
                    isEditMode ? "Entrepôt modifié avec succès" : "Entrepôt ajouté avec succès",
                    "Succès",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private void FrmWarehouseAddEdit_Load(object sender, EventArgs e)
        {
            if (!isEditMode)
            {
                txtCode.Focus();
            }
            else
            {
                txtNom.Focus();
            }
        }
    }
}
