using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace GestionDeStock2024.MODELS.DTOs
{
    /// <summary>
    /// DTO pour l'affichage des clients
    /// </summary>
    public class ClientDTO
    {
        public int Id { get; set; }
        public string Code { get; set; }
        public string NomClient { get; set; }
        public string PrenomClient { get; set; }
        public string RaisonSociale { get; set; }
        public string TypeClient { get; set; }
        public string NomComplet { get; set; }
        public string NomAffichage { get; set; }
        public string Adresse { get; set; }
        public string Ville { get; set; }
        public string Wilaya { get; set; }
        public string CodePostal { get; set; }
        public string AdresseComplete { get; set; }
        public string Telephone { get; set; }
        public string Email { get; set; }
        public string NIF { get; set; }
        public string NIS { get; set; }
        public string RC { get; set; }
        public string ART { get; set; }
        public decimal SoldeInitial { get; set; }
        public decimal SoldeActuel { get; set; }
        public decimal CreditLimite { get; set; }
        public decimal CreditDisponible { get; set; }
        public decimal RemiseHabituelle { get; set; }
        public DateTime? DateNaissance { get; set; }
        public int? Age { get; set; }
        public string Statut { get; set; }
        public bool EstEntreprise { get; set; }
        public bool EstRevendeur { get; set; }
        public bool EstParticulier { get; set; }
        public bool ACredit { get; set; }
        public bool CreditDepasse { get; set; }
        public bool EstValideEntreprise { get; set; }
        public int NombreFactures { get; set; }
        public decimal TotalAchats { get; set; }
        public decimal MoyenneAchats { get; set; }
        public int NombreRetours { get; set; }
        public DateTime? DerniereFacture { get; set; }
    }

    /// <summary>
    /// DTO pour la création/modification de clients
    /// </summary>
    public class ClientCreateUpdateDTO
    {
        [Required(ErrorMessage = "Le code client est obligatoire")]
        [StringLength(50, ErrorMessage = "Le code ne peut pas dépasser 50 caractères")]
        public string Code { get; set; }

        [Required(ErrorMessage = "Le nom du client est obligatoire")]
        [StringLength(255, ErrorMessage = "Le nom ne peut pas dépasser 255 caractères")]
        public string NomClient { get; set; }

        [StringLength(255, ErrorMessage = "Le prénom ne peut pas dépasser 255 caractères")]
        public string PrenomClient { get; set; }

        [StringLength(255, ErrorMessage = "La raison sociale ne peut pas dépasser 255 caractères")]
        public string RaisonSociale { get; set; }

        [Required(ErrorMessage = "Le type de client est obligatoire")]
        public string TypeClient { get; set; } = "Particulier";

        public string Adresse { get; set; }

        [StringLength(100, ErrorMessage = "La ville ne peut pas dépasser 100 caractères")]
        public string Ville { get; set; }

        [StringLength(100, ErrorMessage = "La wilaya ne peut pas dépasser 100 caractères")]
        public string Wilaya { get; set; }

        [StringLength(10, ErrorMessage = "Le code postal ne peut pas dépasser 10 caractères")]
        public string CodePostal { get; set; }

        [StringLength(20, ErrorMessage = "Le téléphone ne peut pas dépasser 20 caractères")]
        [Phone(ErrorMessage = "Format de téléphone invalide")]
        public string Telephone { get; set; }

        [StringLength(100, ErrorMessage = "L'email ne peut pas dépasser 100 caractères")]
        [EmailAddress(ErrorMessage = "Format d'email invalide")]
        public string Email { get; set; }

        [StringLength(20, ErrorMessage = "Le NIF ne peut pas dépasser 20 caractères")]
        public string NIF { get; set; }

        [StringLength(20, ErrorMessage = "Le NIS ne peut pas dépasser 20 caractères")]
        public string NIS { get; set; }

        [StringLength(20, ErrorMessage = "Le RC ne peut pas dépasser 20 caractères")]
        public string RC { get; set; }

        [StringLength(20, ErrorMessage = "L'ART ne peut pas dépasser 20 caractères")]
        public string ART { get; set; }

        public decimal SoldeInitial { get; set; } = 0;

        [Range(0, double.MaxValue, ErrorMessage = "La limite de crédit doit être positive")]
        public decimal CreditLimite { get; set; } = 0;

        [Range(0, 100, ErrorMessage = "La remise doit être entre 0 et 100%")]
        public decimal RemiseHabituelle { get; set; } = 0;

        public DateTime? DateNaissance { get; set; }

        public string Statut { get; set; } = "Actif";
    }

    /// <summary>
    /// DTO pour la recherche de clients
    /// </summary>
    public class ClientSearchDTO
    {
        public string SearchTerm { get; set; }
        public string TypeClient { get; set; }
        public string Statut { get; set; }
        public string Ville { get; set; }
        public string Wilaya { get; set; }
        public bool? ACredit { get; set; }
        public bool? CreditDepasse { get; set; }
        public decimal? SoldeMin { get; set; }
        public decimal? SoldeMax { get; set; }
        public DateTime? DateCreationDebut { get; set; }
        public DateTime? DateCreationFin { get; set; }
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 20;
        public string SortField { get; set; } = "NomClient";
        public bool SortAscending { get; set; } = true;
    }

    /// <summary>
    /// DTO pour les statistiques clients
    /// </summary>
    public class ClientStatisticsDTO
    {
        public int TotalClients { get; set; }
        public int ClientsActifs { get; set; }
        public int ClientsParticuliers { get; set; }
        public int ClientsEntreprises { get; set; }
        public int ClientsRevendeurs { get; set; }
        public int ClientsAvecCredit { get; set; }
        public int ClientsCreditDepasse { get; set; }
        public decimal TotalCreances { get; set; }
        public decimal TotalLimitesCredit { get; set; }
        public decimal CreditDisponibleTotal { get; set; }
        public decimal ChiffreAffairesTotalClients { get; set; }
        public decimal MoyenneAchatsParClient { get; set; }
    }

    /// <summary>
    /// DTO pour l'historique client
    /// </summary>
    public class ClientHistoriqueDTO
    {
        public int ClientId { get; set; }
        public string Type { get; set; } // "Facture", "Paiement", "Retour", "Ajustement"
        public string Reference { get; set; }
        public DateTime Date { get; set; }
        public string Description { get; set; }
        public decimal Montant { get; set; }
        public string Sens { get; set; } // "Débit", "Crédit"
        public decimal SoldeApres { get; set; }
        public string Statut { get; set; }
        public string Notes { get; set; }
    }

    /// <summary>
    /// DTO pour l'import/export de clients
    /// </summary>
    public class ClientImportExportDTO
    {
        public string Code { get; set; }
        public string NomClient { get; set; }
        public string PrenomClient { get; set; }
        public string RaisonSociale { get; set; }
        public string TypeClient { get; set; }
        public string Adresse { get; set; }
        public string Ville { get; set; }
        public string Wilaya { get; set; }
        public string CodePostal { get; set; }
        public string Telephone { get; set; }
        public string Email { get; set; }
        public string NIF { get; set; }
        public string NIS { get; set; }
        public string RC { get; set; }
        public string ART { get; set; }
        public decimal SoldeInitial { get; set; }
        public decimal CreditLimite { get; set; }
        public decimal RemiseHabituelle { get; set; }
        public string DateNaissance { get; set; }
        public string Statut { get; set; }
    }

    /// <summary>
    /// DTO pour les ajustements de solde client
    /// </summary>
    public class ClientSoldeAdjustmentDTO
    {
        [Required(ErrorMessage = "L'ID du client est obligatoire")]
        public int ClientId { get; set; }

        [Required(ErrorMessage = "Le montant est obligatoire")]
        public decimal Montant { get; set; }

        [Required(ErrorMessage = "Le type d'ajustement est obligatoire")]
        public string TypeAjustement { get; set; } // "Crédit", "Débit"

        [Required(ErrorMessage = "Le motif est obligatoire")]
        [StringLength(255, ErrorMessage = "Le motif ne peut pas dépasser 255 caractères")]
        public string Motif { get; set; }

        public string Notes { get; set; }

        [Required(ErrorMessage = "L'utilisateur est obligatoire")]
        public int UtilisateurId { get; set; }
    }

    /// <summary>
    /// DTO pour les alertes clients
    /// </summary>
    public class ClientAlertDTO
    {
        public int ClientId { get; set; }
        public string CodeClient { get; set; }
        public string NomClient { get; set; }
        public string TypeAlerte { get; set; } // "Crédit dépassé", "Paiement en retard", "Limite atteinte"
        public string Priorite { get; set; } // "Haute", "Moyenne", "Basse"
        public DateTime DateAlerte { get; set; }
        public string Message { get; set; }
        public decimal Montant { get; set; }
        public int JoursRetard { get; set; }
        public string Statut { get; set; } // "Nouvelle", "En cours", "Résolue"
    }

    /// <summary>
    /// DTO pour le relevé de compte client
    /// </summary>
    public class ReleveCompteClientDTO
    {
        public ClientDTO Client { get; set; }
        public DateTime DateDebut { get; set; }
        public DateTime DateFin { get; set; }
        public decimal SoldeOuverture { get; set; }
        public decimal SoldeFermeture { get; set; }
        public decimal TotalDebits { get; set; }
        public decimal TotalCredits { get; set; }
        public List<ClientHistoriqueDTO> Mouvements { get; set; } = new List<ClientHistoriqueDTO>();
        public List<FactureVenteDTO> FacturesImpayees { get; set; } = new List<FactureVenteDTO>();
    }

    /// <summary>
    /// DTO pour les fournisseurs (structure similaire aux clients)
    /// </summary>
    public class FournisseurDTO
    {
        public int Id { get; set; }
        public string Code { get; set; }
        public string NomFournisseur { get; set; }
        public string RaisonSociale { get; set; }
        public string NomAffichage { get; set; }
        public string Adresse { get; set; }
        public string Ville { get; set; }
        public string Wilaya { get; set; }
        public string CodePostal { get; set; }
        public string AdresseComplete { get; set; }
        public string Telephone { get; set; }
        public string Fax { get; set; }
        public string Email { get; set; }
        public string SiteWeb { get; set; }
        public string NIF { get; set; }
        public string NIS { get; set; }
        public string RC { get; set; }
        public string ART { get; set; }
        public decimal SoldeInitial { get; set; }
        public decimal SoldeActuel { get; set; }
        public decimal CreditLimite { get; set; }
        public decimal CreditDisponible { get; set; }
        public int DelaiPaiement { get; set; }
        public decimal RemiseHabituelle { get; set; }
        public string ContactPrincipal { get; set; }
        public string TelephoneContact { get; set; }
        public string Statut { get; set; }
        public bool ADette { get; set; }
        public bool EstValide { get; set; }
        public int NombreFactures { get; set; }
        public decimal TotalAchats { get; set; }
        public decimal MoyenneAchats { get; set; }
        public int NombreRetours { get; set; }
        public DateTime? DerniereFacture { get; set; }
    }

    /// <summary>
    /// DTO pour la création/modification de fournisseurs
    /// </summary>
    public class FournisseurCreateUpdateDTO
    {
        [Required(ErrorMessage = "Le code fournisseur est obligatoire")]
        [StringLength(50, ErrorMessage = "Le code ne peut pas dépasser 50 caractères")]
        public string Code { get; set; }

        [Required(ErrorMessage = "Le nom du fournisseur est obligatoire")]
        [StringLength(255, ErrorMessage = "Le nom ne peut pas dépasser 255 caractères")]
        public string NomFournisseur { get; set; }

        [StringLength(255, ErrorMessage = "La raison sociale ne peut pas dépasser 255 caractères")]
        public string RaisonSociale { get; set; }

        public string Adresse { get; set; }

        [StringLength(100, ErrorMessage = "La ville ne peut pas dépasser 100 caractères")]
        public string Ville { get; set; }

        [StringLength(100, ErrorMessage = "La wilaya ne peut pas dépasser 100 caractères")]
        public string Wilaya { get; set; }

        [StringLength(10, ErrorMessage = "Le code postal ne peut pas dépasser 10 caractères")]
        public string CodePostal { get; set; }

        [StringLength(20, ErrorMessage = "Le téléphone ne peut pas dépasser 20 caractères")]
        [Phone(ErrorMessage = "Format de téléphone invalide")]
        public string Telephone { get; set; }

        [StringLength(20, ErrorMessage = "Le fax ne peut pas dépasser 20 caractères")]
        public string Fax { get; set; }

        [StringLength(100, ErrorMessage = "L'email ne peut pas dépasser 100 caractères")]
        [EmailAddress(ErrorMessage = "Format d'email invalide")]
        public string Email { get; set; }

        [StringLength(255, ErrorMessage = "Le site web ne peut pas dépasser 255 caractères")]
        public string SiteWeb { get; set; }

        [StringLength(20, ErrorMessage = "Le NIF ne peut pas dépasser 20 caractères")]
        public string NIF { get; set; }

        [StringLength(20, ErrorMessage = "Le NIS ne peut pas dépasser 20 caractères")]
        public string NIS { get; set; }

        [StringLength(20, ErrorMessage = "Le RC ne peut pas dépasser 20 caractères")]
        public string RC { get; set; }

        [StringLength(20, ErrorMessage = "L'ART ne peut pas dépasser 20 caractères")]
        public string ART { get; set; }

        public decimal SoldeInitial { get; set; } = 0;

        [Range(0, double.MaxValue, ErrorMessage = "La limite de crédit doit être positive")]
        public decimal CreditLimite { get; set; } = 0;

        [Range(1, 365, ErrorMessage = "Le délai de paiement doit être entre 1 et 365 jours")]
        public int DelaiPaiement { get; set; } = 30;

        [Range(0, 100, ErrorMessage = "La remise doit être entre 0 et 100%")]
        public decimal RemiseHabituelle { get; set; } = 0;

        [StringLength(255, ErrorMessage = "Le contact principal ne peut pas dépasser 255 caractères")]
        public string ContactPrincipal { get; set; }

        [StringLength(20, ErrorMessage = "Le téléphone contact ne peut pas dépasser 20 caractères")]
        [Phone(ErrorMessage = "Format de téléphone invalide")]
        public string TelephoneContact { get; set; }

        public string Statut { get; set; } = "Actif";
    }

    /// <summary>
    /// DTO pour l'entreprise
    /// </summary>
    public class EntrepriseDTO
    {
        public int Id { get; set; }
        public string NomEntreprise { get; set; }
        public string RaisonSociale { get; set; }
        public string Adresse { get; set; }
        public string Ville { get; set; }
        public string Wilaya { get; set; }
        public string CodePostal { get; set; }
        public string AdresseComplete { get; set; }
        public string Telephone { get; set; }
        public string Fax { get; set; }
        public string Email { get; set; }
        public string SiteWeb { get; set; }
        public string NIF { get; set; }
        public string NIS { get; set; }
        public string RC { get; set; }
        public string ART { get; set; }
        public byte[] Logo { get; set; }
        public decimal CapitalSocial { get; set; }
        public string FormeJuridique { get; set; }
        public string ActivitePrincipale { get; set; }
        public DateTime? DateCreation { get; set; }
        public string Statut { get; set; }
        public bool EstValide { get; set; }
    }
}
