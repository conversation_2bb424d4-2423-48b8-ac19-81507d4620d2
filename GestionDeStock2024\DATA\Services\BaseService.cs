using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using GestionDeStock2024.DATA.Repositories;

namespace GestionDeStock2024.DATA.Services
{
    /// <summary>
    /// Service de base implémentant les opérations communes
    /// </summary>
    /// <typeparam name="T">Type de l'entité</typeparam>
    public abstract class BaseService<T> : IMasterService<T> where T : class
    {
        protected readonly IMasterRepository<T> _repository;
        protected OperationContext _currentContext;

        protected BaseService(IMasterRepository<T> repository)
        {
            _repository = repository ?? throw new ArgumentNullException(nameof(repository));
        }

        /// <summary>
        /// Définit le contexte d'opération
        /// </summary>
        public virtual void SetOperationContext(OperationContext context)
        {
            _currentContext = context;
        }

        #region Opérations CRUD de base

        public virtual async Task<T> GetByIdAsync(int id)
        {
            try
            {
                return await _repository.GetByIdAsync(id);
            }
            catch (Exception ex)
            {
                throw new ServiceException($"Erreur lors de la récupération de l'entité avec l'ID {id}", ex);
            }
        }

        public virtual async Task<IEnumerable<T>> GetAllAsync()
        {
            try
            {
                return await _repository.GetAllAsync();
            }
            catch (Exception ex)
            {
                throw new ServiceException("Erreur lors de la récupération de toutes les entités", ex);
            }
        }

        public virtual async Task<IEnumerable<T>> GetPagedAsync(int page, int pageSize)
        {
            try
            {
                return await _repository.GetPagedAsync(page, pageSize);
            }
            catch (Exception ex)
            {
                throw new ServiceException($"Erreur lors de la récupération paginée (page {page}, taille {pageSize})", ex);
            }
        }

        public virtual async Task<int> GetCountAsync()
        {
            try
            {
                return await _repository.GetCountAsync();
            }
            catch (Exception ex)
            {
                throw new ServiceException("Erreur lors du comptage des entités", ex);
            }
        }

        public virtual async Task<ServiceResult<int>> AddAsync(T entity)
        {
            try
            {
                // Validation avant ajout
                var validationResult = await ValidateForAddAsync(entity);
                if (!validationResult.Success)
                {
                    return ServiceResult<int>.ErrorResult("Validation échouée", validationResult.Errors);
                }

                var id = await _repository.AddAsync(entity);
                return ServiceResult<int>.SuccessResult(id, "Entité ajoutée avec succès");
            }
            catch (Exception ex)
            {
                return ServiceResult<int>.ErrorResult(ex, "Erreur lors de l'ajout de l'entité");
            }
        }

        public virtual async Task<ServiceResult<bool>> UpdateAsync(T entity)
        {
            try
            {
                // Validation avant mise à jour
                var validationResult = await ValidateForUpdateAsync(entity);
                if (!validationResult.Success)
                {
                    return ServiceResult<bool>.ErrorResult("Validation échouée", validationResult.Errors);
                }

                var success = await _repository.UpdateAsync(entity);
                return success 
                    ? ServiceResult<bool>.SuccessResult(true, "Entité mise à jour avec succès")
                    : ServiceResult<bool>.ErrorResult("Échec de la mise à jour");
            }
            catch (Exception ex)
            {
                return ServiceResult<bool>.ErrorResult(ex, "Erreur lors de la mise à jour de l'entité");
            }
        }

        public virtual async Task<ServiceResult<bool>> DeleteAsync(int id)
        {
            try
            {
                // Validation avant suppression
                var validationResult = await ValidateForDeleteAsync(id);
                if (!validationResult.Success)
                {
                    return ServiceResult<bool>.ErrorResult("Validation échouée", validationResult.Errors);
                }

                var success = await _repository.DeleteAsync(id);
                return success 
                    ? ServiceResult<bool>.SuccessResult(true, "Entité supprimée avec succès")
                    : ServiceResult<bool>.ErrorResult("Échec de la suppression");
            }
            catch (Exception ex)
            {
                return ServiceResult<bool>.ErrorResult(ex, "Erreur lors de la suppression de l'entité");
            }
        }

        #endregion

        #region Recherche et filtrage

        public virtual async Task<IEnumerable<T>> SearchAsync(string searchTerm)
        {
            try
            {
                return await _repository.SearchAsync(searchTerm);
            }
            catch (Exception ex)
            {
                throw new ServiceException($"Erreur lors de la recherche avec le terme '{searchTerm}'", ex);
            }
        }

        public virtual async Task<IEnumerable<T>> GetActiveAsync()
        {
            try
            {
                return await _repository.GetActiveAsync();
            }
            catch (Exception ex)
            {
                throw new ServiceException("Erreur lors de la récupération des entités actives", ex);
            }
        }

        #endregion

        #region ICodeService

        public virtual async Task<T> GetByCodeAsync(string code)
        {
            try
            {
                return await _repository.GetByCodeAsync(code);
            }
            catch (Exception ex)
            {
                throw new ServiceException($"Erreur lors de la récupération de l'entité avec le code '{code}'", ex);
            }
        }

        public virtual async Task<bool> CodeExistsAsync(string code, int? excludeId = null)
        {
            try
            {
                return await _repository.CodeExistsAsync(code, excludeId);
            }
            catch (Exception ex)
            {
                throw new ServiceException($"Erreur lors de la vérification d'existence du code '{code}'", ex);
            }
        }

        public virtual async Task<string> GenerateNextCodeAsync(string prefix = "")
        {
            try
            {
                return await _repository.GenerateNextCodeAsync(prefix);
            }
            catch (Exception ex)
            {
                throw new ServiceException($"Erreur lors de la génération du prochain code avec le préfixe '{prefix}'", ex);
            }
        }

        #endregion

        #region IStatutService

        public virtual async Task<ServiceResult<bool>> ActivateAsync(int id)
        {
            try
            {
                var success = await _repository.ActivateAsync(id);
                return success 
                    ? ServiceResult<bool>.SuccessResult(true, "Entité activée avec succès")
                    : ServiceResult<bool>.ErrorResult("Échec de l'activation");
            }
            catch (Exception ex)
            {
                return ServiceResult<bool>.ErrorResult(ex, "Erreur lors de l'activation de l'entité");
            }
        }

        public virtual async Task<ServiceResult<bool>> DeactivateAsync(int id)
        {
            try
            {
                var success = await _repository.DeactivateAsync(id);
                return success 
                    ? ServiceResult<bool>.SuccessResult(true, "Entité désactivée avec succès")
                    : ServiceResult<bool>.ErrorResult("Échec de la désactivation");
            }
            catch (Exception ex)
            {
                return ServiceResult<bool>.ErrorResult(ex, "Erreur lors de la désactivation de l'entité");
            }
        }

        public virtual async Task<ServiceResult<bool>> ChangeStatusAsync(int id, string newStatus)
        {
            try
            {
                var success = await _repository.ChangeStatusAsync(id, newStatus);
                return success 
                    ? ServiceResult<bool>.SuccessResult(true, $"Statut changé vers '{newStatus}' avec succès")
                    : ServiceResult<bool>.ErrorResult("Échec du changement de statut");
            }
            catch (Exception ex)
            {
                return ServiceResult<bool>.ErrorResult(ex, "Erreur lors du changement de statut");
            }
        }

        public virtual async Task<IEnumerable<T>> GetInactiveAsync()
        {
            try
            {
                return await _repository.GetInactiveAsync();
            }
            catch (Exception ex)
            {
                throw new ServiceException("Erreur lors de la récupération des entités inactives", ex);
            }
        }

        #endregion

        #region Validation

        public virtual async Task<ServiceResult<bool>> ValidateAsync(T entity)
        {
            try
            {
                var (isValid, errors) = await _repository.ValidateAsync(entity);
                return isValid 
                    ? ServiceResult<bool>.SuccessResult(true, "Validation réussie")
                    : ServiceResult<bool>.ErrorResult("Validation échouée", errors);
            }
            catch (Exception ex)
            {
                return ServiceResult<bool>.ErrorResult(ex, "Erreur lors de la validation");
            }
        }

        protected virtual async Task<ServiceResult<bool>> ValidateForAddAsync(T entity)
        {
            try
            {
                var (isValid, errors) = await _repository.ValidateForAddAsync(entity);
                
                // Ajouter des validations métier spécifiques
                var businessValidation = await ValidateBusinessRulesForAddAsync(entity);
                if (!businessValidation.Success)
                {
                    errors.AddRange(businessValidation.Errors);
                    isValid = false;
                }

                return isValid 
                    ? ServiceResult<bool>.SuccessResult(true, "Validation pour ajout réussie")
                    : ServiceResult<bool>.ErrorResult("Validation pour ajout échouée", errors);
            }
            catch (Exception ex)
            {
                return ServiceResult<bool>.ErrorResult(ex, "Erreur lors de la validation pour ajout");
            }
        }

        protected virtual async Task<ServiceResult<bool>> ValidateForUpdateAsync(T entity)
        {
            try
            {
                var (isValid, errors) = await _repository.ValidateForUpdateAsync(entity);
                
                // Ajouter des validations métier spécifiques
                var businessValidation = await ValidateBusinessRulesForUpdateAsync(entity);
                if (!businessValidation.Success)
                {
                    errors.AddRange(businessValidation.Errors);
                    isValid = false;
                }

                return isValid 
                    ? ServiceResult<bool>.SuccessResult(true, "Validation pour mise à jour réussie")
                    : ServiceResult<bool>.ErrorResult("Validation pour mise à jour échouée", errors);
            }
            catch (Exception ex)
            {
                return ServiceResult<bool>.ErrorResult(ex, "Erreur lors de la validation pour mise à jour");
            }
        }

        protected virtual async Task<ServiceResult<bool>> ValidateForDeleteAsync(int id)
        {
            try
            {
                var (isValid, errors) = await _repository.ValidateForDeleteAsync(id);
                
                // Ajouter des validations métier spécifiques
                var businessValidation = await ValidateBusinessRulesForDeleteAsync(id);
                if (!businessValidation.Success)
                {
                    errors.AddRange(businessValidation.Errors);
                    isValid = false;
                }

                return isValid 
                    ? ServiceResult<bool>.SuccessResult(true, "Validation pour suppression réussie")
                    : ServiceResult<bool>.ErrorResult("Validation pour suppression échouée", errors);
            }
            catch (Exception ex)
            {
                return ServiceResult<bool>.ErrorResult(ex, "Erreur lors de la validation pour suppression");
            }
        }

        // Méthodes virtuelles pour les validations métier spécifiques
        protected virtual async Task<ServiceResult<bool>> ValidateBusinessRulesForAddAsync(T entity)
        {
            return ServiceResult<bool>.SuccessResult(true);
        }

        protected virtual async Task<ServiceResult<bool>> ValidateBusinessRulesForUpdateAsync(T entity)
        {
            return ServiceResult<bool>.SuccessResult(true);
        }

        protected virtual async Task<ServiceResult<bool>> ValidateBusinessRulesForDeleteAsync(int id)
        {
            return ServiceResult<bool>.SuccessResult(true);
        }

        #endregion

        #region IAuditableService - Implémentation de base

        public virtual async Task<ServiceResult<int>> AddWithAuditAsync(T entity, int userId)
        {
            try
            {
                var id = await _repository.AddWithAuditAsync(entity, userId);
                return ServiceResult<int>.SuccessResult(id, "Entité ajoutée avec audit");
            }
            catch (Exception ex)
            {
                return ServiceResult<int>.ErrorResult(ex, "Erreur lors de l'ajout avec audit");
            }
        }

        public virtual async Task<ServiceResult<bool>> UpdateWithAuditAsync(T entity, int userId)
        {
            try
            {
                var success = await _repository.UpdateWithAuditAsync(entity, userId);
                return ServiceResult<bool>.SuccessResult(success, "Entité mise à jour avec audit");
            }
            catch (Exception ex)
            {
                return ServiceResult<bool>.ErrorResult(ex, "Erreur lors de la mise à jour avec audit");
            }
        }

        public virtual async Task<ServiceResult<bool>> DeleteWithAuditAsync(int id, int userId)
        {
            try
            {
                var success = await _repository.DeleteWithAuditAsync(id, userId);
                return ServiceResult<bool>.SuccessResult(success, "Entité supprimée avec audit");
            }
            catch (Exception ex)
            {
                return ServiceResult<bool>.ErrorResult(ex, "Erreur lors de la suppression avec audit");
            }
        }

        public virtual async Task<IEnumerable<object>> GetAuditHistoryAsync(int entityId)
        {
            try
            {
                return await _repository.GetAuditHistoryAsync(entityId);
            }
            catch (Exception ex)
            {
                throw new ServiceException($"Erreur lors de la récupération de l'historique d'audit pour l'entité {entityId}", ex);
            }
        }

        #endregion

        #region ICacheableService - Implémentation de base

        public virtual async Task<T> GetByIdCachedAsync(int id)
        {
            try
            {
                return await _repository.GetByIdCachedAsync(id);
            }
            catch (Exception ex)
            {
                throw new ServiceException($"Erreur lors de la récupération en cache de l'entité {id}", ex);
            }
        }

        public virtual async Task<IEnumerable<T>> GetAllCachedAsync()
        {
            try
            {
                return await _repository.GetAllCachedAsync();
            }
            catch (Exception ex)
            {
                throw new ServiceException("Erreur lors de la récupération en cache de toutes les entités", ex);
            }
        }

        public virtual async Task RefreshCacheAsync()
        {
            try
            {
                await _repository.RefreshCacheAsync();
            }
            catch (Exception ex)
            {
                throw new ServiceException("Erreur lors du rafraîchissement du cache", ex);
            }
        }

        public virtual async Task ClearCacheAsync()
        {
            try
            {
                await _repository.ClearCacheAsync();
            }
            catch (Exception ex)
            {
                throw new ServiceException("Erreur lors de la suppression du cache", ex);
            }
        }

        #endregion

        #region ITransactionalService

        public virtual async Task<ServiceResult<TResult>> ExecuteInTransactionAsync<TResult>(Func<Task<TResult>> operation)
        {
            try
            {
                var result = await _repository.ExecuteInTransactionAsync(operation);
                return ServiceResult<TResult>.SuccessResult(result, "Opération transactionnelle réussie");
            }
            catch (Exception ex)
            {
                return ServiceResult<TResult>.ErrorResult(ex, "Erreur lors de l'opération transactionnelle");
            }
        }

        public virtual async Task<ServiceResult> ExecuteInTransactionAsync(Func<Task> operation)
        {
            try
            {
                await _repository.ExecuteInTransactionAsync(operation);
                return ServiceResult.SuccessResult("Opération transactionnelle réussie");
            }
            catch (Exception ex)
            {
                return ServiceResult.ErrorResult(ex, "Erreur lors de l'opération transactionnelle");
            }
        }

        #endregion
    }

    /// <summary>
    /// Exception spécifique aux services
    /// </summary>
    public class ServiceException : Exception
    {
        public ServiceException(string message) : base(message) { }
        public ServiceException(string message, Exception innerException) : base(message, innerException) { }
    }
}
