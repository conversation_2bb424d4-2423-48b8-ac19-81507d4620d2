using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using static GestionDeStock2024.MODELS.Enums;

namespace GestionDeStock2024.MODELS
{
    /// <summary>
    /// Modèle pour les factures d'achat
    /// </summary>
    public class FactureAchat : BaseEntity, IStatutEntity
    {
        [Required(ErrorMessage = "Le numéro de facture est obligatoire")]
        [StringLength(50)]
        public string NumeroFacture { get; set; }

        [StringLength(50)]
        public string NumeroFactureFournisseur { get; set; }

        [Required(ErrorMessage = "Le fournisseur est obligatoire")]
        public int FournisseurId { get; set; }

        [Required(ErrorMessage = "L'utilisateur est obligatoire")]
        public int UtilisateurId { get; set; }

        [Required(ErrorMessage = "La date de facture est obligatoire")]
        public DateTime DateFacture { get; set; } = DateTime.Today;

        public DateTime? DateEcheance { get; set; }

        public decimal MontantHT { get; set; } = 0;

        public decimal MontantTVA { get; set; } = 0;

        public decimal MontantTTC { get; set; } = 0;

        public decimal RemiseGlobale { get; set; } = 0;

        [Range(0, 100, ErrorMessage = "Le pourcentage de remise doit être entre 0 et 100")]
        public decimal RemisePourcentage { get; set; } = 0;

        public decimal MontantTotal { get; set; } = 0;

        public decimal MontantPaye { get; set; } = 0;

        public decimal MontantRestant { get; set; } = 0;

        public int? ModePaiementId { get; set; }

        public string StatutPaiement { get; set; } = StatutPaiement.Non_payé.ToString();

        public string Statut { get; set; } = StatutFacture.Brouillon.ToString();

        public string Notes { get; set; }

        // Navigation
        public virtual Fournisseur Fournisseur { get; set; }
        public virtual Utilisateur Utilisateur { get; set; }
        public virtual ModePaiement ModePaiement { get; set; }
        public virtual ICollection<DetailFactureAchat> DetailsFacture { get; set; }
        public virtual ICollection<Paiement> Paiements { get; set; }
        public virtual ICollection<RetourFournisseur> RetoursFournisseur { get; set; }

        // Propriétés calculées
        public bool EstValidee => Statut == StatutFacture.Validée.ToString();
        public bool EstBrouillon => Statut == StatutFacture.Brouillon.ToString();
        public bool EstAnnulee => Statut == StatutFacture.Annulée.ToString();

        public bool EstPayee => StatutPaiement == StatutPaiement.Payé.ToString();
        public bool EstNonPayee => StatutPaiement == StatutPaiement.Non_payé.ToString();
        public bool EstPartiellementPayee => StatutPaiement == StatutPaiement.Partiellement_payé.ToString();

        public bool EstEchue => DateEcheance.HasValue && DateEcheance.Value < DateTime.Today && !EstPayee;

        public int? JoursRetard => EstEchue ? (DateTime.Today - DateEcheance.Value).Days : null;

        public int NombreArticles => DetailsFacture?.Count ?? 0;

        public decimal QuantiteTotale => DetailsFacture?.Sum(d => d.Quantite) ?? 0;

        /// <summary>
        /// Calcule les totaux de la facture
        /// </summary>
        public void CalculerTotaux()
        {
            if (DetailsFacture == null || !DetailsFacture.Any()) return;

            MontantHT = DetailsFacture.Sum(d => d.MontantHT);
            MontantTVA = DetailsFacture.Sum(d => d.MontantTVA);
            MontantTTC = DetailsFacture.Sum(d => d.MontantTTC);

            // Appliquer la remise globale
            if (RemisePourcentage > 0)
            {
                RemiseGlobale = MontantTTC * (RemisePourcentage / 100);
            }

            MontantTotal = MontantTTC - RemiseGlobale;
            MontantRestant = MontantTotal - MontantPaye;
        }

        /// <summary>
        /// Met à jour le statut de paiement
        /// </summary>
        public void MettreAJourStatutPaiement()
        {
            if (MontantPaye <= 0)
            {
                StatutPaiement = StatutPaiement.Non_payé.ToString();
            }
            else if (MontantPaye >= MontantTotal)
            {
                StatutPaiement = StatutPaiement.Payé.ToString();
                MontantRestant = 0;
            }
            else
            {
                StatutPaiement = StatutPaiement.Partiellement_payé.ToString();
                MontantRestant = MontantTotal - MontantPaye;
            }
        }

        /// <summary>
        /// Calcule la date d'échéance basée sur le délai du fournisseur
        /// </summary>
        public void CalculerDateEcheance()
        {
            if (Fournisseur != null)
            {
                DateEcheance = DateFacture.AddDays(Fournisseur.DelaiPaiement);
            }
        }
    }

    /// <summary>
    /// Modèle pour les détails des factures d'achat
    /// </summary>
    public class DetailFactureAchat : BaseEntity
    {
        [Required(ErrorMessage = "La facture est obligatoire")]
        public int FactureAchatId { get; set; }

        [Required(ErrorMessage = "L'article est obligatoire")]
        public int ArticleId { get; set; }

        [Required(ErrorMessage = "La quantité est obligatoire")]
        [Range(0.01, double.MaxValue, ErrorMessage = "La quantité doit être positive")]
        public decimal Quantite { get; set; }

        [Required(ErrorMessage = "Le prix unitaire HT est obligatoire")]
        [Range(0, double.MaxValue, ErrorMessage = "Le prix unitaire HT doit être positif")]
        public decimal PrixUnitaireHT { get; set; }

        [Required(ErrorMessage = "Le prix unitaire TTC est obligatoire")]
        [Range(0, double.MaxValue, ErrorMessage = "Le prix unitaire TTC doit être positif")]
        public decimal PrixUnitaireTTC { get; set; }

        public decimal RemiseLigne { get; set; } = 0;

        [Range(0, 100, ErrorMessage = "Le pourcentage de remise doit être entre 0 et 100")]
        public decimal RemisePourcentage { get; set; } = 0;

        [Range(0, 100, ErrorMessage = "Le taux de TVA doit être entre 0 et 100")]
        public decimal TvaTaux { get; set; } = 19.00m;

        public decimal MontantHT { get; set; }

        public decimal MontantTVA { get; set; }

        public decimal MontantTTC { get; set; }

        public DateTime? DateExpiration { get; set; }

        [StringLength(100)]
        public string NumeroLot { get; set; }

        // Navigation
        public virtual FactureAchat FactureAchat { get; set; }
        public virtual Article Article { get; set; }

        // Propriétés calculées
        public decimal MontantRemise => RemiseLigne > 0 ? RemiseLigne : 
                                       (PrixUnitaireTTC * Quantite * RemisePourcentage / 100);

        public decimal MontantNetHT => MontantHT - (MontantRemise / (1 + TvaTaux / 100));

        public bool EstPerime => DateExpiration.HasValue && DateExpiration.Value < DateTime.Today;

        public bool ExpireBientot => DateExpiration.HasValue && 
                                    DateExpiration.Value <= DateTime.Today.AddDays(30) && 
                                    !EstPerime;

        /// <summary>
        /// Calcule les montants de la ligne
        /// </summary>
        public void CalculerMontants()
        {
            var montantBrut = PrixUnitaireTTC * Quantite;
            var remise = RemiseLigne > 0 ? RemiseLigne : (montantBrut * RemisePourcentage / 100);
            
            MontantTTC = montantBrut - remise;
            MontantHT = MontantTTC / (1 + TvaTaux / 100);
            MontantTVA = MontantTTC - MontantHT;
        }
    }

    /// <summary>
    /// Modèle pour les retours clients
    /// </summary>
    public class RetourClient : BaseEntity, IStatutEntity
    {
        [Required(ErrorMessage = "Le numéro de retour est obligatoire")]
        [StringLength(50)]
        public string NumeroRetour { get; set; }

        public int? FactureVenteId { get; set; }

        public int? ClientId { get; set; }

        [Required(ErrorMessage = "L'utilisateur est obligatoire")]
        public int UtilisateurId { get; set; }

        [Required(ErrorMessage = "La date de retour est obligatoire")]
        public DateTime DateRetour { get; set; } = DateTime.Today;

        [Required(ErrorMessage = "Le motif de retour est obligatoire")]
        public string MotifRetour { get; set; }

        public string DescriptionMotif { get; set; }

        public decimal MontantHT { get; set; } = 0;

        public decimal MontantTVA { get; set; } = 0;

        public decimal MontantTTC { get; set; } = 0;

        public decimal MontantRembourse { get; set; } = 0;

        public int? ModeRemboursementId { get; set; }

        public string Statut { get; set; } = "En_attente";

        // Navigation
        public virtual FactureVente FactureVente { get; set; }
        public virtual Client Client { get; set; }
        public virtual Utilisateur Utilisateur { get; set; }
        public virtual ModePaiement ModeRemboursement { get; set; }
        public virtual ICollection<DetailRetourClient> DetailsRetour { get; set; }

        // Propriétés calculées
        public bool EstEnAttente => Statut == "En_attente";
        public bool EstApprouve => Statut == "Approuvé";
        public bool EstRejete => Statut == "Rejeté";
        public bool EstRembourse => Statut == "Remboursé";

        public int NombreArticles => DetailsRetour?.Count ?? 0;

        public decimal QuantiteTotale => DetailsRetour?.Sum(d => d.QuantiteRetournee) ?? 0;
    }

    /// <summary>
    /// Modèle pour les détails des retours clients
    /// </summary>
    public class DetailRetourClient : BaseEntity
    {
        [Required(ErrorMessage = "Le retour client est obligatoire")]
        public int RetourClientId { get; set; }

        [Required(ErrorMessage = "L'article est obligatoire")]
        public int ArticleId { get; set; }

        [Required(ErrorMessage = "La quantité retournée est obligatoire")]
        [Range(0.01, double.MaxValue, ErrorMessage = "La quantité retournée doit être positive")]
        public decimal QuantiteRetournee { get; set; }

        [Required(ErrorMessage = "Le prix unitaire HT est obligatoire")]
        [Range(0, double.MaxValue, ErrorMessage = "Le prix unitaire HT doit être positif")]
        public decimal PrixUnitaireHT { get; set; }

        [Required(ErrorMessage = "Le prix unitaire TTC est obligatoire")]
        [Range(0, double.MaxValue, ErrorMessage = "Le prix unitaire TTC doit être positif")]
        public decimal PrixUnitaireTTC { get; set; }

        [Range(0, 100, ErrorMessage = "Le taux de TVA doit être entre 0 et 100")]
        public decimal TvaTaux { get; set; } = 19.00m;

        public decimal MontantHT { get; set; }

        public decimal MontantTVA { get; set; }

        public decimal MontantTTC { get; set; }

        public string ActionStock { get; set; } = "Remettre_stock";

        // Navigation
        public virtual RetourClient RetourClient { get; set; }
        public virtual Article Article { get; set; }

        // Propriétés calculées
        public bool RemettreEnStock => ActionStock == "Remettre_stock";
        public bool Detruire => ActionStock == "Détruire";
        public bool RetournerAuFournisseur => ActionStock == "Retour_fournisseur";

        /// <summary>
        /// Calcule les montants de la ligne
        /// </summary>
        public void CalculerMontants()
        {
            MontantTTC = PrixUnitaireTTC * QuantiteRetournee;
            MontantHT = MontantTTC / (1 + TvaTaux / 100);
            MontantTVA = MontantTTC - MontantHT;
        }
    }

    /// <summary>
    /// Modèle pour les retours fournisseurs
    /// </summary>
    public class RetourFournisseur : BaseEntity, IStatutEntity
    {
        [Required(ErrorMessage = "Le numéro de retour est obligatoire")]
        [StringLength(50)]
        public string NumeroRetour { get; set; }

        public int? FactureAchatId { get; set; }

        [Required(ErrorMessage = "Le fournisseur est obligatoire")]
        public int FournisseurId { get; set; }

        [Required(ErrorMessage = "L'utilisateur est obligatoire")]
        public int UtilisateurId { get; set; }

        [Required(ErrorMessage = "La date de retour est obligatoire")]
        public DateTime DateRetour { get; set; } = DateTime.Today;

        [Required(ErrorMessage = "Le motif de retour est obligatoire")]
        public string MotifRetour { get; set; }

        public string DescriptionMotif { get; set; }

        public decimal MontantHT { get; set; } = 0;

        public decimal MontantTVA { get; set; } = 0;

        public decimal MontantTTC { get; set; } = 0;

        public decimal MontantRembourse { get; set; } = 0;

        public string Statut { get; set; } = "En_attente";

        // Navigation
        public virtual FactureAchat FactureAchat { get; set; }
        public virtual Fournisseur Fournisseur { get; set; }
        public virtual Utilisateur Utilisateur { get; set; }
        public virtual ICollection<DetailRetourFournisseur> DetailsRetour { get; set; }

        // Propriétés calculées
        public bool EstEnAttente => Statut == "En_attente";
        public bool EstApprouve => Statut == "Approuvé";
        public bool EstRejete => Statut == "Rejeté";
        public bool EstRembourse => Statut == "Remboursé";

        public int NombreArticles => DetailsRetour?.Count ?? 0;

        public decimal QuantiteTotale => DetailsRetour?.Sum(d => d.QuantiteRetournee) ?? 0;
    }

    /// <summary>
    /// Modèle pour les détails des retours fournisseurs
    /// </summary>
    public class DetailRetourFournisseur : BaseEntity
    {
        [Required(ErrorMessage = "Le retour fournisseur est obligatoire")]
        public int RetourFournisseurId { get; set; }

        [Required(ErrorMessage = "L'article est obligatoire")]
        public int ArticleId { get; set; }

        [Required(ErrorMessage = "La quantité retournée est obligatoire")]
        [Range(0.01, double.MaxValue, ErrorMessage = "La quantité retournée doit être positive")]
        public decimal QuantiteRetournee { get; set; }

        [Required(ErrorMessage = "Le prix unitaire HT est obligatoire")]
        [Range(0, double.MaxValue, ErrorMessage = "Le prix unitaire HT doit être positif")]
        public decimal PrixUnitaireHT { get; set; }

        [Required(ErrorMessage = "Le prix unitaire TTC est obligatoire")]
        [Range(0, double.MaxValue, ErrorMessage = "Le prix unitaire TTC doit être positif")]
        public decimal PrixUnitaireTTC { get; set; }

        [Range(0, 100, ErrorMessage = "Le taux de TVA doit être entre 0 et 100")]
        public decimal TvaTaux { get; set; } = 19.00m;

        public decimal MontantHT { get; set; }

        public decimal MontantTVA { get; set; }

        public decimal MontantTTC { get; set; }

        // Navigation
        public virtual RetourFournisseur RetourFournisseur { get; set; }
        public virtual Article Article { get; set; }

        /// <summary>
        /// Calcule les montants de la ligne
        /// </summary>
        public void CalculerMontants()
        {
            MontantTTC = PrixUnitaireTTC * QuantiteRetournee;
            MontantHT = MontantTTC / (1 + TvaTaux / 100);
            MontantTVA = MontantTTC - MontantHT;
        }
    }
}
