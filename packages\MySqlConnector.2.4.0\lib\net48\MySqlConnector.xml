<?xml version="1.0"?>
<doc>
    <assembly>
        <name>MySqlConnector</name>
    </assembly>
    <members>
        <member name="T:MySqlConnector.Authentication.AuthenticationPlugins">
            <summary>
            A registry of known authentication plugins.
            </summary>
        </member>
        <member name="M:MySqlConnector.Authentication.AuthenticationPlugins.Register(MySqlConnector.Authentication.IAuthenticationPlugin)">
            <summary>
            Registers the specified authentication plugin. The name of this plugin must be unique.
            </summary>
            <param name="plugin">The authentication plugin.</param>
        </member>
        <member name="T:MySqlConnector.Authentication.IAuthenticationPlugin">
            <summary>
            The primary interface implemented by an authentication plugin.
            </summary>
        </member>
        <member name="P:MySqlConnector.Authentication.IAuthenticationPlugin.Name">
            <summary>
            Gets the authentication plugin name.
            </summary>
        </member>
        <member name="M:MySqlConnector.Authentication.IAuthenticationPlugin.CreateResponse(System.String,System.ReadOnlySpan{System.Byte})">
            <summary>
            Creates the authentication response.
            </summary>
            <param name="password">The client's password.</param>
            <param name="authenticationData">The authentication data supplied by the server; this is the <code>auth method data</code>
            from the <a href="https://dev.mysql.com/doc/internals/en/connection-phase-packets.html#packet-Protocol::AuthSwitchRequest">Authentication
            Method Switch Request Packet</a>.</param>
            <returns>The authentication response.</returns>
        </member>
        <member name="T:MySqlConnector.Authentication.IAuthenticationPlugin2">
            <summary>
            <see cref="T:MySqlConnector.Authentication.IAuthenticationPlugin2"/> is an extension to <see cref="T:MySqlConnector.Authentication.IAuthenticationPlugin"/> that returns a hash of the client's password.
            </summary>
        </member>
        <member name="M:MySqlConnector.Authentication.IAuthenticationPlugin2.CreatePasswordHash(System.String,System.ReadOnlySpan{System.Byte})">
            <summary>
            Hashes the client's password (e.g., for TLS certificate fingerprint verification).
            </summary>
            <param name="password">The client's password.</param>
            <param name="authenticationData">The authentication data supplied by the server; this is the <code>auth method data</code>
            from the <a href="https://dev.mysql.com/doc/internals/en/connection-phase-packets.html#packet-Protocol::AuthSwitchRequest">Authentication
            Method Switch Request Packet</a>.</param>
            <returns>The authentication-method-specific hash of the client's password.</returns>
        </member>
        <member name="T:MySqlConnector.Core.CommandListPosition">
            <summary>
            <see cref="T:MySqlConnector.Core.CommandListPosition"/> encapsulates a list of <see cref="T:MySqlConnector.Core.IMySqlCommand"/> and the current position within that list.
            </summary>
        </member>
        <member name="F:MySqlConnector.Core.CommandListPosition.m_commands">
            <summary>
            The commands in this list; either a singular <see cref="T:MySqlConnector.MySqlCommand"/> or a <see cref="T:System.Collections.Generic.IReadOnlyList`1"/>.
            </summary>
        </member>
        <member name="F:MySqlConnector.Core.CommandListPosition.CommandCount">
            <summary>
            The number of commands in the list.
            </summary>
        </member>
        <member name="F:MySqlConnector.Core.CommandListPosition.PreparedStatements">
            <summary>
            Associated prepared statements of commands
            </summary>
        </member>
        <member name="F:MySqlConnector.Core.CommandListPosition.CommandIndex">
            <summary>
            The index of the current command.
            </summary>
        </member>
        <member name="F:MySqlConnector.Core.CommandListPosition.PreparedStatementIndex">
            <summary>
            If the current command is a prepared statement, the index of the current prepared statement for that command.
            </summary>
        </member>
        <member name="F:MySqlConnector.Core.CommandListPosition.LastUsedPreparedStatement">
            <summary>
            Retrieve the last used prepared statement
            </summary>
        </member>
        <member name="P:MySqlConnector.Core.ConnectionPool.IsEmpty">
            <summary>
            Returns <c>true</c> if the connection pool is empty, i.e., all connections are in use. Note that in a highly-multithreaded
            environment, the value of this property may be stale by the time it's returned.
            </summary>
        </member>
        <member name="M:MySqlConnector.Core.ConnectionPool.GetProcedureCache">
            <summary>
            Returns the stored procedure cache for this <see cref="T:MySqlConnector.Core.ConnectionPool"/>, lazily creating it on demand.
            This method may return a different object after <see cref="M:MySqlConnector.Core.ConnectionPool.ClearAsync(MySqlConnector.Protocol.Serialization.IOBehavior,System.Threading.CancellationToken)"/> has been called. The returned
            object is shared between multiple threads and is only safe to use after taking a <c>lock</c> on the
            object itself.
            </summary>
        </member>
        <member name="M:MySqlConnector.Core.ConnectionPool.RecoverLeakedSessionsAsync(MySqlConnector.Protocol.Serialization.IOBehavior)">
            <summary>
            Examines all the <see cref="T:MySqlConnector.Core.ServerSession"/> objects in <see cref="F:MySqlConnector.Core.ConnectionPool.m_leasedSessions"/> to determine if any
            have an owning <see cref="T:MySqlConnector.MySqlConnection"/> that has been garbage-collected. If so, assumes that the connection
            was not properly disposed and returns the session to the pool.
            </summary>
        </member>
        <member name="P:MySqlConnector.Core.ConnectionSettings.ConnectionStringBuilder">
            <summary>
            The <see cref="T:MySqlConnector.MySqlConnectionStringBuilder" /> that was used to create this <see cref="T:MySqlConnector.Core.ConnectionSettings" />.!--
            This object must not be mutated.
            </summary>
        </member>
        <member name="T:MySqlConnector.Core.ICancellableCommand">
            <summary>
            <see cref="T:MySqlConnector.Core.IMySqlCommand"/> provides an internal abstraction over operations that can be cancelled: <see cref="T:MySqlConnector.MySqlCommand"/> and <see cref="T:MySqlConnector.MySqlBatch"/>.
            </summary>
        </member>
        <member name="M:MySqlConnector.Core.ICancellableCommandExtensions.GetNextId">
            <summary>
            Returns a unique ID for all implementations of <see cref="T:MySqlConnector.Core.ICancellableCommand"/>.
            </summary>
        </member>
        <member name="M:MySqlConnector.Core.ICancellableCommandExtensions.ResetCommandTimeout(MySqlConnector.Core.ICancellableCommand)">
            <summary>
            Causes the effective command timeout to be reset back to the value specified by <see cref="P:MySqlConnector.Core.ICancellableCommand.CommandTimeout"/>
            plus <see cref="P:MySqlConnector.MySqlConnectionStringBuilder.CancellationTimeout"/>. This allows for the command to time out, a cancellation to attempt
            to happen, then the "hard" timeout to occur.
            </summary>
            <remarks>As per the <a href="https://msdn.microsoft.com/en-us/library/system.data.sqlclient.sqlcommand.commandtimeout.aspx">MSDN documentation</a>,
            "This property is the cumulative time-out (for all network packets that are read during the invocation of a method) for all network reads during command
            execution or processing of the results. A time-out can still occur after the first row is returned, and does not include user processing time, only network
            read time. For example, with a 30 second time out, if Read requires two network packets, then it has 30 seconds to read both network packets. If you call
            Read again, it will have another 30 seconds to read any data that it requires."
            The <see cref="M:MySqlConnector.Core.ICancellableCommandExtensions.ResetCommandTimeout(MySqlConnector.Core.ICancellableCommand)"/> method is called by public ADO.NET API methods to reset the effective time remaining at the beginning of a new
            method call.</remarks>
        </member>
        <member name="T:MySqlConnector.Core.ICommandPayloadCreator">
            <summary>
            <see cref="T:MySqlConnector.Core.ICommandPayloadCreator"/> creates the data for an "execute query" command for one or more <see cref="T:MySqlConnector.Core.IMySqlCommand"/> objects in a command list.
            </summary>
        </member>
        <member name="M:MySqlConnector.Core.ICommandPayloadCreator.WriteQueryCommand(MySqlConnector.Core.CommandListPosition@,System.Collections.Generic.IDictionary{System.String,MySqlConnector.Core.CachedProcedure},MySqlConnector.Protocol.Serialization.ByteBufferWriter,System.Boolean)">
            <summary>
            Writes the payload for an "execute query" command to <paramref name="writer"/>.
            </summary>
            <param name="commandListPosition">The command list and its current position. This will be updated to the position of the next command to write (or past the end if there are no more commands).</param>
            <param name="cachedProcedures">A <see cref="T:MySqlConnector.Core.CachedProcedure"/> for all the stored procedures in the command list, if any.</param>
            <param name="writer">The <see cref="T:MySqlConnector.Protocol.Serialization.ByteBufferWriter"/> to write the payload to.</param>
            <param name="appendSemicolon">Whether a statement-separating semicolon should be appended if it's missing.</param>
            <returns><c>true</c> if a command was written; otherwise, <c>false</c> (if there were no more commands in the list).</returns>
        </member>
        <member name="P:MySqlConnector.Core.IConnectionPoolMetadata.ConnectionPool">
            <summary>
            Returns the <see cref="P:MySqlConnector.Core.IConnectionPoolMetadata.ConnectionPool"/> this <see cref="T:MySqlConnector.Core.IConnectionPoolMetadata"/> is associated with,
            or <c>null</c> if it represents a non-pooled connection.
            </summary>
        </member>
        <member name="P:MySqlConnector.Core.IConnectionPoolMetadata.Id">
            <summary>
            Returns the ID of the connection pool, or 0 if this is a non-pooled connection.
            </summary>
        </member>
        <member name="P:MySqlConnector.Core.IConnectionPoolMetadata.Generation">
            <summary>
            Returns the generation of the connection pool, or 0 if this is a non-pooled connection.
            </summary>
        </member>
        <member name="M:MySqlConnector.Core.IConnectionPoolMetadata.GetNewSessionId">
            <summary>
            Returns a new session ID.
            </summary>
            <returns>A new session ID.</returns>
        </member>
        <member name="M:MySqlConnector.Core.ILoadBalancer.LoadBalance(System.Collections.Generic.IReadOnlyList{System.String})">
            <summary>
            Returns an <see cref="T:System.Collections.Generic.IEnumerable`1"/> containing <paramref name="hosts"/> in the order they
            should be tried to satisfy the load balancing policy.
            </summary>
        </member>
        <member name="T:MySqlConnector.Core.IMySqlCommand">
            <summary>
            <see cref="T:MySqlConnector.Core.IMySqlCommand"/> provides an internal abstraction over <see cref="T:MySqlConnector.MySqlCommand"/> and <see cref="T:MySqlConnector.MySqlBatchCommand"/>.
            </summary>
        </member>
        <member name="T:MySqlConnector.Core.IValuesEnumerator">
            <summary>
            <see cref="T:MySqlConnector.Core.IValuesEnumerator"/> provides an abstraction over iterating through a sequence of
            rows, where each row can fill an array of field values.
            </summary>
        </member>
        <member name="T:MySqlConnector.Core.ParsedStatement">
            <summary>
            <see cref="T:MySqlConnector.Core.ParsedStatement"/> represents an individual SQL statement that's been parsed
            from a string possibly containing multiple semicolon-delimited SQL statements.
            </summary>
        </member>
        <member name="P:MySqlConnector.Core.ParsedStatement.StatementBytes">
            <summary>
            The bytes for this statement that will be written on the wire.
            </summary>
        </member>
        <member name="P:MySqlConnector.Core.ParsedStatement.ParameterNames">
            <summary>
            The names of the parameters (if known) of the parameters in the prepared statement. There
            is one entry in this list for each parameter, which will be <c>null</c> if the name is unknown.
            </summary>
        </member>
        <member name="P:MySqlConnector.Core.ParsedStatement.NormalizedParameterNames">
            <summary>
            The normalized names of the parameters (if known) of the parameters in the prepared statement. There
            is one entry in this list for each parameter, which will be <c>null</c> if the name is unknown.
            </summary>
        </member>
        <member name="P:MySqlConnector.Core.ParsedStatement.ParameterIndexes">
            <summary>
            The indexes of the parameters in the prepared statement. There is one entry in this list for
            each parameter; it will be <c>-1</c> if the parameter is named.
            </summary>
        </member>
        <member name="T:MySqlConnector.Core.ParsedStatements">
            <summary>
            <see cref="T:MySqlConnector.Core.ParsedStatements"/> wraps a collection of <see cref="T:MySqlConnector.Core.ParsedStatement"/> objects.
            It implements <see cref="T:System.IDisposable"/> to return the memory backing the statements to a shared pool.
            </summary>
        </member>
        <member name="M:MySqlConnector.Core.ParsedStatements.#ctor(System.Collections.Generic.List{MySqlConnector.Core.ParsedStatement},MySqlConnector.Protocol.PayloadData)">
            <summary>
            <see cref="T:MySqlConnector.Core.ParsedStatements"/> wraps a collection of <see cref="T:MySqlConnector.Core.ParsedStatement"/> objects.
            It implements <see cref="T:System.IDisposable"/> to return the memory backing the statements to a shared pool.
            </summary>
        </member>
        <member name="T:MySqlConnector.Core.PreparedStatement">
            <summary>
            <see cref="T:MySqlConnector.Core.PreparedStatement"/> is a statement that has been prepared on the MySQL Server.
            </summary>
        </member>
        <member name="M:MySqlConnector.Core.PreparedStatement.#ctor(System.Int32,MySqlConnector.Core.ParsedStatement,MySqlConnector.Protocol.Payloads.ColumnDefinitionPayload[],MySqlConnector.Protocol.Payloads.ColumnDefinitionPayload[])">
            <summary>
            <see cref="T:MySqlConnector.Core.PreparedStatement"/> is a statement that has been prepared on the MySQL Server.
            </summary>
        </member>
        <member name="M:MySqlConnector.Core.ServerSession.ValidateFingerprint(System.Byte[],System.ReadOnlySpan{System.Byte},System.String)">
            <summary>
            Validate SSL validation hash (from OK packet).
            </summary>
            <param name="validationHash">The validation hash received from the server.</param>
            <param name="challenge">The auth plugin data from the initial handshake.</param>
            <param name="password">The user's password.</param>
            <returns><c>true</c> if the validation hash matches the locally-computed value; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:MySqlConnector.Core.ServerSession.SafeDispose``1(``0@)">
            <summary>
            Disposes and sets <paramref name="disposable"/> to <c>null</c>, ignoring any
            <see cref="T:System.IO.IOException"/> or <see cref="T:System.Net.Sockets.SocketException"/> that is thrown.
            </summary>
            <typeparam name="T">An <see cref="T:System.IDisposable"/> type.</typeparam>
            <param name="disposable">The object to dispose.</param>
        </member>
        <member name="T:MySqlConnector.Core.ServerSession.__ExpectedSessionState6Struct">
            <summary> This API supports the logging infrastructure and is not intended to be used directly from your code. It is subject to change in the future. </summary>
        </member>
        <member name="M:MySqlConnector.Core.SingleCommandPayloadCreator.WriteQueryPayload(MySqlConnector.Core.IMySqlCommand,System.Collections.Generic.IDictionary{System.String,MySqlConnector.Core.CachedProcedure},MySqlConnector.Protocol.Serialization.ByteBufferWriter,System.Boolean,System.Boolean,System.Boolean)">
            <summary>
            Writes the text of <paramref name="command"/> to <paramref name="writer"/>, encoded in UTF-8.
            </summary>
            <param name="command">The command.</param>
            <param name="cachedProcedures">The cached procedures.</param>
            <param name="writer">The output writer.</param>
            <param name="appendSemicolon">Whether a statement-separating semicolon should be appended if it's missing.</param>
            <param name="isFirstCommand">Whether this command is the first one.</param>
            <param name="isLastCommand">Whether this command is the last one.</param>
            <returns><c>true</c> if a complete command was written; otherwise, <c>false</c>.</returns>
        </member>
        <member name="F:MySqlConnector.Core.SqlParser.FinalParseStates.Complete">
            <summary>
            The statement is complete (apart from potentially needing a semicolon or newline).
            </summary>
        </member>
        <member name="F:MySqlConnector.Core.SqlParser.FinalParseStates.NeedsNewline">
            <summary>
            The statement needs a newline (e.g., to terminate a final comment).
            </summary>
        </member>
        <member name="F:MySqlConnector.Core.SqlParser.FinalParseStates.NeedsSemicolon">
            <summary>
            The statement needs a semicolon (if another statement is going to be concatenated to it).
            </summary>
        </member>
        <member name="T:MySqlConnector.Logging.IMySqlConnectorLogger">
            <summary>
            Implementations of <see cref="T:MySqlConnector.Logging.IMySqlConnectorLogger"/> write logs to a particular target.
            </summary>
        </member>
        <member name="M:MySqlConnector.Logging.IMySqlConnectorLogger.IsEnabled(MySqlConnector.Logging.MySqlConnectorLogLevel)">
            <summary>
            Returns <c>true</c> if logging for this logger is enabled at the specified level.
            </summary>
            <param name="level">The log level.</param>
            <returns><c>true</c> if logging is enabled; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:MySqlConnector.Logging.IMySqlConnectorLogger.Log(MySqlConnector.Logging.MySqlConnectorLogLevel,System.String,System.Object[],System.Exception)">
            <summary>
            Writes a log message to the target.
            </summary>
            <param name="level">The log level.</param>
            <param name="message">The log message. See documentation for <paramref name="args"/> for notes on interpreting <c>{0}</c> within this string.</param>
            <param name="args">If not <c>null</c> or empty, then <paramref name="message"/> includes formatting placeholders (e.g., <c>{0}</c>)
            which must be replaced with the arguments in <paramref name="args"/>, using <see cref="M:System.String.Format(System.IFormatProvider,System.String,System.Object[])"/> or similar.
            If <c>null</c> or an empty array, then <paramref name="message"/> is a literal string; any curly braces within it must be treated as literal characters,
            not formatting placeholders.</param>
            <param name="exception">If not <c>null</c>, an <see cref="T:System.Exception"/> associated with the log message.</param>
            <remarks>This method may be called from multiple threads and must be thread-safe. This method may be called
            even if <see cref="M:MySqlConnector.Logging.IMySqlConnectorLogger.IsEnabled(MySqlConnector.Logging.MySqlConnectorLogLevel)"/> would return <c>false</c> for <paramref name="level"/>; the implementation must
            check if logging is enabled for that level.</remarks>
        </member>
        <member name="T:MySqlConnector.Logging.IMySqlConnectorLoggerProvider">
            <summary>
            Implementations of <see cref="T:MySqlConnector.Logging.IMySqlConnectorLoggerProvider"/> create logger instances.
            </summary>
        </member>
        <member name="M:MySqlConnector.Logging.IMySqlConnectorLoggerProvider.CreateLogger(System.String)">
            <summary>
            Creates a logger with the specified name. This method may be called from multiple threads and must be thread-safe.
            </summary>
        </member>
        <member name="T:MySqlConnector.Logging.Log.__SessionMadeConnectionStruct">
            <summary> This API supports the logging infrastructure and is not intended to be used directly from your code. It is subject to change in the future. </summary>
        </member>
        <member name="T:MySqlConnector.Logging.Log.__ConnectingToIpAddressStruct">
            <summary> This API supports the logging infrastructure and is not intended to be used directly from your code. It is subject to change in the future. </summary>
        </member>
        <member name="T:MySqlConnector.Logging.Log.__FailedToConnectToIpAddressStruct">
            <summary> This API supports the logging infrastructure and is not intended to be used directly from your code. It is subject to change in the future. </summary>
        </member>
        <member name="T:MySqlConnector.Logging.Log.__CouldNotLoadCaCertificateFromFileStruct">
            <summary> This API supports the logging infrastructure and is not intended to be used directly from your code. It is subject to change in the future. </summary>
        </member>
        <member name="T:MySqlConnector.Logging.Log.__ConnectedTlsDetailedPreliminaryStruct">
            <summary> This API supports the logging infrastructure and is not intended to be used directly from your code. It is subject to change in the future. </summary>
        </member>
        <member name="T:MySqlConnector.Logging.Log.__SuccessfullyPingedServerStruct">
            <summary> This API supports the logging infrastructure and is not intended to be used directly from your code. It is subject to change in the future. </summary>
        </member>
        <member name="T:MySqlConnector.Logging.MySqlConnectorLogManager">
            <summary>
            Controls logging for MySqlConnector.
            </summary>
        </member>
        <member name="P:MySqlConnector.Logging.MySqlConnectorLogManager.Provider">
            <summary>
            Allows the <see cref="T:MySqlConnector.Logging.IMySqlConnectorLoggerProvider"/> to be set for this library. <see cref="P:MySqlConnector.Logging.MySqlConnectorLogManager.Provider"/> can
            be set once, and must be set before any other library methods are used.
            </summary>
        </member>
        <member name="T:MySqlConnector.Logging.NoOpLogger">
            <summary>
            <see cref="T:MySqlConnector.Logging.NoOpLogger"/> is an implementation of <see cref="T:MySqlConnector.Logging.IMySqlConnectorLogger"/> that does nothing.
            </summary>
            <remarks>This is the default logging implementation unless <see cref="P:MySqlConnector.Logging.MySqlConnectorLogManager.Provider"/> is set.</remarks>
        </member>
        <member name="M:MySqlConnector.Logging.NoOpLogger.IsEnabled(MySqlConnector.Logging.MySqlConnectorLogLevel)">
            <summary>
            Returns <c>false</c>.
            </summary>
        </member>
        <member name="M:MySqlConnector.Logging.NoOpLogger.Log(MySqlConnector.Logging.MySqlConnectorLogLevel,System.String,System.Object[],System.Exception)">
            <summary>
            Ignores the specified log message.
            </summary>
        </member>
        <member name="P:MySqlConnector.Logging.NoOpLogger.Instance">
            <summary>
            Returns a singleton instance of <see cref="T:MySqlConnector.Logging.NoOpLogger"/>.
            </summary>
        </member>
        <member name="T:MySqlConnector.Logging.NoOpLoggerProvider">
            <summary>
            Creates loggers that do nothing.
            </summary>
        </member>
        <member name="M:MySqlConnector.Logging.NoOpLoggerProvider.CreateLogger(System.String)">
            <summary>
            Returns a <see cref="T:MySqlConnector.Logging.NoOpLogger"/>.
            </summary>
        </member>
        <member name="T:MySqlConnector.MySqlAttribute">
            <summary>
            <see cref="T:MySqlConnector.MySqlAttribute"/> represents an attribute that can be sent with a MySQL query.
            </summary>
            <remarks>See <a href="https://dev.mysql.com/doc/refman/8.0/en/query-attributes.html">Query Attributes</a> for information on using query attributes.</remarks>
        </member>
        <member name="M:MySqlConnector.MySqlAttribute.#ctor(System.String,System.Object)">
            <summary>
            <see cref="T:MySqlConnector.MySqlAttribute"/> represents an attribute that can be sent with a MySQL query.
            </summary>
            <remarks>See <a href="https://dev.mysql.com/doc/refman/8.0/en/query-attributes.html">Query Attributes</a> for information on using query attributes.</remarks>
        </member>
        <member name="M:MySqlConnector.MySqlAttribute.#ctor">
            <summary>
            Initializes a new <see cref="T:MySqlConnector.MySqlAttribute"/>.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlAttribute.AttributeName">
            <summary>
            Gets or sets the attribute name.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlAttribute.Value">
            <summary>
            Gets or sets the attribute value.
            </summary>
        </member>
        <member name="M:MySqlConnector.MySqlAttribute.Clone">
            <summary>
            Returns a new <see cref="T:MySqlConnector.MySqlAttribute"/> with the same property values as this instance.
            </summary>
        </member>
        <member name="T:MySqlConnector.MySqlAttributeCollection">
            <summary>
            <see cref="T:MySqlConnector.MySqlAttributeCollection"/> represents a collection of query attributes that can be added to a <see cref="T:MySqlConnector.MySqlCommand"/>.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlAttributeCollection.Count">
            <summary>
            Returns the number of attributes in the collection.
            </summary>
        </member>
        <member name="M:MySqlConnector.MySqlAttributeCollection.Add(MySqlConnector.MySqlAttribute)">
            <summary>
            Adds a new <see cref="T:MySqlConnector.MySqlAttribute"/> to the collection.
            </summary>
            <param name="attribute">The attribute to add.</param>
            <remarks>The attribute name must not be empty, and must not already exist in the collection.</remarks>
        </member>
        <member name="M:MySqlConnector.MySqlAttributeCollection.SetAttribute(System.String,System.Object)">
            <summary>
            Sets the attribute with the specified name to the given value, overwriting it if it already exists.
            </summary>
            <param name="attributeName">The attribute name.</param>
            <param name="value">The attribute value.</param>
        </member>
        <member name="P:MySqlConnector.MySqlAttributeCollection.Item(System.Int32)">
            <summary>
            Gets the attribute at the specified index.
            </summary>
            <param name="index">The index.</param>
            <returns>The <see cref="T:MySqlConnector.MySqlAttribute"/> at that index.</returns>
        </member>
        <member name="M:MySqlConnector.MySqlAttributeCollection.Clear">
            <summary>
            Clears the collection.
            </summary>
        </member>
        <member name="M:MySqlConnector.MySqlAttributeCollection.GetEnumerator">
            <summary>
            Returns an enumerator for the collection.
            </summary>
        </member>
        <member name="M:MySqlConnector.MySqlAttributeCollection.Remove(MySqlConnector.MySqlAttribute)">
            <summary>
            Removes the specified attribute from the collection.
            </summary>
            <param name="attribute">The attribute to remove.</param>
            <returns><c>true</c> if that attribute was removed; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:MySqlConnector.MySqlAttributeCollection.System#Collections#IEnumerable#GetEnumerator">
            <summary>
            Returns an enumerator for the collection.
            </summary>
        </member>
        <member name="T:MySqlConnector.MySqlBatch">
             <summary>
             <para><see cref="T:MySqlConnector.MySqlBatch"/> implements the new
             <a href="https://github.com/dotnet/runtime/issues/28633">ADO.NET batching API</a>.
             <strong>It is currently experimental</strong> and may change in the future.</para>
             <para>When using MariaDB (10.2 or later), the commands will be sent in a single batch, reducing network
             round-trip time. With other MySQL Servers, this may be no more efficient than executing the commands
             individually.</para>
             <para>Example usage:</para>
             <code>
             using var connection = new MySqlConnection("...connection string...");
             await connection.OpenAsync();
            
             using var batch = new MySqlBatch(connection)
             {
             	BatchCommands =
             	{
             		new MySqlBatchCommand("INSERT INTO departments(name) VALUES(@name);")
             		{
             			Parameters =
             			{
             				new MySqlParameter("@name", "Sales"),
             			},
             		},
             		new MySqlBatchCommand("SET @dept_id = last_insert_id()"),
             		new MySqlBatchCommand("INSERT INTO employees(name, department_id) VALUES(@name, @dept_id);")
             		{
             			Parameters =
             			{
             				new MySqlParameter("@name", "Jim Halpert"),
             			},
             		},
             	 	new MySqlBatchCommand("INSERT INTO employees(name, department_id) VALUES(@name, @dept_id);")
             		{
             			Parameters =
             			{
             				new MySqlParameter("@name", "Dwight Schrute"),
             			},
             		},
             	},
              };
              await batch.ExecuteNonQueryAsync();
             </code>
             </summary>
             <remarks>The proposed ADO.NET API that <see cref="T:MySqlConnector.MySqlBatch"/> is based on is not finalized. This API is experimental and may change in the future.</remarks>
        </member>
        <member name="M:MySqlConnector.MySqlBatch.#ctor">
            <summary>
            Initializes a new <see cref="T:MySqlConnector.MySqlBatch"/> object. The <see cref="P:MySqlConnector.MySqlBatch.Connection"/> property must be set before this object can be used.
            </summary>
        </member>
        <member name="M:MySqlConnector.MySqlBatch.#ctor(MySqlConnector.MySqlConnection,MySqlConnector.MySqlTransaction)">
            <summary>
            Initializes a new <see cref="T:MySqlConnector.MySqlBatch"/> object, setting the <see cref="P:MySqlConnector.MySqlBatch.Connection"/> and <see cref="P:MySqlConnector.MySqlBatch.Transaction"/> if specified.
            </summary>
            <param name="connection">(Optional) The <see cref="T:MySqlConnector.MySqlConnection"/> to use.</param>
            <param name="transaction">(Optional) The <see cref="T:MySqlConnector.MySqlTransaction"/> to use.</param>
        </member>
        <member name="P:MySqlConnector.MySqlBatch.BatchCommands">
            <summary>
            The collection of commands that will be executed in the batch.
            </summary>
        </member>
        <member name="M:MySqlConnector.MySqlBatch.ExecuteReader(System.Data.CommandBehavior)">
            <summary>
            Executes all the commands in the batch, returning a <see cref="T:MySqlConnector.MySqlDataReader"/> that can iterate
            over the result sets. If multiple resultsets are returned, use <see cref="M:MySqlConnector.MySqlDataReader.NextResult"/>
            to access them.
            </summary>
        </member>
        <member name="M:MySqlConnector.MySqlBatch.ExecuteReaderAsync(System.Threading.CancellationToken)">
            <summary>
            Executes all the commands in the batch, returning a <see cref="T:MySqlConnector.MySqlDataReader"/> that can iterate
            over the result sets. If multiple resultsets are returned, use <see cref="M:MySqlConnector.MySqlDataReader.NextResultAsync(System.Threading.CancellationToken)"/>
            to access them.
            </summary>
            <param name="cancellationToken">A token to cancel the asynchronous operation.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task`1"/> containing the result of the asynchronous operation.</returns>
        </member>
        <member name="T:MySqlConnector.MySqlBulkCopy">
             <summary>
             <para><see cref="T:MySqlConnector.MySqlBulkCopy"/> lets you efficiently load a MySQL Server table with data from another source.
             It is similar to the <a href="https://docs.microsoft.com/en-us/dotnet/api/system.data.sqlclient.sqlbulkcopy">SqlBulkCopy</a> class
             for SQL Server.</para>
             <para>Due to <a href="https://mysqlconnector.net/troubleshooting/load-data-local-infile/">security features</a>
             in MySQL Server, the connection string <em>must</em> have <c>AllowLoadLocalInfile=true</c> in order
             to use this class.</para>
             <para>For data that is in CSV or TSV format, use <see cref="T:MySqlConnector.MySqlBulkLoader"/> to bulk load the file.</para>
             <para>Example code:</para>
             <code>
             // NOTE: to copy data between tables in the same database, use INSERT ... SELECT
             // https://dev.mysql.com/doc/refman/8.0/en/insert-select.html
             var dataTable = GetDataTableFromExternalSource();
            
             // open the connection
             using var connection = new MySqlConnection("...;AllowLoadLocalInfile=True");
             await connection.OpenAsync();
            
             // bulk copy the data
             var bulkCopy = new MySqlBulkCopy(connection);
             bulkCopy.DestinationTableName = "some_table_name";
             var result = await bulkCopy.WriteToServerAsync(dataTable);
            
             // check for problems
             if (result.Warnings.Count != 0) { /* handle potential data loss warnings */ }
             </code>
             </summary>
             <remarks><para><strong>Note:</strong> This API is a unique feature of MySqlConnector; you must
             <a href="https://mysqlconnector.net/overview/installing/">switch to MySqlConnector</a> in order to use it.</para>
             <para>This API is experimental and may change in the future.</para>
             </remarks>
        </member>
        <member name="M:MySqlConnector.MySqlBulkCopy.#ctor(MySqlConnector.MySqlConnection,MySqlConnector.MySqlTransaction)">
            <summary>
            Initializes a <see cref="T:MySqlConnector.MySqlBulkCopy"/> object with the specified connection, and optionally the active transaction.
            </summary>
            <param name="connection">The <see cref="T:MySqlConnector.MySqlConnection"/> to use.</param>
            <param name="transaction">(Optional) The <see cref="T:MySqlConnector.MySqlTransaction"/> to use.</param>
        </member>
        <member name="P:MySqlConnector.MySqlBulkCopy.ConflictOption">
            <summary>
            A <see cref="T:MySqlConnector.MySqlBulkLoaderConflictOption"/> value that specifies how conflicts are resolved (default <see cref="F:MySqlConnector.MySqlBulkLoaderConflictOption.None"/>).
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlBulkCopy.BulkCopyTimeout">
            <summary>
            The number of seconds for the operation to complete before it times out, or <c>0</c> for no timeout.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlBulkCopy.DestinationTableName">
            <summary>
            The name of the table to insert rows into.
            </summary>
            <remarks>This name needs to be quoted if it contains special characters.</remarks>
        </member>
        <member name="P:MySqlConnector.MySqlBulkCopy.NotifyAfter">
            <summary>
            If non-zero, this specifies the number of rows to be processed before generating a notification event.
            </summary>
        </member>
        <member name="E:MySqlConnector.MySqlBulkCopy.MySqlRowsCopied">
            <summary>
            This event is raised every time that the number of rows specified by the <see cref="P:MySqlConnector.MySqlBulkCopy.NotifyAfter"/> property have been processed.
            </summary>
            <remarks>
            <para>Receipt of a RowsCopied event does not imply that any rows have been sent to the server or committed.</para>
            <para>The <see cref="P:MySqlConnector.MySqlRowsCopiedEventArgs.Abort"/> property can be set to <c>true</c> by the event handler to abort the copy.</para>
            </remarks>
        </member>
        <member name="P:MySqlConnector.MySqlBulkCopy.ColumnMappings">
            <summary>
            A collection of <see cref="T:MySqlConnector.MySqlBulkCopyColumnMapping"/> objects. If the columns being copied from the
            data source line up one-to-one with the columns in the destination table then populating this collection is
            unnecessary. Otherwise, this should be filled with a collection of <see cref="T:MySqlConnector.MySqlBulkCopyColumnMapping"/> objects
            specifying how source columns are to be mapped onto destination columns. If one column mapping is specified,
            then all must be specified.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlBulkCopy.RowsCopied">
            <summary>
            Returns the number of rows that were copied (after <c>WriteToServer(Async)</c> finishes).
            </summary>
        </member>
        <member name="M:MySqlConnector.MySqlBulkCopy.WriteToServer(System.Data.DataTable)">
            <summary>
            Copies all rows in the supplied <see cref="T:System.Data.DataTable"/> to the destination table specified by the
            <see cref="P:MySqlConnector.MySqlBulkCopy.DestinationTableName"/> property of the <see cref="T:MySqlConnector.MySqlBulkCopy"/> object.
            </summary>
            <param name="dataTable">The <see cref="T:System.Data.DataTable"/> to copy.</param>
            <returns>A <see cref="T:MySqlConnector.MySqlBulkCopyResult"/> with the result of the bulk copy operation.</returns>
        </member>
        <member name="M:MySqlConnector.MySqlBulkCopy.WriteToServerAsync(System.Data.DataTable,System.Threading.CancellationToken)">
            <summary>
            Asynchronously copies all rows in the supplied <see cref="T:System.Data.DataTable"/> to the destination table specified by the
            <see cref="P:MySqlConnector.MySqlBulkCopy.DestinationTableName"/> property of the <see cref="T:MySqlConnector.MySqlBulkCopy"/> object.
            </summary>
            <param name="dataTable">The <see cref="T:System.Data.DataTable"/> to copy.</param>
            <param name="cancellationToken">A token to cancel the asynchronous operation.</param>
            <returns>A <see cref="T:MySqlConnector.MySqlBulkCopyResult"/> with the result of the bulk copy operation.</returns>
        </member>
        <member name="M:MySqlConnector.MySqlBulkCopy.WriteToServer(System.Collections.Generic.IEnumerable{System.Data.DataRow},System.Int32)">
            <summary>
            Copies all rows in the supplied sequence of <see cref="T:System.Data.DataRow"/> objects to the destination table specified by the
            <see cref="P:MySqlConnector.MySqlBulkCopy.DestinationTableName"/> property of the <see cref="T:MySqlConnector.MySqlBulkCopy"/> object. The number of columns
            to be read from the <see cref="T:System.Data.DataRow"/> objects must be specified in advance.
            </summary>
            <param name="dataRows">The collection of <see cref="T:System.Data.DataRow"/> objects.</param>
            <param name="columnCount">The number of columns to copy (in each row).</param>
            <returns>A <see cref="T:MySqlConnector.MySqlBulkCopyResult"/> with the result of the bulk copy operation.</returns>
        </member>
        <member name="M:MySqlConnector.MySqlBulkCopy.WriteToServerAsync(System.Collections.Generic.IEnumerable{System.Data.DataRow},System.Int32,System.Threading.CancellationToken)">
            <summary>
            Asynchronously copies all rows in the supplied sequence of <see cref="T:System.Data.DataRow"/> objects to the destination table specified by the
            <see cref="P:MySqlConnector.MySqlBulkCopy.DestinationTableName"/> property of the <see cref="T:MySqlConnector.MySqlBulkCopy"/> object. The number of columns
            to be read from the <see cref="T:System.Data.DataRow"/> objects must be specified in advance.
            </summary>
            <param name="dataRows">The collection of <see cref="T:System.Data.DataRow"/> objects.</param>
            <param name="columnCount">The number of columns to copy (in each row).</param>
            <param name="cancellationToken">A token to cancel the asynchronous operation.</param>
            <returns>A <see cref="T:MySqlConnector.MySqlBulkCopyResult"/> with the result of the bulk copy operation.</returns>
        </member>
        <member name="M:MySqlConnector.MySqlBulkCopy.WriteToServer(System.Data.IDataReader)">
            <summary>
            Copies all rows in the supplied <see cref="T:System.Data.IDataReader"/> to the destination table specified by the
            <see cref="P:MySqlConnector.MySqlBulkCopy.DestinationTableName"/> property of the <see cref="T:MySqlConnector.MySqlBulkCopy"/> object.
            </summary>
            <param name="dataReader">The <see cref="T:System.Data.IDataReader"/> to copy from.</param>
            <returns>A <see cref="T:MySqlConnector.MySqlBulkCopyResult"/> with the result of the bulk copy operation.</returns>
        </member>
        <member name="M:MySqlConnector.MySqlBulkCopy.WriteToServerAsync(System.Data.IDataReader,System.Threading.CancellationToken)">
            <summary>
            Asynchronously copies all rows in the supplied <see cref="T:System.Data.IDataReader"/> to the destination table specified by the
            <see cref="P:MySqlConnector.MySqlBulkCopy.DestinationTableName"/> property of the <see cref="T:MySqlConnector.MySqlBulkCopy"/> object.
            </summary>
            <param name="dataReader">The <see cref="T:System.Data.IDataReader"/> to copy from.</param>
            <param name="cancellationToken">A token to cancel the asynchronous operation.</param>
            <returns>A <see cref="T:MySqlConnector.MySqlBulkCopyResult"/> with the result of the bulk copy operation.</returns>
        </member>
        <member name="T:MySqlConnector.MySqlBulkCopyColumnMapping">
            <summary>
            <para>Use <see cref="T:MySqlConnector.MySqlBulkCopyColumnMapping"/> to specify how to map columns in the source data to
            columns in the destination table when using <see cref="T:MySqlConnector.MySqlBulkCopy"/>.</para>
            <para>Set <see cref="P:MySqlConnector.MySqlBulkCopyColumnMapping.SourceOrdinal"/> to the zero-based index of the source column to map. Set <see cref="P:MySqlConnector.MySqlBulkCopyColumnMapping.DestinationColumn"/> to
            either the name of a column in the destination table, or the name of a user-defined variable.
            If a user-defined variable, you can use <see cref="P:MySqlConnector.MySqlBulkCopyColumnMapping.Expression"/> to specify a MySQL expression that assigns
            its value to destination column.</para>
            <para>Source columns that don't have an entry in <see cref="P:MySqlConnector.MySqlBulkCopy.ColumnMappings"/> will be ignored
            (unless the <see cref="P:MySqlConnector.MySqlBulkCopy.ColumnMappings"/> collection is empty, in which case all columns will be mapped
            one-to-one).</para>
            <para>MySqlConnector will transmit all binary data as hex, so any expression that operates on it
            must decode it with the <c>UNHEX</c> function first. (This will be performed automatically if no
            <see cref="P:MySqlConnector.MySqlBulkCopyColumnMapping.Expression"/> is specified, but will be necessary to specify manually for more complex expressions.)</para>
            <para>Example code:</para>
            <code>
            new MySqlBulkCopyColumnMapping
            {
                SourceOrdinal = 2,
                DestinationColumn = "user_name",
            },
            new MySqlBulkCopyColumnMapping
            {
                SourceOrdinal = 0,
                DestinationColumn = "@tmp",
                Expression = "column_value = @tmp * 2",
            },
            </code>
            </summary>
            <param name="sourceOrdinal">The zero-based ordinal position of the source column.</param>
            <param name="destinationColumn">The name of the destination column.</param>
            <param name="expression">The optional expression to be used to set the destination column.</param>
        </member>
        <member name="M:MySqlConnector.MySqlBulkCopyColumnMapping.#ctor(System.Int32,System.String,System.String)">
            <summary>
            <para>Use <see cref="T:MySqlConnector.MySqlBulkCopyColumnMapping"/> to specify how to map columns in the source data to
            columns in the destination table when using <see cref="T:MySqlConnector.MySqlBulkCopy"/>.</para>
            <para>Set <see cref="P:MySqlConnector.MySqlBulkCopyColumnMapping.SourceOrdinal"/> to the zero-based index of the source column to map. Set <see cref="P:MySqlConnector.MySqlBulkCopyColumnMapping.DestinationColumn"/> to
            either the name of a column in the destination table, or the name of a user-defined variable.
            If a user-defined variable, you can use <see cref="P:MySqlConnector.MySqlBulkCopyColumnMapping.Expression"/> to specify a MySQL expression that assigns
            its value to destination column.</para>
            <para>Source columns that don't have an entry in <see cref="P:MySqlConnector.MySqlBulkCopy.ColumnMappings"/> will be ignored
            (unless the <see cref="P:MySqlConnector.MySqlBulkCopy.ColumnMappings"/> collection is empty, in which case all columns will be mapped
            one-to-one).</para>
            <para>MySqlConnector will transmit all binary data as hex, so any expression that operates on it
            must decode it with the <c>UNHEX</c> function first. (This will be performed automatically if no
            <see cref="P:MySqlConnector.MySqlBulkCopyColumnMapping.Expression"/> is specified, but will be necessary to specify manually for more complex expressions.)</para>
            <para>Example code:</para>
            <code>
            new MySqlBulkCopyColumnMapping
            {
                SourceOrdinal = 2,
                DestinationColumn = "user_name",
            },
            new MySqlBulkCopyColumnMapping
            {
                SourceOrdinal = 0,
                DestinationColumn = "@tmp",
                Expression = "column_value = @tmp * 2",
            },
            </code>
            </summary>
            <param name="sourceOrdinal">The zero-based ordinal position of the source column.</param>
            <param name="destinationColumn">The name of the destination column.</param>
            <param name="expression">The optional expression to be used to set the destination column.</param>
        </member>
        <member name="M:MySqlConnector.MySqlBulkCopyColumnMapping.#ctor">
            <summary>
            Initializes <see cref="T:MySqlConnector.MySqlBulkCopyColumnMapping"/> with the default values.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlBulkCopyColumnMapping.SourceOrdinal">
            <summary>
            The zero-based ordinal position of the source column to map from.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlBulkCopyColumnMapping.DestinationColumn">
            <summary>
            The name of the destination column to copy to. To use an expression, this should be the name of a unique user-defined variable.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlBulkCopyColumnMapping.Expression">
            <summary>
            An optional expression for setting a destination column. To use an expression, the <see cref="P:MySqlConnector.MySqlBulkCopyColumnMapping.DestinationColumn"/> should
            be set to the name of a user-defined variable and this expression should set a column using that variable.
            </summary>
            <remarks>To populate a binary column, you must set <see cref="P:MySqlConnector.MySqlBulkCopyColumnMapping.DestinationColumn"/> to a variable name, and <see cref="P:MySqlConnector.MySqlBulkCopyColumnMapping.Expression"/> to an
            expression that uses <code>UNHEX</code> to set the column value, e.g., <code>`destColumn` = UNHEX(@variableName)</code>.</remarks>
        </member>
        <member name="T:MySqlConnector.MySqlBulkCopyResult">
            <summary>
            Represents the result of a <see cref="T:MySqlConnector.MySqlBulkCopy"/> operation.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlBulkCopyResult.Warnings">
            <summary>
            The warnings, if any. Users of <see cref="T:MySqlConnector.MySqlBulkCopy"/> should check that this collection is empty to avoid
            potential data loss from failed data type conversions.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlBulkCopyResult.RowsInserted">
            <summary>
            The number of rows that were inserted during the bulk copy operation.
            </summary>
        </member>
        <member name="T:MySqlConnector.MySqlBulkLoader">
            <summary>
            <para><see cref="T:MySqlConnector.MySqlBulkLoader"/> lets you efficiently load a MySQL Server Table with data from a CSV or TSV file or <see cref="T:System.IO.Stream"/>.</para>
            <para>Example code:</para>
            <code>
            using var connection = new MySqlConnection("...;AllowLoadLocalInfile=True");
            await connection.OpenAsync();
            var bulkLoader = new MySqlBulkLoader(connection)
            {
            	FileName = @"C:\Path\To\file.csv",
            	TableName = "destination",
            	CharacterSet = "UTF8",
            	NumberOfLinesToSkip = 1,
            	FieldTerminator = ",",
            	FieldQuotationCharacter = '"',
            	FieldQuotationOptional = true,
            	Local = true,
            }
            var rowCount = await bulkLoader.LoadAsync();
            </code>
            </summary>
            <remarks>Due to <a href="https://mysqlconnector.net/troubleshooting/load-data-local-infile/">security features</a>
            in MySQL Server, the connection string <strong>must</strong> have <c>AllowLoadLocalInfile=true</c> in order to use a local source.
            </remarks>
        </member>
        <member name="P:MySqlConnector.MySqlBulkLoader.CharacterSet">
            <summary>
            (Optional) The character set of the source data. By default, the database's character set is used.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlBulkLoader.Columns">
            <summary>
            (Optional) A list of the column names in the destination table that should be filled with data from the input file.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlBulkLoader.ConflictOption">
            <summary>
            A <see cref="T:MySqlConnector.MySqlBulkLoaderConflictOption"/> value that specifies how conflicts are resolved (default <see cref="F:MySqlConnector.MySqlBulkLoaderConflictOption.None"/>).
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlBulkLoader.Connection">
            <summary>
            The <see cref="T:MySqlConnector.MySqlConnection"/> to use.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlBulkLoader.EscapeCharacter">
            <summary>
            (Optional) The character used to escape instances of <see cref="P:MySqlConnector.MySqlBulkLoader.FieldQuotationCharacter"/> within field values.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlBulkLoader.Expressions">
            <summary>
            (Optional) A list of expressions used to set field values from the columns in the source data.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlBulkLoader.FieldQuotationCharacter">
            <summary>
            (Optional) The character used to enclose fields in the source data.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlBulkLoader.FieldQuotationOptional">
            <summary>
            Whether quoting fields is optional (default <c>false</c>).
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlBulkLoader.FieldTerminator">
            <summary>
            (Optional) The string fields are terminated with.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlBulkLoader.FileName">
            <summary>
            The name of the local (if <see cref="P:MySqlConnector.MySqlBulkLoader.Local"/> is <c>true</c>) or remote (otherwise) file to load.
            Either this or <see cref="P:MySqlConnector.MySqlBulkLoader.SourceStream"/> must be set.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlBulkLoader.LinePrefix">
            <summary>
            (Optional) A prefix in each line that should be skipped when loading.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlBulkLoader.LineTerminator">
            <summary>
            (Optional) The string lines are terminated with.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlBulkLoader.Local">
            <summary>
            Whether a local file is being used (default <c>true</c>).
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlBulkLoader.NumberOfLinesToSkip">
            <summary>
            The number of lines to skip at the beginning of the file (default <c>0</c>).
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlBulkLoader.Priority">
            <summary>
            A <see cref="T:MySqlConnector.MySqlBulkLoaderPriority"/> giving the priority to load with (default <see cref="F:MySqlConnector.MySqlBulkLoaderPriority.None"/>).
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlBulkLoader.SourceStream">
            <summary>
            A <see cref="T:System.IO.Stream"/> containing the data to load. Either this or <see cref="P:MySqlConnector.MySqlBulkLoader.FileName"/> must be set.
            The <see cref="P:MySqlConnector.MySqlBulkLoader.Local"/> property must be <c>true</c> if this is set.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlBulkLoader.TableName">
            <summary>
            The name of the table to load into. If this is a reserved word or contains spaces, it must be quoted.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlBulkLoader.Timeout">
            <summary>
            The timeout (in milliseconds) to use.
            </summary>
        </member>
        <member name="M:MySqlConnector.MySqlBulkLoader.#ctor(MySqlConnector.MySqlConnection)">
            <summary>
            Initializes a new instance of the <see cref="T:MySqlConnector.MySqlBulkLoader"/> class with the specified <see cref="T:MySqlConnector.MySqlConnection"/>.
            </summary>
            <param name="connection">The <see cref="T:MySqlConnector.MySqlConnection"/> to use.</param>
        </member>
        <member name="M:MySqlConnector.MySqlBulkLoader.Load">
            <summary>
            Loads all data in the source file or stream into the destination table.
            </summary>
            <returns>The number of rows inserted.</returns>
        </member>
        <member name="M:MySqlConnector.MySqlBulkLoader.LoadAsync">
            <summary>
            Asynchronously loads all data in the source file or stream into the destination table.
            </summary>
            <returns>A <see cref="T:System.Threading.Tasks.Task`1"/> that will be completed with the number of rows inserted.</returns>
        </member>
        <member name="M:MySqlConnector.MySqlBulkLoader.LoadAsync(System.Threading.CancellationToken)">
            <summary>
            Asynchronously loads all data in the source file or stream into the destination table.
            </summary>
            <param name="cancellationToken">A token to cancel the asynchronous operation.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task`1"/> that will be completed with the number of rows inserted.</returns>
        </member>
        <member name="F:MySqlConnector.MySqlCertificateStoreLocation.None">
            <summary>
            Do not use certificate store
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlCertificateStoreLocation.CurrentUser">
            <summary>
            Use certificate store for the current user
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlCertificateStoreLocation.LocalMachine">
            <summary>
            User certificate store for the machine
            </summary>
        </member>
        <member name="T:MySqlConnector.MySqlCommand">
            <summary>
            <see cref="T:MySqlConnector.MySqlCommand"/> represents a SQL statement or stored procedure name
            to execute against a MySQL database.
            </summary>
        </member>
        <member name="M:MySqlConnector.MySqlCommand.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:MySqlConnector.MySqlCommand"/> class.
            </summary>
        </member>
        <member name="M:MySqlConnector.MySqlCommand.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:MySqlConnector.MySqlCommand"/> class, setting <see cref="P:MySqlConnector.MySqlCommand.CommandText"/> to <paramref name="commandText"/>.
            </summary>
            <param name="commandText">The text to assign to <see cref="P:MySqlConnector.MySqlCommand.CommandText"/>.</param>
        </member>
        <member name="M:MySqlConnector.MySqlCommand.#ctor(MySqlConnector.MySqlConnection,MySqlConnector.MySqlTransaction)">
            <summary>
            Initializes a new instance of the <see cref="T:MySqlConnector.MySqlCommand"/> class with the specified <see cref="T:MySqlConnector.MySqlConnection"/> and <see cref="T:MySqlConnector.MySqlTransaction"/>.
            </summary>
            <param name="connection">The <see cref="T:MySqlConnector.MySqlConnection"/> to use.</param>
            <param name="transaction">The active <see cref="T:MySqlConnector.MySqlTransaction"/>, if any.</param>
        </member>
        <member name="M:MySqlConnector.MySqlCommand.#ctor(System.String,MySqlConnector.MySqlConnection)">
            <summary>
            Initializes a new instance of the <see cref="T:MySqlConnector.MySqlCommand"/> class with the specified command text and <see cref="T:MySqlConnector.MySqlConnection"/>.
            </summary>
            <param name="commandText">The text to assign to <see cref="P:MySqlConnector.MySqlCommand.CommandText"/>.</param>
            <param name="connection">The <see cref="T:MySqlConnector.MySqlConnection"/> to use.</param>
        </member>
        <member name="M:MySqlConnector.MySqlCommand.#ctor(System.String,MySqlConnector.MySqlConnection,MySqlConnector.MySqlTransaction)">
            <summary>
            Initializes a new instance of the <see cref="T:MySqlConnector.MySqlCommand"/> class with the specified command text,<see cref="T:MySqlConnector.MySqlConnection"/>, and <see cref="T:MySqlConnector.MySqlTransaction"/>.
            </summary>
            <param name="commandText">The text to assign to <see cref="P:MySqlConnector.MySqlCommand.CommandText"/>.</param>
            <param name="connection">The <see cref="T:MySqlConnector.MySqlConnection"/> to use.</param>
            <param name="transaction">The active <see cref="T:MySqlConnector.MySqlTransaction"/>, if any.</param>
        </member>
        <member name="P:MySqlConnector.MySqlCommand.Parameters">
            <summary>
            The collection of <see cref="T:MySqlConnector.MySqlParameter"/> objects for this command.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlCommand.Attributes">
            <summary>
            The collection of <see cref="T:MySqlConnector.MySqlAttribute"/> objects for this command.
            </summary>
        </member>
        <member name="M:MySqlConnector.MySqlCommand.Cancel">
            <inheritdoc/>
        </member>
        <member name="M:MySqlConnector.MySqlCommand.ExecuteNonQuery">
            <summary>
            Executes this command on the associated <see cref="T:MySqlConnector.MySqlConnection"/>.
            </summary>
            <returns>The number of rows affected.</returns>
            <remarks>For UPDATE, INSERT, and DELETE statements, the return value is the number of rows affected by the command.
            For stored procedures, the return value is the number of rows affected by the last statement in the stored procedure,
            or zero if the last statement is a SELECT. For all other types of statements, the return value is -1.</remarks>
        </member>
        <member name="M:MySqlConnector.MySqlCommand.Prepare">
            <inheritdoc/>
        </member>
        <member name="P:MySqlConnector.MySqlCommand.CommandText">
            <summary>
            Gets or sets the command text to execute.
            </summary>
            <remarks>If <see cref="P:MySqlConnector.MySqlCommand.CommandType"/> is <see cref="F:System.Data.CommandType.Text"/>, this is one or more SQL statements to execute.
            If <see cref="P:MySqlConnector.MySqlCommand.CommandType"/> is <see cref="F:System.Data.CommandType.StoredProcedure"/>, this is the name of the stored procedure; any
            special characters in the stored procedure name must be quoted or escaped.</remarks>
        </member>
        <member name="P:MySqlConnector.MySqlCommand.CommandTimeout">
            <inheritdoc/>
        </member>
        <member name="P:MySqlConnector.MySqlCommand.CommandType">
            <inheritdoc/>
        </member>
        <member name="P:MySqlConnector.MySqlCommand.DesignTimeVisible">
            <inheritdoc/>
        </member>
        <member name="P:MySqlConnector.MySqlCommand.UpdatedRowSource">
            <inheritdoc/>
        </member>
        <member name="P:MySqlConnector.MySqlCommand.LastInsertedId">
            <summary>
            Holds the first automatically-generated ID for a value inserted in an <c>AUTO_INCREMENT</c> column in the last statement.
            </summary>
            <remarks>
            See <a href="https://dev.mysql.com/doc/refman/8.0/en/information-functions.html#function_last-insert-id"><c>LAST_INSERT_ID()</c></a> for more information.
            </remarks>
        </member>
        <member name="M:MySqlConnector.MySqlCommand.ExecuteNonQueryAsync(System.Threading.CancellationToken)">
            <summary>
            Executes this command asynchronously on the associated <see cref="T:MySqlConnector.MySqlConnection"/>.
            </summary>
            <param name="cancellationToken">A token to cancel the asynchronous operation.</param>
            <returns>A task representing the asynchronous operation.</returns>
            <remarks>For UPDATE, INSERT, and DELETE statements, the return value is the number of rows affected by the command.
            For stored procedures, the return value is the number of rows affected by the last statement in the stored procedure,
            or zero if the last statement is a SELECT. For all other types of statements, the return value is -1.</remarks>
        </member>
        <member name="M:MySqlConnector.MySqlCommand.MySqlConnector#Core#ICancellableCommand#RegisterCancel(System.Threading.CancellationToken)">
            <summary>
            Registers <see cref="M:MySqlConnector.MySqlCommand.Cancel"/> as a callback with <paramref name="cancellationToken"/> if cancellation is supported.
            </summary>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/>.</param>
            <returns>An object that must be disposed to revoke the cancellation registration.</returns>
            <remarks>This method is more efficient than calling <code>token.Register(Command.Cancel)</code> because it avoids
            unnecessary allocations.</remarks>
        </member>
        <member name="T:MySqlConnector.MySqlConnection">
            <summary>
            <see cref="T:MySqlConnector.MySqlConnection"/> represents a connection to a MySQL database.
            </summary>
        </member>
        <member name="M:MySqlConnector.MySqlConnection.BeginTransaction">
            <summary>
            Begins a database transaction.
            </summary>
            <returns>A <see cref="T:MySqlConnector.MySqlTransaction"/> representing the new database transaction.</returns>
            <remarks>Transactions may not be nested.</remarks>
        </member>
        <member name="M:MySqlConnector.MySqlConnection.BeginTransaction(System.Data.IsolationLevel)">
            <summary>
            Begins a database transaction.
            </summary>
            <param name="isolationLevel">The <see cref="T:System.Data.IsolationLevel"/> for the transaction.</param>
            <returns>A <see cref="T:MySqlConnector.MySqlTransaction"/> representing the new database transaction.</returns>
            <remarks>Transactions may not be nested.</remarks>
        </member>
        <member name="M:MySqlConnector.MySqlConnection.BeginTransaction(System.Data.IsolationLevel,System.Boolean)">
            <summary>
            Begins a database transaction.
            </summary>
            <param name="isolationLevel">The <see cref="T:System.Data.IsolationLevel"/> for the transaction.</param>
            <param name="isReadOnly">If <c>true</c>, changes to tables used in the transaction are prohibited; otherwise, they are permitted.</param>
            <returns>A <see cref="T:MySqlConnector.MySqlTransaction"/> representing the new database transaction.</returns>
            <remarks>Transactions may not be nested.</remarks>
        </member>
        <member name="M:MySqlConnector.MySqlConnection.BeginDbTransaction(System.Data.IsolationLevel)">
            <summary>
            Begins a database transaction.
            </summary>
            <param name="isolationLevel">The <see cref="T:System.Data.IsolationLevel"/> for the transaction.</param>
            <returns>A <see cref="T:MySqlConnector.MySqlTransaction"/> representing the new database transaction.</returns>
        </member>
        <member name="M:MySqlConnector.MySqlConnection.BeginTransactionAsync(System.Threading.CancellationToken)">
            <summary>
            Begins a database transaction asynchronously.
            </summary>
            <param name="cancellationToken">A token to cancel the asynchronous operation.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task`1"/> representing the new database transaction.</returns>
            <remarks>Transactions may not be nested.</remarks>
        </member>
        <member name="M:MySqlConnector.MySqlConnection.BeginTransactionAsync(System.Data.IsolationLevel,System.Threading.CancellationToken)">
            <summary>
            Begins a database transaction asynchronously.
            </summary>
            <param name="isolationLevel">The <see cref="T:System.Data.IsolationLevel"/> for the transaction.</param>
            <param name="cancellationToken">A token to cancel the asynchronous operation.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task`1"/> representing the new database transaction.</returns>
            <remarks>Transactions may not be nested.</remarks>
        </member>
        <member name="M:MySqlConnector.MySqlConnection.BeginTransactionAsync(System.Data.IsolationLevel,System.Boolean,System.Threading.CancellationToken)">
            <summary>
            Begins a database transaction asynchronously.
            </summary>
            <param name="isolationLevel">The <see cref="T:System.Data.IsolationLevel"/> for the transaction.</param>
            <param name="isReadOnly">If <c>true</c>, changes to tables used in the transaction are prohibited; otherwise, they are permitted.</param>
            <param name="cancellationToken">A token to cancel the asynchronous operation.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task`1"/> representing the new database transaction.</returns>
            <remarks>Transactions may not be nested.</remarks>
        </member>
        <member name="M:MySqlConnector.MySqlConnection.GetStartTransactionPayload(System.Data.IsolationLevel,System.Nullable{System.Boolean},System.Boolean)">
            <summary>
            Returns a <see cref="T:System.ReadOnlyMemory`1"/> containing two payloads to set the isolation level and start a transaction.
            </summary>
        </member>
        <member name="M:MySqlConnector.MySqlConnection.ResetConnectionAsync(System.Threading.CancellationToken)">
            <summary>
            Resets the session state of the current open connection; this clears temporary tables and user-defined variables.
            </summary>
            <param name="cancellationToken">A token to cancel the asynchronous operation.</param>
            <returns>A <c>ValueTask</c> representing the asynchronous operation.</returns>
            <remarks>This is an optional feature of the MySQL protocol and may not be supported by all servers.
            It's known to be supported by MySQL Server 5.7.3 (and later) and MariaDB 10.2.4 (and later).
            Other MySQL-compatible servers or proxies may not support this command.</remarks>
        </member>
        <member name="P:MySqlConnector.MySqlConnection.ServerThread">
            <summary>
            The connection ID from MySQL Server.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlConnection.ProvideClientCertificatesCallback">
            <summary>
            Gets or sets the delegate used to provide client certificates for connecting to a server.
            </summary>
            <remarks>The provided <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection"/> should be filled with the client certificate(s) needed to connect to the server.</remarks>
        </member>
        <member name="P:MySqlConnector.MySqlConnection.ProvidePasswordCallback">
            <summary>
            Gets or sets the delegate used to generate a password for new database connections.
            </summary>
            <remarks>
            <para>This delegate is executed when a new database connection is opened that requires a password. Due to
            connection pooling, this delegate is only executed when a new physical connection is established with a database
            server, not when a connection is retrieved from the pool.</para>
            <para>The <see cref="P:MySqlConnector.MySqlConnectionStringBuilder.Password"/> option takes precedence over this
            delegate if it is specified.</para>
            <para>Using this delegate can make more efficient use of connection pooling for servers that require
            frequently-changing passwords or authentication tokens. Changing the password in the connection string
            will create unique connection pools; this delegate allows a single connection pool to use multiple passwords.</para>
            </remarks>
        </member>
        <member name="P:MySqlConnector.MySqlConnection.RemoteCertificateValidationCallback">
            <summary>
            Gets or sets the delegate used to verify that the server's certificate is valid.
            </summary>
            <remarks><see cref="P:MySqlConnector.MySqlConnectionStringBuilder.SslMode"/> must be set to <see cref="F:MySqlConnector.MySqlSslMode.Preferred"/>
            or <see cref="F:MySqlConnector.MySqlSslMode.Required"/> in order for this delegate to be invoked. See the documentation for
            <see cref="P:MySqlConnector.MySqlConnection.RemoteCertificateValidationCallback"/> for more information on the values passed to this delegate.</remarks>
        </member>
        <member name="M:MySqlConnector.MySqlConnection.ClearPool(MySqlConnector.MySqlConnection)">
            <summary>
            Clears the connection pool that <paramref name="connection"/> belongs to.
            </summary>
            <param name="connection">The <see cref="T:MySqlConnector.MySqlConnection"/> whose connection pool will be cleared.</param>
        </member>
        <member name="M:MySqlConnector.MySqlConnection.ClearPoolAsync(MySqlConnector.MySqlConnection,System.Threading.CancellationToken)">
            <summary>
            Asynchronously clears the connection pool that <paramref name="connection"/> belongs to.
            </summary>
            <param name="connection">The <see cref="T:MySqlConnector.MySqlConnection"/> whose connection pool will be cleared.</param>
            <param name="cancellationToken">A token to cancel the asynchronous operation.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> representing the asynchronous operation.</returns>
        </member>
        <member name="M:MySqlConnector.MySqlConnection.ClearAllPools">
            <summary>
            Clears all connection pools.
            </summary>
        </member>
        <member name="M:MySqlConnector.MySqlConnection.ClearAllPoolsAsync(System.Threading.CancellationToken)">
            <summary>
            Asynchronously clears all connection pools.
            </summary>
            <param name="cancellationToken">A token to cancel the asynchronous operation.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> representing the asynchronous operation.</returns>
        </member>
        <member name="M:MySqlConnector.MySqlConnection.GetSchema">
            <summary>
            Returns schema information for the data source of this <see cref="T:MySqlConnector.MySqlConnection"/>.
            </summary>
            <returns>A <see cref="T:System.Data.DataTable"/> containing schema information.</returns>
        </member>
        <member name="M:MySqlConnector.MySqlConnection.GetSchema(System.String)">
            <summary>
            Returns schema information for the data source of this <see cref="T:MySqlConnector.MySqlConnection"/>.
            </summary>
            <param name="collectionName">The name of the schema to return. See <a href="https://mysqlconnector.net/overview/schema-collections/">Supported Schema Collections</a> for the list of supported schema names.</param>
            <returns>A <see cref="T:System.Data.DataTable"/> containing schema information.</returns>
        </member>
        <member name="M:MySqlConnector.MySqlConnection.GetSchema(System.String,System.String[])">
            <summary>
            Returns schema information for the data source of this <see cref="T:MySqlConnector.MySqlConnection"/>.
            </summary>
            <param name="collectionName">The name of the schema to return. See <a href="https://mysqlconnector.net/overview/schema-collections/">Supported Schema Collections</a> for the list of supported schema names.</param>
            <param name="restrictionValues">The restrictions to apply to the schema.</param>
            <returns>A <see cref="T:System.Data.DataTable"/> containing schema information.</returns>
        </member>
        <member name="M:MySqlConnector.MySqlConnection.GetSchemaAsync(System.Threading.CancellationToken)">
            <summary>
            Asynchronously returns schema information for the data source of this <see cref="T:MySqlConnector.MySqlConnection"/>.
            </summary>
            <param name="cancellationToken">A token to cancel the asynchronous operation.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task`1"/> containing schema information.</returns>
            <remarks>The proposed ADO.NET API that this is based on is not finalized; this API may change in the future.</remarks>
        </member>
        <member name="M:MySqlConnector.MySqlConnection.GetSchemaAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Asynchronously returns schema information for the data source of this <see cref="T:MySqlConnector.MySqlConnection"/>.
            </summary>
            <param name="collectionName">The name of the schema to return.</param>
            <param name="cancellationToken">A token to cancel the asynchronous operation.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task`1"/> containing schema information.</returns>
            <remarks>The proposed ADO.NET API that this is based on is not finalized; this API may change in the future.</remarks>
        </member>
        <member name="M:MySqlConnector.MySqlConnection.GetSchemaAsync(System.String,System.String[],System.Threading.CancellationToken)">
            <summary>
            Asynchronously returns schema information for the data source of this <see cref="T:MySqlConnector.MySqlConnection"/>.
            </summary>
            <param name="collectionName">The name of the schema to return.</param>
            <param name="restrictionValues">The restrictions to apply to the schema.</param>
            <param name="cancellationToken">A token to cancel the asynchronous operation.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task`1"/> containing schema information.</returns>
            <remarks>The proposed ADO.NET API that this is based on is not finalized; this API may change in the future.</remarks>
        </member>
        <member name="P:MySqlConnector.MySqlConnection.ConnectionTimeout">
            <summary>
            Gets the time (in seconds) to wait while trying to establish a connection
            before terminating the attempt and generating an error. This value
            is controlled by <see cref="P:MySqlConnector.MySqlConnectionStringBuilder.ConnectionTimeout"/>,
            which defaults to 15 seconds.
            </summary>
        </member>
        <member name="M:MySqlConnector.MySqlConnection.CreateBatch">
            <summary>
            Creates a <see cref="T:MySqlConnector.MySqlBatch"/> object for executing batched commands.
            </summary>
        </member>
        <member name="M:MySqlConnector.MySqlConnection.CloneWith(System.String)">
            <summary>
            Returns an unopened copy of this connection with a new connection string. If the <c>Password</c>
            in <paramref name="connectionString"/> is not set, the password from this connection will be used.
            This allows creating a new connection with the same security information while changing other options,
            such as database or pooling.
            </summary>
            <param name="connectionString">The new connection string to be used.</param>
            <returns>A new <see cref="T:MySqlConnector.MySqlConnection"/> with different connection string options but
            the same password as this connection (unless overridden by <paramref name="connectionString"/>).</returns>
        </member>
        <member name="T:MySqlConnector.MySqlConnectionOpenedCallback">
            <summary>
            A callback that is invoked when a new <see cref="T:MySqlConnector.MySqlConnection"/> is opened.
            </summary>
            <param name="context">A <see cref="T:MySqlConnector.MySqlConnectionOpenedContext"/> giving information about the connection being opened.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> that can be used to cancel the asynchronous operation.</param>
            <returns>A <see cref="T:System.Threading.Tasks.ValueTask"/> representing the result of the possibly-asynchronous operation.</returns>
        </member>
        <member name="T:MySqlConnector.MySqlConnectionOpenedConditions">
            <summary>
            Bitflags giving the conditions under which a connection was opened.
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlConnectionOpenedConditions.None">
            <summary>
            No specific conditions apply. This value may be used when an existing pooled connection is reused without being reset.
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlConnectionOpenedConditions.New">
            <summary>
            A new physical connection to a MySQL Server was opened. This value is mutually exclusive with <see cref="F:MySqlConnector.MySqlConnectionOpenedConditions.Reset"/>.
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlConnectionOpenedConditions.Reset">
            <summary>
            An existing pooled connection to a MySQL Server was reset. This value is mutually exclusive with <see cref="F:MySqlConnector.MySqlConnectionOpenedConditions.New"/>.
            </summary>
        </member>
        <member name="T:MySqlConnector.MySqlConnectionOpenedContext">
            <summary>
            Contains information passed to <see cref="T:MySqlConnector.MySqlConnectionOpenedCallback"/> when a new <see cref="T:MySqlConnector.MySqlConnection"/> is opened.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlConnectionOpenedContext.Connection">
            <summary>
            The <see cref="T:MySqlConnector.MySqlConnection"/> that was opened.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlConnectionOpenedContext.Conditions">
            <summary>
            Bitflags giving the conditions under which a connection was opened.
            </summary>
        </member>
        <member name="T:MySqlConnector.MySqlConnectionProtocol">
            <summary>
            Specifies the type of connection to make to the server.
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlConnectionProtocol.Sockets">
            <summary>
            TCP/IP connection.
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlConnectionProtocol.Pipe">
            <summary>
            Named pipe connection. Only works on Windows.
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlConnectionProtocol.UnixSocket">
            <summary>
            Unix domain socket connection. Only works on Unix/Linux.
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlConnectionProtocol.SharedMemory">
            <summary>
            Shared memory connection. Not currently supported.
            </summary>
        </member>
        <member name="T:MySqlConnector.MySqlConnectionStringBuilder">
            <summary>
            <see cref="T:MySqlConnector.MySqlConnectionStringBuilder"/> allows you to construct a MySQL connection string by setting properties on the builder then reading the <see cref="P:System.Data.Common.DbConnectionStringBuilder.ConnectionString"/> property.
            </summary>
            <remarks>See <a href="https://mysqlconnector.net/connection-options/">Connection String Options</a> for more documentation on the options.</remarks>
        </member>
        <member name="M:MySqlConnector.MySqlConnectionStringBuilder.#ctor">
            <summary>
            Initializes a new <see cref="T:MySqlConnector.MySqlConnectionStringBuilder"/>.
            </summary>
        </member>
        <member name="M:MySqlConnector.MySqlConnectionStringBuilder.#ctor(System.String)">
            <summary>
            Initializes a new <see cref="T:MySqlConnector.MySqlConnectionStringBuilder"/> with properties set from the specified connection string.
            </summary>
            <param name="connectionString">The connection string to use to set property values on this object.</param>
        </member>
        <member name="P:MySqlConnector.MySqlConnectionStringBuilder.Server">
            <summary>
            <para>The host name or network address of the MySQL Server to which to connect. Multiple hosts can be specified in a comma-delimited list.</para>
            <para>On Unix-like systems, this can be a fully qualified path to a MySQL socket file, which will cause a Unix socket to be used instead of a TCP/IP socket. Only a single socket name can be specified.</para>
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlConnectionStringBuilder.Port">
            <summary>
            The TCP port on which MySQL Server is listening for connections.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlConnectionStringBuilder.UserID">
            <summary>
            The MySQL user ID.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlConnectionStringBuilder.Password">
            <summary>
            The password for the MySQL user.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlConnectionStringBuilder.Database">
            <summary>
            (Optional) The case-sensitive name of the initial database to use. This may be required if the MySQL user account only has access rights to particular databases on the server.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlConnectionStringBuilder.LoadBalance">
            <summary>
            Specifies how load is distributed across backend servers.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlConnectionStringBuilder.ConnectionProtocol">
            <summary>
            The protocol to use to connect to the MySQL Server.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlConnectionStringBuilder.PipeName">
            <summary>
            The name of the Windows named pipe to use to connect to the server. You must also set <see cref="P:MySqlConnector.MySqlConnectionStringBuilder.ConnectionProtocol"/> to <see cref="F:MySqlConnector.MySqlConnectionProtocol.NamedPipe"/> to used named pipes.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlConnectionStringBuilder.SslMode">
            <summary>
            Whether to use SSL/TLS when connecting to the MySQL server.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlConnectionStringBuilder.CertificateFile">
            <summary>
            The path to a certificate file in PKCS #12 (.pfx) format containing a bundled Certificate and Private Key used for mutual authentication.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlConnectionStringBuilder.CertificatePassword">
            <summary>
            The password for the certificate specified using the <see cref="P:MySqlConnector.MySqlConnectionStringBuilder.CertificateFile"/> option. Not required if the certificate file is not password protected.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlConnectionStringBuilder.CertificateStoreLocation">
            <summary>
            Uses a certificate from the specified Certificate Store on the machine. The default value of <see cref="F:MySqlConnector.MySqlCertificateStoreLocation.None"/> means the certificate store is not used; a value of <see cref="F:MySqlConnector.MySqlCertificateStoreLocation.CurrentUser"/> or <see cref="F:MySqlConnector.MySqlCertificateStoreLocation.LocalMachine"/> uses the specified store.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlConnectionStringBuilder.CertificateThumbprint">
            <summary>
            Specifies which certificate should be used from the Certificate Store specified in <see cref="P:MySqlConnector.MySqlConnectionStringBuilder.CertificateStoreLocation"/>. This option must be used to indicate which certificate in the store should be used for authentication.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlConnectionStringBuilder.SslCert">
            <summary>
            The path to the client’s SSL certificate file in PEM format. <see cref="P:MySqlConnector.MySqlConnectionStringBuilder.SslKey"/> must also be specified, and <see cref="P:MySqlConnector.MySqlConnectionStringBuilder.CertificateFile"/> should not be.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlConnectionStringBuilder.SslKey">
            <summary>
            The path to the client’s SSL private key in PEM format. <see cref="P:MySqlConnector.MySqlConnectionStringBuilder.SslCert"/> must also be specified, and <see cref="P:MySqlConnector.MySqlConnectionStringBuilder.CertificateFile"/> should not be.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlConnectionStringBuilder.CACertificateFile">
            <summary>
            Use <see cref="P:MySqlConnector.MySqlConnectionStringBuilder.SslCa"/> instead.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlConnectionStringBuilder.SslCa">
            <summary>
            The path to a CA certificate file in a PEM Encoded (.pem) format. This should be used with a value for the <see cref="P:MySqlConnector.MySqlConnectionStringBuilder.SslMode"/> property of <see cref="F:MySqlConnector.MySqlSslMode.VerifyCA"/> or <see cref="F:MySqlConnector.MySqlSslMode.VerifyFull"/> to enable verification of a CA certificate that is not trusted by the operating system’s certificate store.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlConnectionStringBuilder.TlsVersion">
            <summary>
            The TLS versions which may be used during TLS negotiation, or empty to use OS defaults.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlConnectionStringBuilder.TlsCipherSuites">
            <summary>
            The TLS cipher suites which may be used during TLS negotiation. The default value (the empty string) allows the OS to determine the TLS cipher suites to use; this is the recommended setting.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlConnectionStringBuilder.Pooling">
            <summary>
            Enables connection pooling.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlConnectionStringBuilder.ConnectionLifeTime">
            <summary>
            The maximum lifetime (in seconds) for any connection, or <c>0</c> for no lifetime limit.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlConnectionStringBuilder.ConnectionReset">
            <summary>
            Whether connections are reset when being retrieved from the pool.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlConnectionStringBuilder.DeferConnectionReset">
            <summary>
            This option is no longer supported.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlConnectionStringBuilder.ConnectionIdlePingTime">
            <summary>
            This option is no longer supported.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlConnectionStringBuilder.ConnectionIdleTimeout">
            <summary>
            The amount of time (in seconds) that a connection can remain idle in the pool.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlConnectionStringBuilder.MinimumPoolSize">
            <summary>
            The minimum number of connections to leave in the pool if <see cref="P:MySqlConnector.MySqlConnectionStringBuilder.ConnectionIdleTimeout"/> is reached.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlConnectionStringBuilder.MaximumPoolSize">
            <summary>
            The maximum number of connections allowed in the pool.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlConnectionStringBuilder.DnsCheckInterval">
            <summary>
            The number of seconds between checks for DNS changes, or 0 to disable periodic checks.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlConnectionStringBuilder.AllowLoadLocalInfile">
            <summary>
            Allows the <c>LOAD DATA LOCAL</c> command to request files from the client.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlConnectionStringBuilder.AllowPublicKeyRetrieval">
            <summary>
            Allows the client to automatically request the RSA public key from the server.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlConnectionStringBuilder.AllowUserVariables">
            <summary>
            Allows user-defined variables (prefixed with <c>@</c>) to be used in SQL statements.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlConnectionStringBuilder.AllowZeroDateTime">
            <summary>
            Returns <c>DATETIME</c> fields as <see cref="T:MySqlConnector.MySqlDateTime"/> objects instead of <see cref="T:System.DateTime"/> objects.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlConnectionStringBuilder.ApplicationName">
            <summary>
            Sets the <c>program_name</c> connection attribute passed to MySQL Server.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlConnectionStringBuilder.AutoEnlist">
            <summary>
            Automatically enlists this connection in any active <see cref="T:System.Transactions.TransactionScope"/>.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlConnectionStringBuilder.CancellationTimeout">
            <summary>
            The length of time (in seconds) to wait for a query to be canceled when <see cref="P:MySqlConnector.MySqlCommand.CommandTimeout"/> expires, or zero for no timeout.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlConnectionStringBuilder.CharacterSet">
            <summary>
            Supported for backwards compatibility; MySqlConnector always uses <c>utf8mb4</c>.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlConnectionStringBuilder.ConnectionTimeout">
            <summary>
            The length of time (in seconds) to wait for a connection to the server before terminating the attempt and generating an error.
            The default value is 15.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlConnectionStringBuilder.ConvertZeroDateTime">
            <summary>
            Whether invalid <c>DATETIME</c> fields should be converted to <see cref="F:System.DateTime.MinValue"/>.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlConnectionStringBuilder.DateTimeKind">
            <summary>
            The <see cref="P:MySqlConnector.MySqlConnectionStringBuilder.DateTimeKind"/> to use when deserializing <c>DATETIME</c> values.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlConnectionStringBuilder.DefaultCommandTimeout">
            <summary>
            The length of time (in seconds) each command can execute before the query is cancelled on the server, or zero to disable timeouts.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlConnectionStringBuilder.ForceSynchronous">
            <summary>
            Forces all async methods to execute synchronously. This can be useful for debugging.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlConnectionStringBuilder.GuidFormat">
            <summary>
            Determines which column type (if any) should be read as a <see cref="T:System.Guid"/>.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlConnectionStringBuilder.IgnoreCommandTransaction">
            <summary>
            Does not check the <see cref="P:MySqlConnector.MySqlCommand.Transaction"/> property for validity when executing a command.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlConnectionStringBuilder.IgnorePrepare">
            <summary>
            Ignores calls to <see cref="M:MySqlConnector.MySqlCommand.Prepare"/> and <c>PrepareAsync</c>.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlConnectionStringBuilder.InteractiveSession">
            <summary>
            Instructs the MySQL server that this is an interactive session.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlConnectionStringBuilder.Keepalive">
            <summary>
            TCP Keepalive idle time (in seconds), or 0 to use OS defaults.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlConnectionStringBuilder.NoBackslashEscapes">
            <summary>
            Doesn't escape backslashes in string parameters. For use with the <c>NO_BACKSLASH_ESCAPES</c> MySQL server mode.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlConnectionStringBuilder.OldGuids">
            <summary>
            Use the <see cref="P:MySqlConnector.MySqlConnectionStringBuilder.GuidFormat"/> property instead.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlConnectionStringBuilder.PersistSecurityInfo">
            <summary>
            If true, preserves security-sensitive information in the connection string retrieved from any open <see cref="T:MySqlConnector.MySqlConnection"/>.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlConnectionStringBuilder.Pipelining">
            <summary>
            Enables query pipelining.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlConnectionStringBuilder.ServerRedirectionMode">
            <summary>
            Whether to use server redirection.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlConnectionStringBuilder.ServerRsaPublicKeyFile">
            <summary>
            The path to a file containing the server's RSA public key.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlConnectionStringBuilder.ServerSPN">
            <summary>
            The server’s Service Principal Name (for <c>auth_gssapi_client</c> authentication).
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlConnectionStringBuilder.TreatTinyAsBoolean">
            <summary>
            Returns <c>TINYINT(1)</c> fields as <see cref="T:System.Boolean"/> values.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlConnectionStringBuilder.UseAffectedRows">
            <summary>
            Report changed rows instead of found rows.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlConnectionStringBuilder.UseCompression">
            <summary>
            Compress packets sent to and from the server.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlConnectionStringBuilder.UseXaTransactions">
            <summary>
            Use XA transactions to implement <see cref="T:System.Transactions.TransactionScope"/> distributed transactions.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlConnectionStringBuilder.Keys">
            <summary>
            Returns an <see cref="T:System.Collections.ICollection"/> that contains the keys in the <see cref="T:MySqlConnector.MySqlConnectionStringBuilder"/>.
            </summary>
        </member>
        <member name="M:MySqlConnector.MySqlConnectionStringBuilder.ContainsKey(System.String)">
            <summary>
            Whether this <see cref="T:MySqlConnector.MySqlConnectionStringBuilder"/> contains a set option with the specified name.
            </summary>
            <param name="keyword">The option name.</param>
            <returns><c>true</c> if an option with that name is set; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:MySqlConnector.MySqlConnectionStringBuilder.Remove(System.String)">
            <summary>
            Removes the option with the specified name.
            </summary>
            <param name="keyword">The option name.</param>
        </member>
        <member name="P:MySqlConnector.MySqlConnectionStringBuilder.Item(System.String)">
            <summary>
            Retrieves an option value by name.
            </summary>
            <param name="key">The option name.</param>
            <returns>That option's value, if set.</returns>
        </member>
        <member name="M:MySqlConnector.MySqlConnectionStringBuilder.GetProperties(System.Collections.Hashtable)">
            <summary>
            Fills in <paramref name="propertyDescriptors"/> with information about the available properties on this object.
            </summary>
            <param name="propertyDescriptors">The collection of <see cref="T:System.ComponentModel.PropertyDescriptor"/> objects to populate.</param>
        </member>
        <member name="T:MySqlConnector.MySqlConnectorFactory">
            <summary>
            An implementation of <see cref="T:System.Data.Common.DbProviderFactory"/> that creates MySqlConnector objects.
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlConnectorFactory.Instance">
            <summary>
            Provides an instance of <see cref="T:System.Data.Common.DbProviderFactory"/> that can create MySqlConnector objects.
            </summary>
        </member>
        <member name="M:MySqlConnector.MySqlConnectorFactory.CreateCommand">
            <summary>
            Creates a new <see cref="T:MySqlConnector.MySqlCommand"/> object.
            </summary>
        </member>
        <member name="M:MySqlConnector.MySqlConnectorFactory.CreateConnection">
            <summary>
            Creates a new <see cref="T:MySqlConnector.MySqlConnection"/> object.
            </summary>
        </member>
        <member name="M:MySqlConnector.MySqlConnectorFactory.CreateConnectionStringBuilder">
            <summary>
            Creates a new <see cref="T:MySqlConnector.MySqlConnectionStringBuilder"/> object.
            </summary>
        </member>
        <member name="M:MySqlConnector.MySqlConnectorFactory.CreateParameter">
            <summary>
            Creates a new <see cref="T:MySqlConnector.MySqlParameter"/> object.
            </summary>
        </member>
        <member name="M:MySqlConnector.MySqlConnectorFactory.CreateCommandBuilder">
            <summary>
            Creates a new <see cref="T:MySqlConnector.MySqlCommandBuilder"/> object.
            </summary>
        </member>
        <member name="M:MySqlConnector.MySqlConnectorFactory.CreateDataAdapter">
            <summary>
            Creates a new <see cref="T:MySqlConnector.MySqlDataAdapter"/> object.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlConnectorFactory.CanCreateDataSourceEnumerator">
            <summary>
            Returns <c>false</c>.
            </summary>
            <remarks><see cref="T:System.Data.Common.DbDataSourceEnumerator"/> is not supported by MySqlConnector.</remarks>
        </member>
        <member name="M:MySqlConnector.MySqlConnectorFactory.CreateBatch">
            <summary>
            Creates a new <see cref="T:MySqlConnector.MySqlBatch"/> object.
            </summary>
        </member>
        <member name="M:MySqlConnector.MySqlConnectorFactory.CreateBatchCommand">
            <summary>
            Creates a new <see cref="T:MySqlConnector.MySqlBatchCommand"/> object.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlConnectorFactory.CanCreateBatch">
            <summary>
            Returns <c>true</c>.
            </summary>
        </member>
        <member name="M:MySqlConnector.MySqlConnectorFactory.CreateDataSource(System.String)">
            <summary>
            Creates a new <see cref="T:MySqlConnector.MySqlDataSource"/> object.
            </summary>
            <param name="connectionString">The connection string.</param>
        </member>
        <member name="T:MySqlConnector.MySqlConversionException">
            <summary>
            <see cref="T:MySqlConnector.MySqlConversionException"/> is thrown when a MySQL value can't be converted to another type.
            </summary>
        </member>
        <member name="M:MySqlConnector.MySqlConversionException.#ctor(System.String)">
            <summary>
            Initializes a new instance of <see cref="T:MySqlConnector.MySqlConversionException"/>.
            </summary>
            <param name="message">The exception message.</param>
        </member>
        <member name="P:MySqlConnector.MySqlDataReader.RecordsAffected">
            <summary>
            Gets the number of rows changed, inserted, or deleted by execution of the SQL statement.
            </summary>
            <remarks>For UPDATE, INSERT, and DELETE statements, the return value is the number of rows affected by the command.
            For stored procedures, the return value is the number of rows affected by the last statement in the stored procedure,
            or zero if the last statement is a SELECT. For all other types of statements, the return value is -1.</remarks>
        </member>
        <member name="M:MySqlConnector.MySqlDataReader.GetSchemaTable">
            <summary>
            Returns a <see cref="T:System.Data.DataTable"/> that contains metadata about the columns in the result set.
            </summary>
            <returns>A <see cref="T:System.Data.DataTable"/> containing metadata about the columns in the result set.</returns>
        </member>
        <member name="M:MySqlConnector.MySqlDataReader.GetSchemaTableAsync(System.Threading.CancellationToken)">
            <summary>
            Returns a <see cref="T:System.Data.DataTable"/> that contains metadata about the columns in the result set.
            </summary>
            <param name="cancellationToken">A token to cancel the operation.</param>
            <returns>A <see cref="T:System.Data.DataTable"/> containing metadata about the columns in the result set.</returns>
            <remarks>This method runs synchronously; prefer to call <see cref="M:MySqlConnector.MySqlDataReader.GetSchemaTable"/> to avoid the overhead of allocating an unnecessary <c>Task</c>.</remarks>
        </member>
        <member name="M:MySqlConnector.MySqlDataReader.GetColumnSchema">
            <summary>
            Returns metadata about the columns in the result set.
            </summary>
            <returns>A <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection`1"/> containing metadata about the result set.</returns>
        </member>
        <member name="M:MySqlConnector.MySqlDataReader.GetColumnSchemaAsync(System.Threading.CancellationToken)">
            <summary>
            Returns metadata about the columns in the result set.
            </summary>
            <param name="cancellationToken">A token to cancel the operation.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> containing <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection`1"/> containing metadata about the result set.</returns>
            <remarks>This method runs synchronously; prefer to call <see cref="M:MySqlConnector.MySqlDataReader.GetColumnSchema"/> to avoid the overhead of allocating an unnecessary <c>Task</c>.</remarks>
        </member>
        <member name="T:MySqlConnector.MySqlDataSource">
            <summary>
            <see cref="T:MySqlConnector.MySqlDataSource"/> implements a MySQL data source which can be used to obtain open connections.
            </summary>
        </member>
        <member name="M:MySqlConnector.MySqlDataSource.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:MySqlConnector.MySqlDataSource"/> class.
            </summary>
            <param name="connectionString">The connection string for the MySQL Server. This parameter is required.</param>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="connectionString"/> is <c>null</c>.</exception>
        </member>
        <member name="M:MySqlConnector.MySqlDataSource.CreateConnection">
            <summary>
            Creates a new <see cref="T:MySqlConnector.MySqlConnection"/> that can connect to the database represented by this <see cref="T:MySqlConnector.MySqlDataSource"/>.
            </summary>
            <remarks>
            <para>The connection must be opened before it can be used.</para>
            <para>It is the responsibility of the caller to properly dispose the connection returned by this method. Failure to do so may result in a connection leak.</para>
            </remarks>
        </member>
        <member name="M:MySqlConnector.MySqlDataSource.OpenConnection">
            <summary>
            Returns a new, open <see cref="T:MySqlConnector.MySqlConnection"/> to the database represented by this <see cref="T:MySqlConnector.MySqlDataSource"/>.
            </summary>
            <remarks>
            <para>The returned connection is already open, and is ready for immediate use.</para>
            <para>It is the responsibility of the caller to properly dispose the connection returned by this method. Failure to do so may result in a connection leak.</para>
            </remarks>
        </member>
        <member name="M:MySqlConnector.MySqlDataSource.OpenConnectionAsync(System.Threading.CancellationToken)">
            <summary>
            Asynchronously returns a new, open <see cref="T:MySqlConnector.MySqlConnection"/> to the database represented by this <see cref="T:MySqlConnector.MySqlDataSource"/>.
            </summary>
            <param name="cancellationToken">A token to cancel the asynchronous operation.</param>
            <remarks>
            <para>The returned connection is already open, and is ready for immediate use.</para>
            <para>It is the responsibility of the caller to properly dispose the connection returned by this method. Failure to do so may result in a connection leak.</para>
            </remarks>
        </member>
        <member name="P:MySqlConnector.MySqlDataSource.ConnectionString">
            <summary>
            Gets the connection string of the database represented by this <see cref="T:MySqlConnector.MySqlDataSource"/>.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlDataSource.Password">
            <summary>
            Sets the password that will be used by the next <see cref="T:MySqlConnector.MySqlConnection"/> created from this <see cref="T:MySqlConnector.MySqlDataSource"/>.
            </summary>
            <remarks>
            <para>This can be used to update the password for database servers that periodically rotate authentication tokens, without
            affecting connection pooling. The <see cref="P:MySqlConnector.MySqlConnectionStringBuilder.Password"/> property must not be specified in
            order for this field to be used.</para>
            <para>Consider using <see cref="M:MySqlConnector.MySqlDataSourceBuilder.UsePeriodicPasswordProvider(System.Func{MySqlConnector.MySqlProvidePasswordContext,System.Threading.CancellationToken,System.Threading.Tasks.ValueTask{System.String}},System.TimeSpan,System.TimeSpan)"/> instead.</para>
            </remarks>
        </member>
        <member name="T:MySqlConnector.MySqlDataSourceBuilder">
            <summary>
            <see cref="T:MySqlConnector.MySqlDataSourceBuilder"/> provides an API for configuring and creating a <see cref="T:MySqlConnector.MySqlDataSource"/>,
            from which <see cref="T:MySqlConnector.MySqlConnection"/> objects can be obtained.
            </summary>
        </member>
        <member name="M:MySqlConnector.MySqlDataSourceBuilder.#ctor(System.String)">
            <summary>
            Initializes a new <see cref="T:MySqlConnector.MySqlDataSourceBuilder"/> with the specified connection string.
            </summary>
            <param name="connectionString">The optional connection string to use.</param>
        </member>
        <member name="M:MySqlConnector.MySqlDataSourceBuilder.UseLoggerFactory(Microsoft.Extensions.Logging.ILoggerFactory)">
            <summary>
            Sets the <see cref="T:Microsoft.Extensions.Logging.ILoggerFactory"/> that will be used for logging.
            </summary>
            <param name="loggerFactory">The logger factory.</param>
            <returns>This builder, so that method calls can be chained.</returns>
        </member>
        <member name="M:MySqlConnector.MySqlDataSourceBuilder.UseName(System.String)">
            <summary>
            Sets the name of the <see cref="T:MySqlConnector.MySqlDataSource"/> that will be created.
            </summary>
            <param name="name">The data source name.</param>
            <returns>This builder, so that method calls can be chained.</returns>
            <remarks>The connection pool name is used to set the <c>program_name</c> connection attribute
            (which is visible to some diagnostic tools) and the <c>pool.name</c> tag supplied with
            <a href="https://mysqlconnector.net/diagnostics/metrics/">connection pool metrics</a>.</remarks>
        </member>
        <member name="M:MySqlConnector.MySqlDataSourceBuilder.UseClientCertificatesCallback(System.Func{System.Security.Cryptography.X509Certificates.X509CertificateCollection,System.Threading.Tasks.ValueTask})">
            <summary>
            Sets the callback used to provide client certificates for connecting to a server.
            </summary>
            <param name="callback">The callback that will provide client certificates. The <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection"/>
            provided to the callback should be filled with the client certificate(s) needed to connect to the server.</param>
            <returns>This builder, so that method calls can be chained.</returns>
        </member>
        <member name="M:MySqlConnector.MySqlDataSourceBuilder.UsePeriodicPasswordProvider(System.Func{MySqlConnector.MySqlProvidePasswordContext,System.Threading.CancellationToken,System.Threading.Tasks.ValueTask{System.String}},System.TimeSpan,System.TimeSpan)">
            <summary>
            Configures a periodic password provider, which is automatically called by the data source at some regular interval. This is the
            recommended way to fetch a rotating access token.
            </summary>
            <param name="passwordProvider">A callback which returns the password to be used by any new MySQL connections that are made.</param>
            <param name="successRefreshInterval">How long to cache the password before re-invoking the callback.</param>
            <param name="failureRefreshInterval">How long to wait before re-invoking the callback on failure. This should
            typically be much shorter than <paramref name="successRefreshInterval"/>.</param>
            <returns>This builder, so that method calls can be chained.</returns>
        </member>
        <member name="M:MySqlConnector.MySqlDataSourceBuilder.UseRemoteCertificateValidationCallback(System.Net.Security.RemoteCertificateValidationCallback)">
            <summary>
            Sets the callback used to verify that the server's certificate is valid.
            </summary>
            <param name="callback">The callback used to verify that the server's certificate is valid.</param>
            <returns>This builder, so that method calls can be chained.</returns>
            <remarks><see cref="P:MySqlConnector.MySqlConnectionStringBuilder.SslMode"/> must be set to <see cref="F:MySqlConnector.MySqlSslMode.Preferred"/>
            or <see cref="F:MySqlConnector.MySqlSslMode.Required"/> in order for this delegate to be invoked. See the documentation for
            <see cref="T:System.Net.Security.RemoteCertificateValidationCallback"/> for more information on the values passed to this delegate.</remarks>
        </member>
        <member name="M:MySqlConnector.MySqlDataSourceBuilder.UseConnectionOpenedCallback(MySqlConnector.MySqlConnectionOpenedCallback)">
            <summary>
            Adds a callback that is invoked when a new <see cref="T:MySqlConnector.MySqlConnection"/> is opened.
            </summary>
            <param name="callback">The callback to invoke.</param>
            <returns>This builder, so that method calls can be chained.</returns>
        </member>
        <member name="M:MySqlConnector.MySqlDataSourceBuilder.Build">
            <summary>
            Builds a <see cref="T:MySqlConnector.MySqlDataSource"/> which is ready for use.
            </summary>
            <returns>A new <see cref="T:MySqlConnector.MySqlDataSource"/> with the settings configured through this <see cref="T:MySqlConnector.MySqlDataSourceBuilder"/>.</returns>
        </member>
        <member name="P:MySqlConnector.MySqlDataSourceBuilder.ConnectionStringBuilder">
            <summary>
            A <see cref="T:MySqlConnector.MySqlConnectionStringBuilder"/> that can be used to configure the connection string on this <see cref="T:MySqlConnector.MySqlDataSourceBuilder"/>.
            </summary>
        </member>
        <member name="T:MySqlConnector.MySqlDateTime">
            <summary>
            Represents a MySQL date/time value. This type can be used to store <c>DATETIME</c> values such
            as <c>0000-00-00</c> that can be stored in MySQL (when <see cref="P:MySqlConnector.MySqlConnectionStringBuilder.AllowZeroDateTime"/>
            is true) but can't be stored in a <see cref="T:System.DateTime"/> value.
            </summary>
            <param name="year">The year.</param>
            <param name="month">The (one-based) month.</param>
            <param name="day">The (one-based) day of the month.</param>
            <param name="hour">The hour.</param>
            <param name="minute">The minute.</param>
            <param name="second">The second.</param>
            <param name="microsecond">The microsecond.</param>
        </member>
        <member name="M:MySqlConnector.MySqlDateTime.#ctor(System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            Represents a MySQL date/time value. This type can be used to store <c>DATETIME</c> values such
            as <c>0000-00-00</c> that can be stored in MySQL (when <see cref="P:MySqlConnector.MySqlConnectionStringBuilder.AllowZeroDateTime"/>
            is true) but can't be stored in a <see cref="T:System.DateTime"/> value.
            </summary>
            <param name="year">The year.</param>
            <param name="month">The (one-based) month.</param>
            <param name="day">The (one-based) day of the month.</param>
            <param name="hour">The hour.</param>
            <param name="minute">The minute.</param>
            <param name="second">The second.</param>
            <param name="microsecond">The microsecond.</param>
        </member>
        <member name="M:MySqlConnector.MySqlDateTime.#ctor(System.DateTime)">
            <summary>
            Initializes a new instance of <see cref="T:MySqlConnector.MySqlDateTime"/> from a <see cref="T:System.DateTime"/>.
            </summary>
            <param name="dt">The <see cref="T:System.DateTime"/> whose values will be copied.</param>
        </member>
        <member name="M:MySqlConnector.MySqlDateTime.#ctor(MySqlConnector.MySqlDateTime)">
            <summary>
            Initializes a new instance of <see cref="T:MySqlConnector.MySqlDateTime"/> from another <see cref="T:MySqlConnector.MySqlDateTime"/>.
            </summary>
            <param name="other">The <see cref="T:MySqlConnector.MySqlDateTime"/> whose values will be copied.</param>
        </member>
        <member name="P:MySqlConnector.MySqlDateTime.IsValidDateTime">
            <summary>
            Returns <c>true</c> if this value is a valid <see cref="T:System.DateTime"/>.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlDateTime.Year">
            <summary>
            Gets or sets the year.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlDateTime.Month">
            <summary>
            Gets or sets the month.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlDateTime.Day">
            <summary>
            Gets or sets the day of the month.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlDateTime.Hour">
            <summary>
            Gets or sets the hour.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlDateTime.Minute">
            <summary>
            Gets or sets the minute.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlDateTime.Second">
            <summary>
            Gets or sets the second.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlDateTime.Microsecond">
            <summary>
            Gets or sets the microseconds.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlDateTime.Millisecond">
            <summary>
            Gets or sets the milliseconds.
            </summary>
        </member>
        <member name="M:MySqlConnector.MySqlDateTime.GetDateTime">
            <summary>
            Returns a <see cref="T:System.DateTime"/> value (if <see cref="P:MySqlConnector.MySqlDateTime.IsValidDateTime"/> is <c>true</c>), or throws a
            <see cref="T:MySqlConnector.MySqlConversionException"/>.
            </summary>
        </member>
        <member name="M:MySqlConnector.MySqlDateTime.ToString">
            <summary>
            Converts this object to a <see cref="T:System.String"/>.
            </summary>
        </member>
        <member name="M:MySqlConnector.MySqlDateTime.op_Explicit(MySqlConnector.MySqlDateTime)~System.DateTime">
            <summary>
            Converts this object to a <see cref="T:System.DateTime"/>.
            </summary>
        </member>
        <member name="M:MySqlConnector.MySqlDateTime.Equals(System.Object)">
            <summary>
            Returns <c>true</c> if this <see cref="T:MySqlConnector.MySqlDateTime"/> is equal to <paramref name="obj"/>.
            </summary>
            <param name="obj">The object to compare against for equality.</param>
            <returns><c>true</c> if the objects are equal, otherwise <c>false</c>.</returns>
        </member>
        <member name="M:MySqlConnector.MySqlDateTime.GetHashCode">
            <summary>
            Returns a hash code for this instance.
            </summary>
        </member>
        <member name="M:MySqlConnector.MySqlDateTime.System#IComparable#CompareTo(System.Object)">
            <summary>
            Compares this object to another <see cref="T:MySqlConnector.MySqlDateTime"/>.
            </summary>
            <param name="obj">The object to compare to.</param>
            <returns>An <see cref="T:System.Int32"/> giving the results of the comparison: a negative value if this
            object is less than <paramref name="obj"/>, zero if this object is equal, or a positive value if this
            object is greater.</returns>
        </member>
        <member name="M:MySqlConnector.MySqlDateTime.System#IComparable{MySqlConnector#MySqlDateTime}#CompareTo(MySqlConnector.MySqlDateTime)">
            <summary>
            Compares this object to another <see cref="T:MySqlConnector.MySqlDateTime"/>.
            </summary>
            <param name="other">The <see cref="T:MySqlConnector.MySqlDateTime"/> to compare to.</param>
            <returns>An <see cref="T:System.Int32"/> giving the results of the comparison: a negative value if this
            object is less than <paramref name="other"/>, zero if this object is equal, or a positive value if this
            object is greater.</returns>
        </member>
        <member name="T:MySqlConnector.MySqlDateTimeKind">
            <summary>
            The <see cref="T:System.DateTimeKind" /> used when reading <see cref="T:System.DateTime" /> from the database.
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlDateTimeKind.Unspecified">
            <summary>
            Use <see cref="F:System.DateTimeKind.Unspecified" /> when reading; allow any <see cref="T:System.DateTimeKind" /> in command parameters.
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlDateTimeKind.Utc">
            <summary>
            Use <see cref="F:System.DateTimeKind.Utc" /> when reading; reject <see cref="F:System.DateTimeKind.Local" /> in command parameters.
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlDateTimeKind.Local">
            <summary>
            Use <see cref="F:System.DateTimeKind.Local" /> when reading; reject <see cref="F:System.DateTimeKind.Utc" /> in command parameters.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlDbColumn.TableName">
            <summary>
            Gets the name of the table that the column belongs to. This will be the alias if the table is aliased in the query.
            </summary>
        </member>
        <member name="T:MySqlConnector.MySqlDecimal">
            <summary>
            <see cref="T:MySqlConnector.MySqlDecimal"/> represents a MySQL <c>DECIMAL</c> value that is too large to fit in a .NET <see cref="T:System.Decimal"/>.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlDecimal.Value">
            <summary>
            Gets the value of this <see cref="T:MySqlConnector.MySqlDecimal"/> as a <see cref="T:System.Decimal"/>.
            </summary>
            <remarks>This method will throw an <see cref="T:System.OverflowException"/> if the value is too large to be represented.</remarks>
        </member>
        <member name="M:MySqlConnector.MySqlDecimal.ToDouble">
            <summary>
            Gets the value of this <see cref="T:MySqlConnector.MySqlDecimal"/> as a <see cref="T:System.Double"/>.
            </summary>
            <remarks>The return value may have lost precision.</remarks>
        </member>
        <member name="M:MySqlConnector.MySqlDecimal.ToString">
            <summary>
            Gets the original value of this <see cref="T:MySqlConnector.MySqlDecimal"/> as a <see cref="T:System.String"/>.
            </summary>
        </member>
        <member name="T:MySqlConnector.MySqlError">
            <summary>
            <see cref="T:MySqlConnector.MySqlError"/> represents an error or warning that occurred during the execution of a SQL statement.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlError.Level">
            <summary>
            The error level. This comes from the MySQL Server. Possible values include <c>Note</c>, <c>Warning</c>, and <c>Error</c>.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlError.Code">
            <summary>
            The numeric error code. Prefer to use <see cref="P:MySqlConnector.MySqlError.ErrorCode"/>.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlError.ErrorCode">
            <summary>
            The <see cref="T:MySqlConnector.MySqlErrorCode"/> for the error or warning.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlError.Message">
            <summary>
            A human-readable description of the error or warning.
            </summary>
        </member>
        <member name="T:MySqlConnector.MySqlErrorCode">
            <summary>
            MySQL Server error codes. Taken from <a href="https://dev.mysql.com/doc/mysql-errors/8.0/en/server-error-reference.html">Server Error Codes and Messages</a>.
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.ProvidePasswordCallbackFailed">
            <summary>
            The delegate provided to <see cref="P:MySqlConnector.MySqlConnection.ProvidePasswordCallback"/> failed.
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.BulkCopyFailed">
            <summary>
            Not all rows from the source supplied to <see cref="T:MySqlConnector.MySqlBulkCopy"/> were copied to <see cref="P:MySqlConnector.MySqlBulkCopy.DestinationTableName"/>.
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.CommandTimeoutExpired">
            <summary>
            The timeout period specified by <see cref="P:MySqlConnector.MySqlCommand.CommandTimeout"/> elapsed before the operation completed.
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.HashCheck">
            <summary>
            ER_HASHCHK
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.ISAMCheck">
            <summary>
            ER_NISAMCHK
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.No">
            <summary>
            ER_NO
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.Yes">
            <summary>
            ER_YES
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.CannotCreateFile">
            <summary>
            ER_CANT_CREATE_FILE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.CannotCreateTable">
            <summary>
            ER_CANT_CREATE_TABLE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.CannotCreateDatabase">
            <summary>
            ER_CANT_CREATE_DB
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.DatabaseCreateExists">
            <summary>
            ER_DB_CREATE_EXISTS
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.DatabaseDropExists">
            <summary>
            ER_DB_DROP_EXISTS
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.DatabaseDropDelete">
            <summary>
            ER_DB_DROP_DELETE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.DatabaseDropRemoveDir">
            <summary>
            ER_DB_DROP_RMDIR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.CannotDeleteFile">
            <summary>
            ER_CANT_DELETE_FILE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.CannotFindSystemRecord">
            <summary>
            ER_CANT_FIND_SYSTEM_REC
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.CannotGetStatus">
            <summary>
            ER_CANT_GET_STAT
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.CannotGetWorkingDirectory">
            <summary>
            ER_CANT_GET_WD
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.CannotLock">
            <summary>
            ER_CANT_LOCK
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.CannotOpenFile">
            <summary>
            ER_CANT_OPEN_FILE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.FileNotFound">
            <summary>
            ER_FILE_NOT_FOUND
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.CannotReadDirectory">
            <summary>
            ER_CANT_READ_DIR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.CannotSetWorkingDirectory">
            <summary>
            ER_CANT_SET_WD
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.CheckRead">
            <summary>
            ER_CHECKREAD
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.DiskFull">
            <summary>
            ER_DISK_FULL
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.DuplicateKey">
            <summary>
            ER_DUP_KEY
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.ErrorOnClose">
            <summary>
            ER_ERROR_ON_CLOSE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.ErrorOnRead">
            <summary>
            ER_ERROR_ON_READ
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.ErrorOnRename">
            <summary>
            ER_ERROR_ON_RENAME
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.ErrorOnWrite">
            <summary>
            ER_ERROR_ON_WRITE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.FileUsed">
            <summary>
            ER_FILE_USED
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.FileSortAborted">
            <summary>
            ER_FILSORT_ABORT
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.FormNotFound">
            <summary>
            ER_FORM_NOT_FOUND
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.GetErrorNumber">
            <summary>
            ER_GET_ERRNO
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.IllegalHA">
            <summary>
            ER_ILLEGAL_HA
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.KeyNotFound">
            <summary>
            ER_KEY_NOT_FOUND
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.NotFormFile">
            <summary>
            ER_NOT_FORM_FILE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.NotKeyFile">
            <summary>
            ER_NOT_KEYFILE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.OldKeyFile">
            <summary>
            ER_OLD_KEYFILE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.OpenAsReadOnly">
            <summary>
            ER_OPEN_AS_READONLY
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.OutOfMemory">
            <summary>
            ER_OUTOFMEMORY
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.OutOfSortMemory">
            <summary>
            ER_OUT_OF_SORTMEMORY
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.UnexepectedEOF">
            <summary>
            ER_UNEXPECTED_EOF
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.ConnectionCountError">
            <summary>
            ER_CON_COUNT_ERROR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.OutOfResources">
            <summary>
            ER_OUT_OF_RESOURCES
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.UnableToConnectToHost">
            <summary>
            ER_BAD_HOST_ERROR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.HandshakeError">
            <summary>
            ER_HANDSHAKE_ERROR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.DatabaseAccessDenied">
            <summary>
            ER_DBACCESS_DENIED_ERROR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.AccessDenied">
            <summary>
            ER_ACCESS_DENIED_ERROR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.NoDatabaseSelected">
            <summary>
            ER_NO_DB_ERROR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.UnknownCommand">
            <summary>
            ER_UNKNOWN_COM_ERROR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.ColumnCannotBeNull">
            <summary>
            ER_BAD_NULL_ERROR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.UnknownDatabase">
            <summary>
            ER_BAD_DB_ERROR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.TableExists">
            <summary>
            ER_TABLE_EXISTS_ERROR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.BadTable">
            <summary>
            ER_BAD_TABLE_ERROR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.NonUnique">
            <summary>
            ER_NON_UNIQ_ERROR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.ServerShutdown">
            <summary>
            ER_SERVER_SHUTDOWN
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.BadFieldError">
            <summary>
            ER_BAD_FIELD_ERROR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.WrongFieldWithGroup">
            <summary>
            ER_WRONG_FIELD_WITH_GROUP
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.WrongGroupField">
            <summary>
            ER_WRONG_GROUP_FIELD
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.WrongSumSelected">
            <summary>
            ER_WRONG_SUM_SELECT
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.WrongValueCount">
            <summary>
            ER_WRONG_VALUE_COUNT
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.TooLongIdentifier">
            <summary>
            ER_TOO_LONG_IDENT
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.DuplicateFieldName">
            <summary>
            ER_DUP_FIELDNAME
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.DuplicateKeyName">
            <summary>
            ER_DUP_KEYNAME
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.DuplicateKeyEntry">
            <summary>
            ER_DUP_ENTRY
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.WrongFieldSpecifier">
            <summary>
            ER_WRONG_FIELD_SPEC
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.ParseError">
            <summary>
            You have an error in your SQL syntax (ER_PARSE_ERROR).
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.EmptyQuery">
            <summary>
            ER_EMPTY_QUERY
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.NonUniqueTable">
            <summary>
            ER_NONUNIQ_TABLE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.InvalidDefault">
            <summary>
            ER_INVALID_DEFAULT
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.MultiplePrimaryKey">
            <summary>
            ER_MULTIPLE_PRI_KEY
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.TooManyKeys">
            <summary>
            ER_TOO_MANY_KEYS
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.TooManyKeysParts">
            <summary>
            ER_TOO_MANY_KEY_PARTS
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.TooLongKey">
            <summary>
            ER_TOO_LONG_KEY
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.KeyColumnDoesNotExist">
            <summary>
            ER_KEY_COLUMN_DOES_NOT_EXITS
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.BlobUsedAsKey">
            <summary>
            ER_BLOB_USED_AS_KEY
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.TooBigFieldLength">
            <summary>
            ER_TOO_BIG_FIELDLENGTH
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.WrongAutoKey">
            <summary>
            ER_WRONG_AUTO_KEY
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.Ready">
            <summary>
            ER_READY
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.NormalShutdown">
            <summary>
            ER_NORMAL_SHUTDOWN
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.GotSignal">
            <summary>
            ER_GOT_SIGNAL
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.ShutdownComplete">
            <summary>
            ER_SHUTDOWN_COMPLETE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.ForcingClose">
            <summary>
            ER_FORCING_CLOSE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.IPSocketError">
            <summary>
            ER_IPSOCK_ERROR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.NoSuchIndex">
            <summary>
            ER_NO_SUCH_INDEX
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.WrongFieldTerminators">
            <summary>
            ER_WRONG_FIELD_TERMINATORS
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.BlobsAndNoTerminated">
            <summary>
            ER_BLOBS_AND_NO_TERMINATED
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.TextFileNotReadable">
            <summary>
            ER_TEXTFILE_NOT_READABLE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.FileExists">
            <summary>
            ER_FILE_EXISTS_ERROR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.LoadInfo">
            <summary>
            ER_LOAD_INFO
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.AlterInfo">
            <summary>
            ER_ALTER_INFO
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.WrongSubKey">
            <summary>
            ER_WRONG_SUB_KEY
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.CannotRemoveAllFields">
            <summary>
            ER_CANT_REMOVE_ALL_FIELDS
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.CannotDropFieldOrKey">
            <summary>
            ER_CANT_DROP_FIELD_OR_KEY
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.InsertInfo">
            <summary>
            ER_INSERT_INFO
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.UpdateTableUsed">
            <summary>
            ER_UPDATE_TABLE_USED
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.NoSuchThread">
            <summary>
            ER_NO_SUCH_THREAD
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.KillDenied">
            <summary>
            ER_KILL_DENIED_ERROR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.NoTablesUsed">
            <summary>
            ER_NO_TABLES_USED
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.TooBigSet">
            <summary>
            ER_TOO_BIG_SET
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.NoUniqueLogFile">
            <summary>
            ER_NO_UNIQUE_LOGFILE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.TableNotLockedForWrite">
            <summary>
            ER_TABLE_NOT_LOCKED_FOR_WRITE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.TableNotLocked">
            <summary>
            ER_TABLE_NOT_LOCKED
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.BlobCannotHaveDefault">
            <summary>
            ER_BLOB_CANT_HAVE_DEFAULT
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.WrongDatabaseName">
            <summary>
            ER_WRONG_DB_NAME
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.WrongTableName">
            <summary>
            ER_WRONG_TABLE_NAME
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.TooBigSelect">
            <summary>
            ER_TOO_BIG_SELECT
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.UnknownError">
            <summary>
            ER_UNKNOWN_ERROR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.UnknownProcedure">
            <summary>
            ER_UNKNOWN_PROCEDURE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.WrongParameterCountToProcedure">
            <summary>
            ER_WRONG_PARAMCOUNT_TO_PROCEDURE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.WrongParametersToProcedure">
            <summary>
            ER_WRONG_PARAMETERS_TO_PROCEDURE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.UnknownTable">
            <summary>
            ER_UNKNOWN_TABLE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.FieldSpecifiedTwice">
            <summary>
            ER_FIELD_SPECIFIED_TWICE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.InvalidGroupFunctionUse">
            <summary>
            ER_INVALID_GROUP_FUNC_USE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.UnsupportedExtenstion">
            <summary>
            ER_UNSUPPORTED_EXTENSION
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.TableMustHaveColumns">
            <summary>
            ER_TABLE_MUST_HAVE_COLUMNS
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.RecordFileFull">
            <summary>
            ER_RECORD_FILE_FULL
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.UnknownCharacterSet">
            <summary>
            ER_UNKNOWN_CHARACTER_SET
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.TooManyTables">
            <summary>
            ER_TOO_MANY_TABLES
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.TooManyFields">
            <summary>
            ER_TOO_MANY_FIELDS
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.TooBigRowSize">
            <summary>
            ER_TOO_BIG_ROWSIZE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.StackOverrun">
            <summary>
            ER_STACK_OVERRUN
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.WrongOuterJoin">
            <summary>
            ER_WRONG_OUTER_JOIN
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.NullColumnInIndex">
            <summary>
            ER_NULL_COLUMN_IN_INDEX
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.CannotFindUDF">
            <summary>
            ER_CANT_FIND_UDF
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.CannotInitializeUDF">
            <summary>
            ER_CANT_INITIALIZE_UDF
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.UDFNoPaths">
            <summary>
            ER_UDF_NO_PATHS
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.UDFExists">
            <summary>
            ER_UDF_EXISTS
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.CannotOpenLibrary">
            <summary>
            ER_CANT_OPEN_LIBRARY
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.CannotFindDLEntry">
            <summary>
            ER_CANT_FIND_DL_ENTRY
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.FunctionNotDefined">
            <summary>
            ER_FUNCTION_NOT_DEFINED
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.HostIsBlocked">
            <summary>
            ER_HOST_IS_BLOCKED
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.HostNotPrivileged">
            <summary>
            ER_HOST_NOT_PRIVILEGED
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.AnonymousUser">
            <summary>
            ER_PASSWORD_ANONYMOUS_USER
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.PasswordNotAllowed">
            <summary>
            ER_PASSWORD_NOT_ALLOWED
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.PasswordNoMatch">
            <summary>
            ER_PASSWORD_NO_MATCH
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.UpdateInfo">
            <summary>
            ER_UPDATE_INFO
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.CannotCreateThread">
            <summary>
            ER_CANT_CREATE_THREAD
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.WrongValueCountOnRow">
            <summary>
            ER_WRONG_VALUE_COUNT_ON_ROW
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.CannotReopenTable">
            <summary>
            ER_CANT_REOPEN_TABLE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.InvalidUseOfNull">
            <summary>
            ER_INVALID_USE_OF_NULL
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.RegExpError">
            <summary>
            ER_REGEXP_ERROR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.MixOfGroupFunctionAndFields">
            <summary>
            ER_MIX_OF_GROUP_FUNC_AND_FIELDS
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.NonExistingGrant">
            <summary>
            ER_NONEXISTING_GRANT
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.TableAccessDenied">
            <summary>
            ER_TABLEACCESS_DENIED_ERROR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.ColumnAccessDenied">
            <summary>
            ER_COLUMNACCESS_DENIED_ERROR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.IllegalGrantForTable">
            <summary>
            ER_ILLEGAL_GRANT_FOR_TABLE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.GrantWrongHostOrUser">
            <summary>
            ER_GRANT_WRONG_HOST_OR_USER
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.NoSuchTable">
            <summary>
            ER_NO_SUCH_TABLE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.NonExistingTableGrant">
            <summary>
            ER_NONEXISTING_TABLE_GRANT
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.NotAllowedCommand">
            <summary>
            ER_NOT_ALLOWED_COMMAND
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.SyntaxError">
            <summary>
            ER_SYNTAX_ERROR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.DelayedCannotChangeLock">
            <summary>
            ER_UNUSED1
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.TooManyDelayedThreads">
            <summary>
            ER_UNUSED2
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.AbortingConnection">
            <summary>
            ER_ABORTING_CONNECTION
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.PacketTooLarge">
            <summary>
            ER_NET_PACKET_TOO_LARGE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.NetReadErrorFromPipe">
            <summary>
            ER_NET_READ_ERROR_FROM_PIPE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.NetFCntlError">
            <summary>
            ER_NET_FCNTL_ERROR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.NetPacketsOutOfOrder">
            <summary>
            ER_NET_PACKETS_OUT_OF_ORDER
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.NetUncompressError">
            <summary>
            ER_NET_UNCOMPRESS_ERROR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.NetReadError">
            <summary>
            ER_NET_READ_ERROR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.NetReadInterrupted">
            <summary>
            ER_NET_READ_INTERRUPTED
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.NetErrorOnWrite">
            <summary>
            ER_NET_ERROR_ON_WRITE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.NetWriteInterrupted">
            <summary>
            ER_NET_WRITE_INTERRUPTED
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.TooLongString">
            <summary>
            ER_TOO_LONG_STRING
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.TableCannotHandleBlob">
            <summary>
            ER_TABLE_CANT_HANDLE_BLOB
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.TableCannotHandleAutoIncrement">
            <summary>
            ER_TABLE_CANT_HANDLE_AUTO_INCREMENT
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.DelayedInsertTableLocked">
            <summary>
            ER_UNUSED3
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.WrongColumnName">
            <summary>
            ER_WRONG_COLUMN_NAME
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.WrongKeyColumn">
            <summary>
            ER_WRONG_KEY_COLUMN
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.WrongMergeTable">
            <summary>
            ER_WRONG_MRG_TABLE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.DuplicateUnique">
            <summary>
            ER_DUP_UNIQUE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.BlobKeyWithoutLength">
            <summary>
            ER_BLOB_KEY_WITHOUT_LENGTH
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.PrimaryCannotHaveNull">
            <summary>
            ER_PRIMARY_CANT_HAVE_NULL
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.TooManyRows">
            <summary>
            ER_TOO_MANY_ROWS
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.RequiresPrimaryKey">
            <summary>
            ER_REQUIRES_PRIMARY_KEY
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.NoRAIDCompiled">
            <summary>
            ER_NO_RAID_COMPILED
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.UpdateWithoutKeysInSafeMode">
            <summary>
            ER_UPDATE_WITHOUT_KEY_IN_SAFE_MODE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.KeyDoesNotExist">
            <summary>
            ER_KEY_DOES_NOT_EXITS
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.CheckNoSuchTable">
            <summary>
            ER_CHECK_NO_SUCH_TABLE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.CheckNotImplemented">
            <summary>
            ER_CHECK_NOT_IMPLEMENTED
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.CannotDoThisDuringATransaction">
            <summary>
            ER_CANT_DO_THIS_DURING_AN_TRANSACTION
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.ErrorDuringCommit">
            <summary>
            ER_ERROR_DURING_COMMIT
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.ErrorDuringRollback">
            <summary>
            ER_ERROR_DURING_ROLLBACK
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.ErrorDuringFlushLogs">
            <summary>
            ER_ERROR_DURING_FLUSH_LOGS
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.ErrorDuringCheckpoint">
            <summary>
            ER_ERROR_DURING_CHECKPOINT
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.NewAbortingConnection">
            <summary>
            ER_NEW_ABORTING_CONNECTION
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.DumpNotImplemented">
            <summary>
            ER_DUMP_NOT_IMPLEMENTED
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.FlushMasterBinLogClosed">
            <summary>
            ER_FLUSH_MASTER_BINLOG_CLOSED
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.IndexRebuild">
            <summary>
            ER_INDEX_REBUILD
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.MasterError">
            <summary>
            ER_MASTER
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.MasterNetRead">
            <summary>
            ER_MASTER_NET_READ
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.MasterNetWrite">
            <summary>
            ER_MASTER_NET_WRITE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.FullTextMatchingKeyNotFound">
            <summary>
            ER_FT_MATCHING_KEY_NOT_FOUND
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.LockOrActiveTransaction">
            <summary>
            ER_LOCK_OR_ACTIVE_TRANSACTION
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.UnknownSystemVariable">
            <summary>
            ER_UNKNOWN_SYSTEM_VARIABLE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.CrashedOnUsage">
            <summary>
            ER_CRASHED_ON_USAGE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.CrashedOnRepair">
            <summary>
            ER_CRASHED_ON_REPAIR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.WarningNotCompleteRollback">
            <summary>
            ER_WARNING_NOT_COMPLETE_ROLLBACK
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.TransactionCacheFull">
            <summary>
            ER_TRANS_CACHE_FULL
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.SlaveMustStop">
            <summary>
            ER_SLAVE_MUST_STOP
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.SlaveNotRunning">
            <summary>
            ER_SLAVE_NOT_RUNNING
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.BadSlave">
            <summary>
            ER_BAD_SLAVE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.MasterInfo">
            <summary>
            ER_MASTER_INFO
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.SlaveThread">
            <summary>
            ER_SLAVE_THREAD
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.TooManyUserConnections">
            <summary>
            ER_TOO_MANY_USER_CONNECTIONS
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.SetConstantsOnly">
            <summary>
            ER_SET_CONSTANTS_ONLY
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.LockWaitTimeout">
            <summary>
            ER_LOCK_WAIT_TIMEOUT
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.LockTableFull">
            <summary>
            ER_LOCK_TABLE_FULL
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.ReadOnlyTransaction">
            <summary>
            ER_READ_ONLY_TRANSACTION
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.DropDatabaseWithReadLock">
            <summary>
            ER_DROP_DB_WITH_READ_LOCK
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.CreateDatabaseWithReadLock">
            <summary>
            ER_CREATE_DB_WITH_READ_LOCK
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.WrongArguments">
            <summary>
            ER_WRONG_ARGUMENTS
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.NoPermissionToCreateUser">
            <summary>
            ER_NO_PERMISSION_TO_CREATE_USER
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.UnionTablesInDifferentDirectory">
            <summary>
            ER_UNION_TABLES_IN_DIFFERENT_DIR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.LockDeadlock">
            <summary>
            ER_LOCK_DEADLOCK
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.TableCannotHandleFullText">
            <summary>
            ER_TABLE_CANT_HANDLE_FT
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.CannotAddForeignConstraint">
            <summary>
            ER_CANNOT_ADD_FOREIGN
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.NoReferencedRow">
            <summary>
            ER_NO_REFERENCED_ROW
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.RowIsReferenced">
            <summary>
            ER_ROW_IS_REFERENCED
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.ConnectToMaster">
            <summary>
            ER_CONNECT_TO_MASTER
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.QueryOnMaster">
            <summary>
            ER_QUERY_ON_MASTER
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.ErrorWhenExecutingCommand">
            <summary>
            ER_ERROR_WHEN_EXECUTING_COMMAND
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.WrongUsage">
            <summary>
            ER_WRONG_USAGE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.WrongNumberOfColumnsInSelect">
            <summary>
            ER_WRONG_NUMBER_OF_COLUMNS_IN_SELECT
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.CannotUpdateWithReadLock">
            <summary>
            ER_CANT_UPDATE_WITH_READLOCK
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.MixingNotAllowed">
            <summary>
            ER_MIXING_NOT_ALLOWED
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.DuplicateArgument">
            <summary>
            ER_DUP_ARGUMENT
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.UserLimitReached">
            <summary>
            ER_USER_LIMIT_REACHED
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.SpecifiedAccessDeniedError">
            <summary>
            ER_SPECIFIC_ACCESS_DENIED_ERROR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.LocalVariableError">
            <summary>
            ER_LOCAL_VARIABLE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.GlobalVariableError">
            <summary>
            ER_GLOBAL_VARIABLE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.NotDefaultError">
            <summary>
            ER_NO_DEFAULT
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.WrongValueForVariable">
            <summary>
            ER_WRONG_VALUE_FOR_VAR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.WrongTypeForVariable">
            <summary>
            ER_WRONG_TYPE_FOR_VAR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.VariableCannotBeRead">
            <summary>
            ER_VAR_CANT_BE_READ
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.CannotUseOptionHere">
            <summary>
            ER_CANT_USE_OPTION_HERE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.NotSupportedYet">
            <summary>
            ER_NOT_SUPPORTED_YET
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.MasterFatalErrorReadingBinLog">
            <summary>
            ER_MASTER_FATAL_ERROR_READING_BINLOG
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.SlaveIgnoredTable">
            <summary>
            ER_SLAVE_IGNORED_TABLE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.IncorrectGlobalLocalVariable">
            <summary>
            ER_INCORRECT_GLOBAL_LOCAL_VAR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.WrongForeignKeyDefinition">
            <summary>
            ER_WRONG_FK_DEF
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.KeyReferenceDoesNotMatchTableReference">
            <summary>
            ER_KEY_REF_DO_NOT_MATCH_TABLE_REF
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.OpearnColumnsError">
            <summary>
            ER_OPERAND_COLUMNS
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.SubQueryNoOneRow">
            <summary>
            ER_SUBQUERY_NO_1_ROW
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.UnknownStatementHandler">
            <summary>
            ER_UNKNOWN_STMT_HANDLER
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.CorruptHelpDatabase">
            <summary>
            ER_CORRUPT_HELP_DB
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.CyclicReference">
            <summary>
            ER_CYCLIC_REFERENCE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.AutoConvert">
            <summary>
            ER_AUTO_CONVERT
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.IllegalReference">
            <summary>
            ER_ILLEGAL_REFERENCE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.DerivedMustHaveAlias">
            <summary>
            ER_DERIVED_MUST_HAVE_ALIAS
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.SelectReduced">
            <summary>
            ER_SELECT_REDUCED
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.TableNameNotAllowedHere">
            <summary>
            ER_TABLENAME_NOT_ALLOWED_HERE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.NotSupportedAuthMode">
            <summary>
            ER_NOT_SUPPORTED_AUTH_MODE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.SpatialCannotHaveNull">
            <summary>
            ER_SPATIAL_CANT_HAVE_NULL
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.CollationCharsetMismatch">
            <summary>
            ER_COLLATION_CHARSET_MISMATCH
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.SlaveWasRunning">
            <summary>
            ER_SLAVE_WAS_RUNNING
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.SlaveWasNotRunning">
            <summary>
            ER_SLAVE_WAS_NOT_RUNNING
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.TooBigForUncompress">
            <summary>
            ER_TOO_BIG_FOR_UNCOMPRESS
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.ZipLibMemoryError">
            <summary>
            ER_ZLIB_Z_MEM_ERROR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.ZipLibBufferError">
            <summary>
            ER_ZLIB_Z_BUF_ERROR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.ZipLibDataError">
            <summary>
            ER_ZLIB_Z_DATA_ERROR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.CutValueGroupConcat">
            <summary>
            ER_CUT_VALUE_GROUP_CONCAT
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.WarningTooFewRecords">
            <summary>
            ER_WARN_TOO_FEW_RECORDS
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.WarningTooManyRecords">
            <summary>
            ER_WARN_TOO_MANY_RECORDS
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.WarningNullToNotNull">
            <summary>
            ER_WARN_NULL_TO_NOTNULL
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.WarningDataOutOfRange">
            <summary>
            ER_WARN_DATA_OUT_OF_RANGE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.WarningDataTruncated">
            <summary>
            WARN_DATA_TRUNCATED
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.WarningUsingOtherHandler">
            <summary>
            ER_WARN_USING_OTHER_HANDLER
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.CannotAggregateTwoCollations">
            <summary>
            ER_CANT_AGGREGATE_2COLLATIONS
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.DropUserError">
            <summary>
            ER_DROP_USER
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.RevokeGrantsError">
            <summary>
            ER_REVOKE_GRANTS
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.CannotAggregateThreeCollations">
            <summary>
            ER_CANT_AGGREGATE_3COLLATIONS
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.CannotAggregateNCollations">
            <summary>
            ER_CANT_AGGREGATE_NCOLLATIONS
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.VariableIsNotStructure">
            <summary>
            ER_VARIABLE_IS_NOT_STRUCT
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.UnknownCollation">
            <summary>
            ER_UNKNOWN_COLLATION
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.SlaveIgnoreSSLParameters">
            <summary>
            ER_SLAVE_IGNORED_SSL_PARAMS
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.ServerIsInSecureAuthMode">
            <summary>
            ER_SERVER_IS_IN_SECURE_AUTH_MODE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.WarningFieldResolved">
            <summary>
            ER_WARN_FIELD_RESOLVED
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.BadSlaveUntilCondition">
            <summary>
            ER_BAD_SLAVE_UNTIL_COND
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.MissingSkipSlave">
            <summary>
            ER_MISSING_SKIP_SLAVE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.ErrorUntilConditionIgnored">
            <summary>
            ER_UNTIL_COND_IGNORED
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.WrongNameForIndex">
            <summary>
            ER_WRONG_NAME_FOR_INDEX
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.WrongNameForCatalog">
            <summary>
            ER_WRONG_NAME_FOR_CATALOG
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.WarningQueryCacheResize">
            <summary>
            ER_WARN_QC_RESIZE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.BadFullTextColumn">
            <summary>
            ER_BAD_FT_COLUMN
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.UnknownKeyCache">
            <summary>
            ER_UNKNOWN_KEY_CACHE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.WarningHostnameWillNotWork">
            <summary>
            ER_WARN_HOSTNAME_WONT_WORK
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.UnknownStorageEngine">
            <summary>
            ER_UNKNOWN_STORAGE_ENGINE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.WarningDeprecatedSyntax">
            <summary>
            ER_WARN_DEPRECATED_SYNTAX
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.NonUpdateableTable">
            <summary>
            ER_NON_UPDATABLE_TABLE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.FeatureDisabled">
            <summary>
            ER_FEATURE_DISABLED
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.OptionPreventsStatement">
            <summary>
            ER_OPTION_PREVENTS_STATEMENT
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.DuplicatedValueInType">
            <summary>
            ER_DUPLICATED_VALUE_IN_TYPE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.TruncatedWrongValue">
            <summary>
            ER_TRUNCATED_WRONG_VALUE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.TooMuchAutoTimestampColumns">
            <summary>
            ER_TOO_MUCH_AUTO_TIMESTAMP_COLS
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.InvalidOnUpdate">
            <summary>
            ER_INVALID_ON_UPDATE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.UnsupportedPreparedStatement">
            <summary>
            ER_UNSUPPORTED_PS
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.GetErroMessage">
            <summary>
            ER_GET_ERRMSG
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.GetTemporaryErrorMessage">
            <summary>
            ER_GET_TEMPORARY_ERRMSG
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.UnknownTimeZone">
            <summary>
            ER_UNKNOWN_TIME_ZONE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.WarningInvalidTimestamp">
            <summary>
            ER_WARN_INVALID_TIMESTAMP
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.InvalidCharacterString">
            <summary>
            ER_INVALID_CHARACTER_STRING
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.WarningAllowedPacketOverflowed">
            <summary>
            ER_WARN_ALLOWED_PACKET_OVERFLOWED
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.ConflictingDeclarations">
            <summary>
            ER_CONFLICTING_DECLARATIONS
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.StoredProcedureNoRecursiveCreate">
            <summary>
            ER_SP_NO_RECURSIVE_CREATE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.StoredProcedureAlreadyExists">
            <summary>
            ER_SP_ALREADY_EXISTS
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.StoredProcedureDoesNotExist">
            <summary>
            ER_SP_DOES_NOT_EXIST
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.StoredProcedureDropFailed">
            <summary>
            ER_SP_DROP_FAILED
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.StoredProcedureStoreFailed">
            <summary>
            ER_SP_STORE_FAILED
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.StoredProcedureLiLabelMismatch">
            <summary>
            ER_SP_LILABEL_MISMATCH
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.StoredProcedureLabelRedefine">
            <summary>
            ER_SP_LABEL_REDEFINE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.StoredProcedureLabelMismatch">
            <summary>
            ER_SP_LABEL_MISMATCH
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.StoredProcedureUninitializedVariable">
            <summary>
            ER_SP_UNINIT_VAR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.StoredProcedureBadSelect">
            <summary>
            ER_SP_BADSELECT
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.StoredProcedureBadReturn">
            <summary>
            ER_SP_BADRETURN
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.StoredProcedureBadStatement">
            <summary>
            ER_SP_BADSTATEMENT
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.UpdateLogDeprecatedIgnored">
            <summary>
            ER_UPDATE_LOG_DEPRECATED_IGNORED
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.UpdateLogDeprecatedTranslated">
            <summary>
            ER_UPDATE_LOG_DEPRECATED_TRANSLATED
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.QueryInterrupted">
            <summary>
            Query execution was interrupted (ER_QUERY_INTERRUPTED).
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.StoredProcedureNumberOfArguments">
            <summary>
            ER_SP_WRONG_NO_OF_ARGS
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.StoredProcedureConditionMismatch">
            <summary>
            ER_SP_COND_MISMATCH
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.StoredProcedureNoReturn">
            <summary>
            ER_SP_NORETURN
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.StoredProcedureNoReturnEnd">
            <summary>
            ER_SP_NORETURNEND
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.StoredProcedureBadCursorQuery">
            <summary>
            ER_SP_BAD_CURSOR_QUERY
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.StoredProcedureBadCursorSelect">
            <summary>
            ER_SP_BAD_CURSOR_SELECT
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.StoredProcedureCursorMismatch">
            <summary>
            ER_SP_CURSOR_MISMATCH
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.StoredProcedureAlreadyOpen">
            <summary>
            ER_SP_CURSOR_ALREADY_OPEN
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.StoredProcedureCursorNotOpen">
            <summary>
            ER_SP_CURSOR_NOT_OPEN
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.StoredProcedureUndeclaredVariabel">
            <summary>
            ER_SP_UNDECLARED_VAR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.StoredProcedureWrongNumberOfFetchArguments">
            <summary>
            ER_SP_WRONG_NO_OF_FETCH_ARGS
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.StoredProcedureFetchNoData">
            <summary>
            ER_SP_FETCH_NO_DATA
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.StoredProcedureDuplicateParameter">
            <summary>
            ER_SP_DUP_PARAM
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.StoredProcedureDuplicateVariable">
            <summary>
            ER_SP_DUP_VAR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.StoredProcedureDuplicateCondition">
            <summary>
            ER_SP_DUP_COND
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.StoredProcedureDuplicateCursor">
            <summary>
            ER_SP_DUP_CURS
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.StoredProcedureCannotAlter">
            <summary>
            ER_SP_CANT_ALTER
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.StoredProcedureSubSelectNYI">
            <summary>
            ER_SP_SUBSELECT_NYI
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.StatementNotAllowedInStoredFunctionOrTrigger">
            <summary>
            ER_STMT_NOT_ALLOWED_IN_SF_OR_TRG
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.StoredProcedureVariableConditionAfterCursorHandler">
            <summary>
            ER_SP_VARCOND_AFTER_CURSHNDLR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.StoredProcedureCursorAfterHandler">
            <summary>
            ER_SP_CURSOR_AFTER_HANDLER
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.StoredProcedureCaseNotFound">
            <summary>
            ER_SP_CASE_NOT_FOUND
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.FileParserTooBigFile">
            <summary>
            ER_FPARSER_TOO_BIG_FILE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.FileParserBadHeader">
            <summary>
            ER_FPARSER_BAD_HEADER
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.FileParserEOFInComment">
            <summary>
            ER_FPARSER_EOF_IN_COMMENT
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.FileParserErrorInParameter">
            <summary>
            ER_FPARSER_ERROR_IN_PARAMETER
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.FileParserEOFInUnknownParameter">
            <summary>
            ER_FPARSER_EOF_IN_UNKNOWN_PARAMETER
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.ViewNoExplain">
            <summary>
            ER_VIEW_NO_EXPLAIN
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.FrmUnknownType">
            <summary>
            ER_FRM_UNKNOWN_TYPE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.WrongObject">
            <summary>
            ER_WRONG_OBJECT
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.NonUpdateableColumn">
            <summary>
            ER_NONUPDATEABLE_COLUMN
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.ViewSelectDerived">
            <summary>
            ER_VIEW_SELECT_DERIVED
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.ViewSelectClause">
            <summary>
            ER_VIEW_SELECT_CLAUSE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.ViewSelectVariable">
            <summary>
            ER_VIEW_SELECT_VARIABLE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.ViewSelectTempTable">
            <summary>
            ER_VIEW_SELECT_TMPTABLE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.ViewWrongList">
            <summary>
            ER_VIEW_WRONG_LIST
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.WarningViewMerge">
            <summary>
            ER_WARN_VIEW_MERGE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.WarningViewWithoutKey">
            <summary>
            ER_WARN_VIEW_WITHOUT_KEY
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.ViewInvalid">
            <summary>
            ER_VIEW_INVALID
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.StoredProcedureNoDropStoredProcedure">
            <summary>
            ER_SP_NO_DROP_SP
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.StoredProcedureGotoInHandler">
            <summary>
            ER_SP_GOTO_IN_HNDLR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.TriggerAlreadyExists">
            <summary>
            ER_TRG_ALREADY_EXISTS
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.TriggerDoesNotExist">
            <summary>
            ER_TRG_DOES_NOT_EXIST
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.TriggerOnViewOrTempTable">
            <summary>
            ER_TRG_ON_VIEW_OR_TEMP_TABLE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.TriggerCannotChangeRow">
            <summary>
            ER_TRG_CANT_CHANGE_ROW
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.TriggerNoSuchRowInTrigger">
            <summary>
            ER_TRG_NO_SUCH_ROW_IN_TRG
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.NoDefaultForField">
            <summary>
            ER_NO_DEFAULT_FOR_FIELD
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.DivisionByZero">
            <summary>
            ER_DIVISION_BY_ZERO
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.TruncatedWrongValueForField">
            <summary>
            ER_TRUNCATED_WRONG_VALUE_FOR_FIELD
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.IllegalValueForType">
            <summary>
            ER_ILLEGAL_VALUE_FOR_TYPE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.ViewNonUpdatableCheck">
            <summary>
            ER_VIEW_NONUPD_CHECK
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.ViewCheckFailed">
            <summary>
            ER_VIEW_CHECK_FAILED
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.PrecedureAccessDenied">
            <summary>
            ER_PROCACCESS_DENIED_ERROR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.RelayLogFail">
            <summary>
            ER_RELAY_LOG_FAIL
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.PasswordLength">
            <summary>
            ER_PASSWD_LENGTH
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.UnknownTargetBinLog">
            <summary>
            ER_UNKNOWN_TARGET_BINLOG
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.IOErrorLogIndexRead">
            <summary>
            ER_IO_ERR_LOG_INDEX_READ
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.BinLogPurgeProhibited">
            <summary>
            ER_BINLOG_PURGE_PROHIBITED
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.FSeekFail">
            <summary>
            ER_FSEEK_FAIL
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.BinLogPurgeFatalError">
            <summary>
            ER_BINLOG_PURGE_FATAL_ERR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.LogInUse">
            <summary>
            ER_LOG_IN_USE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.LogPurgeUnknownError">
            <summary>
            ER_LOG_PURGE_UNKNOWN_ERR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.RelayLogInit">
            <summary>
            ER_RELAY_LOG_INIT
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.NoBinaryLogging">
            <summary>
            ER_NO_BINARY_LOGGING
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.ReservedSyntax">
            <summary>
            ER_RESERVED_SYNTAX
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.WSAStartupFailed">
            <summary>
            ER_WSAS_FAILED
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.DifferentGroupsProcedure">
            <summary>
            ER_DIFF_GROUPS_PROC
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.NoGroupForProcedure">
            <summary>
            ER_NO_GROUP_FOR_PROC
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.OrderWithProcedure">
            <summary>
            ER_ORDER_WITH_PROC
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.LoggingProhibitsChangingOf">
            <summary>
            ER_LOGGING_PROHIBIT_CHANGING_OF
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.NoFileMapping">
            <summary>
            ER_NO_FILE_MAPPING
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.WrongMagic">
            <summary>
            ER_WRONG_MAGIC
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.PreparedStatementManyParameters">
            <summary>
            ER_PS_MANY_PARAM
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.KeyPartZero">
            <summary>
            ER_KEY_PART_0
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.ViewChecksum">
            <summary>
            ER_VIEW_CHECKSUM
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.ViewMultiUpdate">
            <summary>
            ER_VIEW_MULTIUPDATE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.ViewNoInsertFieldList">
            <summary>
            ER_VIEW_NO_INSERT_FIELD_LIST
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.ViewDeleteMergeView">
            <summary>
            ER_VIEW_DELETE_MERGE_VIEW
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.CannotUser">
            <summary>
            ER_CANNOT_USER
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.XAERNotA">
            <summary>
            ER_XAER_NOTA
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.XAERInvalid">
            <summary>
            ER_XAER_INVAL
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.XAERRemoveFail">
            <summary>
            ER_XAER_RMFAIL
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.XAEROutside">
            <summary>
            ER_XAER_OUTSIDE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.XAERRemoveError">
            <summary>
            ER_XAER_RMERR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.XARBRollback">
            <summary>
            ER_XA_RBROLLBACK
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.NonExistingProcedureGrant">
            <summary>
            ER_NONEXISTING_PROC_GRANT
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.ProcedureAutoGrantFail">
            <summary>
            ER_PROC_AUTO_GRANT_FAIL
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.ProcedureAutoRevokeFail">
            <summary>
            ER_PROC_AUTO_REVOKE_FAIL
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.DataTooLong">
            <summary>
            ER_DATA_TOO_LONG
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.StoredProcedureSQLState">
            <summary>
            ER_SP_BAD_SQLSTATE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.StartupError">
            <summary>
            ER_STARTUP
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.LoadFromFixedSizeRowsToVariable">
            <summary>
            ER_LOAD_FROM_FIXED_SIZE_ROWS_TO_VAR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.CannotCreateUserWithGrant">
            <summary>
            ER_CANT_CREATE_USER_WITH_GRANT
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.WrongValueForType">
            <summary>
            ER_WRONG_VALUE_FOR_TYPE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.TableDefinitionChanged">
            <summary>
            ER_TABLE_DEF_CHANGED
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.StoredProcedureDuplicateHandler">
            <summary>
            ER_SP_DUP_HANDLER
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.StoredProcedureNotVariableArgument">
            <summary>
            ER_SP_NOT_VAR_ARG
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.StoredProcedureNoReturnSet">
            <summary>
            ER_SP_NO_RETSET
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.CannotCreateGeometryObject">
            <summary>
            ER_CANT_CREATE_GEOMETRY_OBJECT
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.FailedRoutineBreaksBinLog">
            <summary>
            ER_FAILED_ROUTINE_BREAK_BINLOG
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.BinLogUnsafeRoutine">
            <summary>
            ER_BINLOG_UNSAFE_ROUTINE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.BinLogCreateRoutineNeedSuper">
            <summary>
            ER_BINLOG_CREATE_ROUTINE_NEED_SUPER
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.ExecuteStatementWithOpenCursor">
            <summary>
            ER_EXEC_STMT_WITH_OPEN_CURSOR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.StatementHasNoOpenCursor">
            <summary>
            ER_STMT_HAS_NO_OPEN_CURSOR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.CommitNotAllowedIfStoredFunctionOrTrigger">
            <summary>
            ER_COMMIT_NOT_ALLOWED_IN_SF_OR_TRG
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.NoDefaultForViewField">
            <summary>
            ER_NO_DEFAULT_FOR_VIEW_FIELD
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.StoredProcedureNoRecursion">
            <summary>
            ER_SP_NO_RECURSION
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.TooBigScale">
            <summary>
            ER_TOO_BIG_SCALE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.TooBigPrecision">
            <summary>
            ER_TOO_BIG_PRECISION
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.MBiggerThanD">
            <summary>
            ER_M_BIGGER_THAN_D
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.WrongLockOfSystemTable">
            <summary>
            ER_WRONG_LOCK_OF_SYSTEM_TABLE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.ConnectToForeignDataSource">
            <summary>
            ER_CONNECT_TO_FOREIGN_DATA_SOURCE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.QueryOnForeignDataSource">
            <summary>
            ER_QUERY_ON_FOREIGN_DATA_SOURCE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.ForeignDataSourceDoesNotExist">
            <summary>
            ER_FOREIGN_DATA_SOURCE_DOESNT_EXIST
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.ForeignDataStringInvalidCannotCreate">
            <summary>
            ER_FOREIGN_DATA_STRING_INVALID_CANT_CREATE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.ForeignDataStringInvalid">
            <summary>
            ER_FOREIGN_DATA_STRING_INVALID
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.CannotCreateFederatedTable">
            <summary>
            ER_CANT_CREATE_FEDERATED_TABLE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.TriggerInWrongSchema">
            <summary>
            ER_TRG_IN_WRONG_SCHEMA
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.StackOverrunNeedMore">
            <summary>
            ER_STACK_OVERRUN_NEED_MORE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.TooLongBody">
            <summary>
            ER_TOO_LONG_BODY
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.WarningCannotDropDefaultKeyCache">
            <summary>
            ER_WARN_CANT_DROP_DEFAULT_KEYCACHE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.TooBigDisplayWidth">
            <summary>
            ER_TOO_BIG_DISPLAYWIDTH
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.XAERDuplicateID">
            <summary>
            ER_XAER_DUPID
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.DateTimeFunctionOverflow">
            <summary>
            ER_DATETIME_FUNCTION_OVERFLOW
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.CannotUpdateUsedTableInStoredFunctionOrTrigger">
            <summary>
            ER_CANT_UPDATE_USED_TABLE_IN_SF_OR_TRG
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.ViewPreventUpdate">
            <summary>
            ER_VIEW_PREVENT_UPDATE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.PreparedStatementNoRecursion">
            <summary>
            ER_PS_NO_RECURSION
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.StoredProcedureCannotSetAutoCommit">
            <summary>
            ER_SP_CANT_SET_AUTOCOMMIT
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.MalformedDefiner">
            <summary>
            ER_MALFORMED_DEFINER
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.ViewFrmNoUser">
            <summary>
            ER_VIEW_FRM_NO_USER
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.ViewOtherUser">
            <summary>
            ER_VIEW_OTHER_USER
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.NoSuchUser">
            <summary>
            ER_NO_SUCH_USER
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.ForbidSchemaChange">
            <summary>
            ER_FORBID_SCHEMA_CHANGE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.RowIsReferenced2">
            <summary>
            ER_ROW_IS_REFERENCED_2
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.NoReferencedRow2">
            <summary>
            ER_NO_REFERENCED_ROW_2
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.StoredProcedureBadVariableShadow">
            <summary>
            ER_SP_BAD_VAR_SHADOW
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.TriggerNoDefiner">
            <summary>
            ER_TRG_NO_DEFINER
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.OldFileFormat">
            <summary>
            ER_OLD_FILE_FORMAT
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.StoredProcedureRecursionLimit">
            <summary>
            ER_SP_RECURSION_LIMIT
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.StoredProcedureTableCorrupt">
            <summary>
            ER_SP_PROC_TABLE_CORRUPT
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.StoredProcedureWrongName">
            <summary>
            ER_SP_WRONG_NAME
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.TableNeedsUpgrade">
            <summary>
            ER_TABLE_NEEDS_UPGRADE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.StoredProcedureNoAggregate">
            <summary>
            ER_SP_NO_AGGREGATE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.MaxPreparedStatementCountReached">
            <summary>
            ER_MAX_PREPARED_STMT_COUNT_REACHED
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.ViewRecursive">
            <summary>
            ER_VIEW_RECURSIVE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.NonGroupingFieldUsed">
            <summary>
            ER_NON_GROUPING_FIELD_USED
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.TableCannotHandleSpatialKeys">
            <summary>
            ER_TABLE_CANT_HANDLE_SPKEYS
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.NoTriggersOnSystemSchema">
            <summary>
            ER_NO_TRIGGERS_ON_SYSTEM_SCHEMA
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.RemovedSpaces">
            <summary>
            ER_REMOVED_SPACES
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.AutoIncrementReadFailed">
            <summary>
            ER_AUTOINC_READ_FAILED
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.UserNameError">
            <summary>
            ER_USERNAME
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.HostNameError">
            <summary>
            ER_HOSTNAME
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.WrongStringLength">
            <summary>
            ER_WRONG_STRING_LENGTH
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.NonInsertableTable">
            <summary>
            ER_NON_INSERTABLE_TABLE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.AdminWrongMergeTable">
            <summary>
            ER_ADMIN_WRONG_MRG_TABLE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.TooHighLevelOfNestingForSelect">
            <summary>
            ER_TOO_HIGH_LEVEL_OF_NESTING_FOR_SELECT
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.NameBecomesEmpty">
            <summary>
            ER_NAME_BECOMES_EMPTY
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.AmbiguousFieldTerm">
            <summary>
            ER_AMBIGUOUS_FIELD_TERM
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.ForeignServerExists">
            <summary>
            ER_FOREIGN_SERVER_EXISTS
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.ForeignServerDoesNotExist">
            <summary>
            ER_FOREIGN_SERVER_DOESNT_EXIST
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.IllegalHACreateOption">
            <summary>
            ER_ILLEGAL_HA_CREATE_OPTION
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.PartitionRequiresValues">
            <summary>
            ER_PARTITION_REQUIRES_VALUES_ERROR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.PartitionWrongValues">
            <summary>
            ER_PARTITION_WRONG_VALUES_ERROR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.PartitionMaxValue">
            <summary>
            ER_PARTITION_MAXVALUE_ERROR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.PartitionSubPartition">
            <summary>
            ER_PARTITION_SUBPARTITION_ERROR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.PartitionSubPartMix">
            <summary>
            ER_PARTITION_SUBPART_MIX_ERROR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.PartitionWrongNoPart">
            <summary>
            ER_PARTITION_WRONG_NO_PART_ERROR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.PartitionWrongNoSubPart">
            <summary>
            ER_PARTITION_WRONG_NO_SUBPART_ERROR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.WrongExpressionInParitionFunction">
            <summary>
            ER_WRONG_EXPR_IN_PARTITION_FUNC_ERROR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.NoConstantExpressionInRangeOrListError">
            <summary>
            ER_NO_CONST_EXPR_IN_RANGE_OR_LIST_ERROR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.FieldNotFoundPartitionErrror">
            <summary>
            ER_FIELD_NOT_FOUND_PART_ERROR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.ListOfFieldsOnlyInHash">
            <summary>
            ER_LIST_OF_FIELDS_ONLY_IN_HASH_ERROR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.InconsistentPartitionInfo">
            <summary>
            ER_INCONSISTENT_PARTITION_INFO_ERROR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.PartitionFunctionNotAllowed">
            <summary>
            ER_PARTITION_FUNC_NOT_ALLOWED_ERROR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.PartitionsMustBeDefined">
            <summary>
            ER_PARTITIONS_MUST_BE_DEFINED_ERROR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.RangeNotIncreasing">
            <summary>
            ER_RANGE_NOT_INCREASING_ERROR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.InconsistentTypeOfFunctions">
            <summary>
            ER_INCONSISTENT_TYPE_OF_FUNCTIONS_ERROR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.MultipleDefinitionsConstantInListPartition">
            <summary>
            ER_MULTIPLE_DEF_CONST_IN_LIST_PART_ERROR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.PartitionEntryError">
            <summary>
            ER_PARTITION_ENTRY_ERROR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.MixHandlerError">
            <summary>
            ER_MIX_HANDLER_ERROR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.PartitionNotDefined">
            <summary>
            ER_PARTITION_NOT_DEFINED_ERROR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.TooManyPartitions">
            <summary>
            ER_TOO_MANY_PARTITIONS_ERROR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.SubPartitionError">
            <summary>
            ER_SUBPARTITION_ERROR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.CannotCreateHandlerFile">
            <summary>
            ER_CANT_CREATE_HANDLER_FILE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.BlobFieldInPartitionFunction">
            <summary>
            ER_BLOB_FIELD_IN_PART_FUNC_ERROR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.UniqueKeyNeedAllFieldsInPartitioningFunction">
            <summary>
            ER_UNIQUE_KEY_NEED_ALL_FIELDS_IN_PF
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.NoPartitions">
            <summary>
            ER_NO_PARTS_ERROR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.PartitionManagementOnNoPartitioned">
            <summary>
            ER_PARTITION_MGMT_ON_NONPARTITIONED
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.ForeignKeyOnPartitioned">
            <summary>
            ER_FOREIGN_KEY_ON_PARTITIONED
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.DropPartitionNonExistent">
            <summary>
            ER_DROP_PARTITION_NON_EXISTENT
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.DropLastPartition">
            <summary>
            ER_DROP_LAST_PARTITION
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.CoalesceOnlyOnHashPartition">
            <summary>
            ER_COALESCE_ONLY_ON_HASH_PARTITION
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.ReorganizeHashOnlyOnSameNumber">
            <summary>
            ER_REORG_HASH_ONLY_ON_SAME_NO
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.ReorganizeNoParameter">
            <summary>
            ER_REORG_NO_PARAM_ERROR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.OnlyOnRangeListPartition">
            <summary>
            ER_ONLY_ON_RANGE_LIST_PARTITION
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.AddPartitionSubPartition">
            <summary>
            ER_ADD_PARTITION_SUBPART_ERROR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.AddPartitionNoNewPartition">
            <summary>
            ER_ADD_PARTITION_NO_NEW_PARTITION
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.CoalescePartitionNoPartition">
            <summary>
            ER_COALESCE_PARTITION_NO_PARTITION
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.ReorganizePartitionNotExist">
            <summary>
            ER_REORG_PARTITION_NOT_EXIST
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.SameNamePartition">
            <summary>
            ER_SAME_NAME_PARTITION
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.NoBinLog">
            <summary>
            ER_NO_BINLOG_ERROR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.ConsecutiveReorganizePartitions">
            <summary>
            ER_CONSECUTIVE_REORG_PARTITIONS
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.ReorganizeOutsideRange">
            <summary>
            ER_REORG_OUTSIDE_RANGE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.PartitionFunctionFailure">
            <summary>
            ER_PARTITION_FUNCTION_FAILURE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.PartitionStateError">
            <summary>
            ER_PART_STATE_ERROR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.LimitedPartitionRange">
            <summary>
            ER_LIMITED_PART_RANGE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.PluginIsNotLoaded">
            <summary>
            ER_PLUGIN_IS_NOT_LOADED
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.WrongValue">
            <summary>
            ER_WRONG_VALUE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.NoPartitionForGivenValue">
            <summary>
            ER_NO_PARTITION_FOR_GIVEN_VALUE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.FileGroupOptionOnlyOnce">
            <summary>
            ER_FILEGROUP_OPTION_ONLY_ONCE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.CreateFileGroupFailed">
            <summary>
            ER_CREATE_FILEGROUP_FAILED
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.DropFileGroupFailed">
            <summary>
            ER_DROP_FILEGROUP_FAILED
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.TableSpaceAutoExtend">
            <summary>
            ER_TABLESPACE_AUTO_EXTEND_ERROR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.WrongSizeNumber">
            <summary>
            ER_WRONG_SIZE_NUMBER
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.SizeOverflow">
            <summary>
            ER_SIZE_OVERFLOW_ERROR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.AlterFileGroupFailed">
            <summary>
            ER_ALTER_FILEGROUP_FAILED
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.BinLogRowLogginFailed">
            <summary>
            ER_BINLOG_ROW_LOGGING_FAILED
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.BinLogRowWrongTableDefinition">
            <summary>
            ER_BINLOG_ROW_WRONG_TABLE_DEF
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.BinLogRowRBRToSBR">
            <summary>
            ER_BINLOG_ROW_RBR_TO_SBR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.EventAlreadyExists">
            <summary>
            ER_EVENT_ALREADY_EXISTS
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.EventStoreFailed">
            <summary>
            ER_EVENT_STORE_FAILED
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.EventDoesNotExist">
            <summary>
            ER_EVENT_DOES_NOT_EXIST
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.EventCannotAlter">
            <summary>
            ER_EVENT_CANT_ALTER
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.EventDropFailed">
            <summary>
            ER_EVENT_DROP_FAILED
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.EventIntervalNotPositiveOrTooBig">
            <summary>
            ER_EVENT_INTERVAL_NOT_POSITIVE_OR_TOO_BIG
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.EventEndsBeforeStarts">
            <summary>
            ER_EVENT_ENDS_BEFORE_STARTS
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.EventExecTimeInThePast">
            <summary>
            ER_EVENT_EXEC_TIME_IN_THE_PAST
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.EventOpenTableFailed">
            <summary>
            ER_EVENT_OPEN_TABLE_FAILED
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.EventNeitherMExpresssionNorMAt">
            <summary>
            ER_EVENT_NEITHER_M_EXPR_NOR_M_AT
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.ColumnCountDoesNotMatchCorrupted">
            <summary>
            ER_OBSOLETE_COL_COUNT_DOESNT_MATCH_CORRUPTED
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.CannotLoadFromTable">
            <summary>
            ER_OBSOLETE_CANNOT_LOAD_FROM_TABLE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.EventCannotDelete">
            <summary>
            ER_EVENT_CANNOT_DELETE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.EventCompileError">
            <summary>
            ER_EVENT_COMPILE_ERROR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.EventSameName">
            <summary>
            ER_EVENT_SAME_NAME
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.EventDataTooLong">
            <summary>
            ER_EVENT_DATA_TOO_LONG
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.DropIndexForeignKey">
            <summary>
            ER_DROP_INDEX_FK
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.WarningDeprecatedSyntaxWithVersion">
            <summary>
            ER_WARN_DEPRECATED_SYNTAX_WITH_VER
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.CannotWriteLockLogTable">
            <summary>
            ER_CANT_WRITE_LOCK_LOG_TABLE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.CannotLockLogTable">
            <summary>
            ER_CANT_LOCK_LOG_TABLE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.ForeignDuplicateKey">
            <summary>
            ER_FOREIGN_DUPLICATE_KEY_OLD_UNUSED
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.ColumnCountDoesNotMatchPleaseUpdate">
            <summary>
            ER_COL_COUNT_DOESNT_MATCH_PLEASE_UPDATE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.TemoraryTablePreventSwitchOutOfRBR">
            <summary>
            ER_TEMP_TABLE_PREVENTS_SWITCH_OUT_OF_RBR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.StoredFunctionPreventsSwitchBinLogFormat">
            <summary>
            ER_STORED_FUNCTION_PREVENTS_SWITCH_BINLOG_FORMAT
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.NDBCannotSwitchBinLogFormat">
            <summary>
            ER_NDB_CANT_SWITCH_BINLOG_FORMAT
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.PartitionNoTemporary">
            <summary>
            ER_PARTITION_NO_TEMPORARY
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.PartitionConstantDomain">
            <summary>
            ER_PARTITION_CONST_DOMAIN_ERROR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.PartitionFunctionIsNotAllowed">
            <summary>
            ER_PARTITION_FUNCTION_IS_NOT_ALLOWED
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.DDLLogError">
            <summary>
            ER_DDL_LOG_ERROR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.NullInValuesLessThan">
            <summary>
            ER_NULL_IN_VALUES_LESS_THAN
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.WrongPartitionName">
            <summary>
            ER_WRONG_PARTITION_NAME
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.CannotChangeTransactionIsolation">
            <summary>
            ER_CANT_CHANGE_TX_CHARACTERISTICS
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.DuplicateEntryAutoIncrementCase">
            <summary>
            ER_DUP_ENTRY_AUTOINCREMENT_CASE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.EventModifyQueueError">
            <summary>
            ER_EVENT_MODIFY_QUEUE_ERROR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.EventSetVariableError">
            <summary>
            ER_EVENT_SET_VAR_ERROR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.PartitionMergeError">
            <summary>
            ER_PARTITION_MERGE_ERROR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.CannotActivateLog">
            <summary>
            ER_CANT_ACTIVATE_LOG
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.RBRNotAvailable">
            <summary>
            ER_RBR_NOT_AVAILABLE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.Base64DecodeError">
            <summary>
            ER_BASE64_DECODE_ERROR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.EventRecursionForbidden">
            <summary>
            ER_EVENT_RECURSION_FORBIDDEN
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.EventsDatabaseError">
            <summary>
            ER_EVENTS_DB_ERROR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.OnlyIntegersAllowed">
            <summary>
            ER_ONLY_INTEGERS_ALLOWED
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.UnsupportedLogEngine">
            <summary>
            ER_UNSUPORTED_LOG_ENGINE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.BadLogStatement">
            <summary>
            ER_BAD_LOG_STATEMENT
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.CannotRenameLogTable">
            <summary>
            ER_CANT_RENAME_LOG_TABLE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.WrongParameterCountToNativeFCT">
            <summary>
            ER_WRONG_PARAMCOUNT_TO_NATIVE_FCT
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.WrongParametersToNativeFCT">
            <summary>
            ER_WRONG_PARAMETERS_TO_NATIVE_FCT
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.WrongParametersToStoredFCT">
            <summary>
            ER_WRONG_PARAMETERS_TO_STORED_FCT
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.NativeFCTNameCollision">
            <summary>
            ER_NATIVE_FCT_NAME_COLLISION
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.DuplicateEntryWithKeyName">
            <summary>
            ER_DUP_ENTRY_WITH_KEY_NAME
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.BinLogPurgeEMFile">
            <summary>
            ER_BINLOG_PURGE_EMFILE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.EventCannotCreateInThePast">
            <summary>
            ER_EVENT_CANNOT_CREATE_IN_THE_PAST
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.EventCannotAlterInThePast">
            <summary>
            ER_EVENT_CANNOT_ALTER_IN_THE_PAST
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.SlaveIncident">
            <summary>
            ER_SLAVE_INCIDENT
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.NoPartitionForGivenValueSilent">
            <summary>
            ER_NO_PARTITION_FOR_GIVEN_VALUE_SILENT
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.BinLogUnsafeStatement">
            <summary>
            ER_BINLOG_UNSAFE_STATEMENT
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.SlaveFatalError">
            <summary>
            ER_SLAVE_FATAL_ERROR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.SlaveRelayLogReadFailure">
            <summary>
            ER_SLAVE_RELAY_LOG_READ_FAILURE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.SlaveRelayLogWriteFailure">
            <summary>
            ER_SLAVE_RELAY_LOG_WRITE_FAILURE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.SlaveCreateEventFailure">
            <summary>
            ER_SLAVE_CREATE_EVENT_FAILURE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.SlaveMasterComFailure">
            <summary>
            ER_SLAVE_MASTER_COM_FAILURE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.BinLogLoggingImpossible">
            <summary>
            ER_BINLOG_LOGGING_IMPOSSIBLE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.ViewNoCreationContext">
            <summary>
            ER_VIEW_NO_CREATION_CTX
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.ViewInvalidCreationContext">
            <summary>
            ER_VIEW_INVALID_CREATION_CTX
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.StoredRoutineInvalidCreateionContext">
            <summary>
            ER_SR_INVALID_CREATION_CTX
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.TiggerCorruptedFile">
            <summary>
            ER_TRG_CORRUPTED_FILE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.TriggerNoCreationContext">
            <summary>
            ER_TRG_NO_CREATION_CTX
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.TriggerInvalidCreationContext">
            <summary>
            ER_TRG_INVALID_CREATION_CTX
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.EventInvalidCreationContext">
            <summary>
            ER_EVENT_INVALID_CREATION_CTX
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.TriggerCannotOpenTable">
            <summary>
            ER_TRG_CANT_OPEN_TABLE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.CannotCreateSubRoutine">
            <summary>
            ER_CANT_CREATE_SROUTINE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.SlaveAmbiguousExecMode">
            <summary>
            ER_NEVER_USED
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.NoFormatDescriptionEventBeforeBinLogStatement">
            <summary>
            ER_NO_FORMAT_DESCRIPTION_EVENT_BEFORE_BINLOG_STATEMENT
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.SlaveCorruptEvent">
            <summary>
            ER_SLAVE_CORRUPT_EVENT
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.LoadDataInvalidColumn">
            <summary>
            ER_LOAD_DATA_INVALID_COLUMN
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.LogPurgeNoFile">
            <summary>
            ER_LOG_PURGE_NO_FILE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.XARBTimeout">
            <summary>
            ER_XA_RBTIMEOUT
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.XARBDeadlock">
            <summary>
            ER_XA_RBDEADLOCK
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.NeedRePrepare">
            <summary>
            ER_NEED_REPREPARE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.DelayedNotSupported">
            <summary>
            ER_DELAYED_NOT_SUPPORTED
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.WarningNoMasterInfo">
            <summary>
            WARN_NO_MASTER_INFO
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.WarningOptionIgnored">
            <summary>
            WARN_OPTION_IGNORED
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.WarningPluginDeleteBuiltIn">
            <summary>
            WARN_PLUGIN_DELETE_BUILTIN
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.WarningPluginBusy">
            <summary>
            WARN_PLUGIN_BUSY
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.VariableIsReadonly">
            <summary>
            ER_VARIABLE_IS_READONLY
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.WarningEngineTransactionRollback">
            <summary>
            ER_WARN_ENGINE_TRANSACTION_ROLLBACK
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.SlaveHeartbeatFailure">
            <summary>
            ER_SLAVE_HEARTBEAT_FAILURE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.SlaveHeartbeatValueOutOfRange">
            <summary>
            ER_SLAVE_HEARTBEAT_VALUE_OUT_OF_RANGE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.NDBReplicationSchemaError">
            <summary>
            ER_NDB_REPLICATION_SCHEMA_ERROR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.ConflictFunctionParseError">
            <summary>
            ER_CONFLICT_FN_PARSE_ERROR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.ExcepionsWriteError">
            <summary>
            ER_EXCEPTIONS_WRITE_ERROR
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.TooLongTableComment">
            <summary>
            ER_TOO_LONG_TABLE_COMMENT
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.TooLongFieldComment">
            <summary>
            ER_TOO_LONG_FIELD_COMMENT
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.FunctionInExistentNameCollision">
            <summary>
            ER_FUNC_INEXISTENT_NAME_COLLISION
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.DatabaseNameError">
            <summary>
            ER_DATABASE_NAME
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.TableNameErrror">
            <summary>
            ER_TABLE_NAME
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.PartitionNameError">
            <summary>
            ER_PARTITION_NAME
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.SubPartitionNameError">
            <summary>
            ER_SUBPARTITION_NAME
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.TemporaryNameError">
            <summary>
            ER_TEMPORARY_NAME
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.RenamedNameError">
            <summary>
            ER_RENAMED_NAME
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.TooManyConcurrentTransactions">
            <summary>
            ER_TOO_MANY_CONCURRENT_TRXS
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.WarningNonASCIISeparatorNotImplemented">
            <summary>
            WARN_NON_ASCII_SEPARATOR_NOT_IMPLEMENTED
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.DebugSyncTimeout">
            <summary>
            ER_DEBUG_SYNC_TIMEOUT
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.DebugSyncHitLimit">
            <summary>
            ER_DEBUG_SYNC_HIT_LIMIT
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.WarnDeprecatedSyntaxNoReplacement">
            <summary>
            ER_WARN_DEPRECATED_SYNTAX_NO_REPLACEMENT
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.TooLongIndexComment">
            <summary>
            ER_TOO_LONG_INDEX_COMMENT
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.LockAborted">
            <summary>
            ER_LOCK_ABORTED
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.DataOutOfRange">
            <summary>
            ER_DATA_OUT_OF_RANGE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.CannotExecuteInReadOnlyTransaction">
            <summary>
            ER_CANT_EXECUTE_IN_READ_ONLY_TRANSACTION
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.InnodbReadOnly">
            <summary>
            ER_INNODB_READ_ONLY
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.TableCorrupt">
            <summary>
            ER_TABLE_CORRUPT
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.TempFileWriteFailure">
            <summary>
            ER_TEMP_FILE_WRITE_FAILURE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.ExplainNotSupported">
            <summary>
            ER_EXPLAIN_NOT_SUPPORTED
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.InvalidFieldSize">
            <summary>
            ER_INVALID_FIELD_SIZE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.QueryTimeout">
            <summary>
            ER_QUERY_TIMEOUT
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.UserLockWrongName">
            <summary>
            ER_USER_LOCK_WRONG_NAME
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.UserLockDeadlock">
            <summary>
            ER_USER_LOCK_DEADLOCK
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.InvalidJsonData">
            <summary>
            ER_INVALID_JSON_DATA
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.InvalidJsonText">
            <summary>
            ER_INVALID_JSON_TEXT
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.InvalidJsonTextInParam">
            <summary>
            ER_INVALID_JSON_TEXT_IN_PARAM
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.InvalidJsonBinaryData">
            <summary>
            ER_INVALID_JSON_BINARY_DATA
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.InvalidJsonPath">
            <summary>
            ER_INVALID_JSON_PATH
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.InvalidJsonCharset">
            <summary>
            ER_INVALID_JSON_CHARSET
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.InvalidJsonCharsetInFunction">
            <summary>
            ER_INVALID_JSON_CHARSET_IN_FUNCTION
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.InvalidTypeForJson">
            <summary>
            ER_INVALID_TYPE_FOR_JSON
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.InvalidCastToJson">
            <summary>
            ER_INVALID_CAST_TO_JSON
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.InvalidJsonPathCharset">
            <summary>
            ER_INVALID_JSON_PATH_CHARSET
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.InvalidJsonPathWildcard">
            <summary>
            ER_INVALID_JSON_PATH_WILDCARD
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.JsonValueTooBig">
            <summary>
            ER_JSON_VALUE_TOO_BIG
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.JsonKeyTooBig">
            <summary>
            ER_JSON_KEY_TOO_BIG
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.JsonUsedAsKey">
            <summary>
            ER_JSON_USED_AS_KEY
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.JsonVacuousPath">
            <summary>
            ER_JSON_VACUOUS_PATH
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.JsonBadOneOrAllArg">
            <summary>
            ER_JSON_BAD_ONE_OR_ALL_ARG
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.NumericJsonValueOutOfRange">
            <summary>
            ER_NUMERIC_JSON_VALUE_OUT_OF_RANGE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.InvalidJsonValueForCast">
            <summary>
            ER_INVALID_JSON_VALUE_FOR_CAST
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.JsonDocumentTooDeep">
            <summary>
            ER_JSON_DOCUMENT_TOO_DEEP
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.JsonDocumentNullKey">
            <summary>
            ER_JSON_DOCUMENT_NULL_KEY
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.InvalidJsonPathArrayCell">
            <summary>
            ER_INVALID_JSON_PATH_ARRAY_CELL
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.NoSuchDb">
            <summary>
            ER_NO_SUCH_DB
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.MissingJsonTableValue">
            <summary>
            ER_MISSING_JSON_TABLE_VALUE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.WrongJsonTableValue">
            <summary>
            ER_WRONG_JSON_TABLE_VALUE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.JsonTableValueOutOfRange">
            <summary>
            ER_JT_VALUE_OUT_OF_RANGE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.JsonTableMaxNestedPath">
            <summary>
            ER_JT_MAX_NESTED_PATH
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.InvalidJsonType">
            <summary>
            ER_INVALID_JSON_TYPE
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.CannotConvertString">
            <summary>
            ER_CANNOT_CONVERT_STRING
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlErrorCode.ClientInteractionTimeout">
            <summary>
            ER_CLIENT_INTERACTION_TIMEOUT
            </summary>
        </member>
        <member name="T:MySqlConnector.MySqlException">
            <summary>
            <see cref="T:MySqlConnector.MySqlException"/> is thrown when MySQL Server returns an error code, or there is a
            communication error with the server.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlException.Number">
            <summary>
            A <see cref="T:MySqlConnector.MySqlErrorCode"/> value identifying the kind of error. Prefer to use the <see cref="P:MySqlConnector.MySqlException.ErrorCode"/> property.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlException.ErrorCode">
            <summary>
            A <see cref="T:MySqlConnector.MySqlErrorCode"/> value identifying the kind of error.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlException.SqlState">
            <summary>
            A <c>SQLSTATE</c> code identifying the kind of error.
            </summary>
            <remarks>See <a href="https://en.wikipedia.org/wiki/SQLSTATE">SQLSTATE</a> for more information.</remarks>
        </member>
        <member name="P:MySqlConnector.MySqlException.IsTransient">
            <summary>
            Returns <c>true</c> if this exception could indicate a transient error condition (that could succeed if retried); otherwise, <c>false</c>.
            </summary>
        </member>
        <member name="M:MySqlConnector.MySqlException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Sets the <see cref="T:System.Runtime.Serialization.SerializationInfo"/> with information about the exception.
            </summary>
            <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo"/> that will be set.</param>
            <param name="context">The context.</param>
        </member>
        <member name="P:MySqlConnector.MySqlException.Data">
            <summary>
            Gets a collection of key/value pairs that provide additional information about the exception.
            </summary>
        </member>
        <member name="T:MySqlConnector.MySqlGeometry">
            <summary>
            Represents MySQL's internal GEOMETRY format: https://dev.mysql.com/doc/refman/8.0/en/gis-data-formats.html#gis-internal-format
            </summary>
        </member>
        <member name="M:MySqlConnector.MySqlGeometry.FromWkb(System.Int32,System.ReadOnlySpan{System.Byte})">
            <summary>
            Constructs a <see cref="T:MySqlConnector.MySqlGeometry"/> from a SRID and Well-known Binary bytes.
            </summary>
            <param name="srid">The SRID (Spatial Reference System ID).</param>
            <param name="wkb">The Well-known Binary serialization of the geometry.</param>
            <returns>A new <see cref="T:MySqlConnector.MySqlGeometry"/> containing the specified geometry.</returns>
        </member>
        <member name="M:MySqlConnector.MySqlGeometry.FromMySql(System.ReadOnlySpan{System.Byte})">
            <summary>
            Constructs a <see cref="T:MySqlConnector.MySqlGeometry"/> from MySQL's internal format.
            </summary>
            <param name="value">The raw bytes of MySQL's internal GEOMETRY format.</param>
            <returns>A new <see cref="T:MySqlConnector.MySqlGeometry"/> containing the specified geometry.</returns>
            <remarks>See <a href="https://dev.mysql.com/doc/refman/8.0/en/gis-data-formats.html#gis-internal-format">Internal Geometry Storage Format</a>.</remarks>
        </member>
        <member name="P:MySqlConnector.MySqlGeometry.SRID">
            <summary>
            The Spatial Reference System ID of this geometry.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlGeometry.WKB">
            <summary>
            The Well-known Binary serialization of this geometry.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlGeometry.Value">
            <summary>
            The internal MySQL form of this geometry.
            </summary>
        </member>
        <member name="T:MySqlConnector.MySqlGuidFormat">
            <summary>
            Determines which column type (if any) should be read as a <c>System.Guid</c>.
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlGuidFormat.Default">
            <summary>
            Same as <c>Char36</c> if <c>OldGuids=False</c>; same as <c>LittleEndianBinary16</c> if <c>OldGuids=True</c>.
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlGuidFormat.None">
            <summary>
            No column types are read/written as a <code>Guid</code>.
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlGuidFormat.Char36">
            <summary>
            All <c>CHAR(36)</c> columns are read/written as a <c>Guid</c> using lowercase hex with hyphens,
            which matches <a href="https://dev.mysql.com/doc/refman/8.0/en/miscellaneous-functions.html#function_uuid"><c>UUID()</c></a>.
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlGuidFormat.Char32">
            <summary>
            All <c>CHAR(32)</c> columns are read/written as a <c>Guid</c> using lowercase hex without hyphens.
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlGuidFormat.Binary16">
            <summary>
            All <c>BINARY(16)</c> columns are read/written as a <c>Guid</c> using big-endian byte order,
            which matches <a href="https://dev.mysql.com/doc/refman/8.0/en/miscellaneous-functions.html#function_uuid-to-bin"><c>UUID_TO_BIN(x)</c></a>.
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlGuidFormat.TimeSwapBinary16">
            <summary>
            All <c>BINARY(16)</c> columns are read/written as a <c>Guid</c> using big-endian byte order with time parts swapped,
            which matches <a href="https://dev.mysql.com/doc/refman/8.0/en/miscellaneous-functions.html#function_uuid-to-bin"><c>UUID_TO_BIN(x,1)</c></a>.
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlGuidFormat.LittleEndianBinary16">
            <summary>
            All <c>BINARY(16)</c> columns are read/written as a <c>Guid</c> using little-endian byte order, i.e. the byte order
            used by <see cref="M:System.Guid.ToByteArray"/> and <see cref="M:System.Guid.#ctor(System.Byte[])"/>.
            </summary>
        </member>
        <member name="M:MySqlConnector.MySqlHelper.EscapeString(System.String)">
            <summary>
            Escapes single and double quotes, and backslashes in <paramref name="value"/>.
            </summary>
        </member>
        <member name="T:MySqlConnector.MySqlInfoMessageEventArgs">
            <summary>
            <see cref="T:MySqlConnector.MySqlInfoMessageEventArgs"/> contains the data supplied to the <see cref="T:MySqlConnector.MySqlInfoMessageEventHandler"/> event handler.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlInfoMessageEventArgs.Errors">
            <summary>
            The list of errors being reported.
            </summary>
        </member>
        <member name="T:MySqlConnector.MySqlInfoMessageEventHandler">
            <summary>
            Defines the event handler for <see cref="E:MySqlConnector.MySqlConnection.InfoMessage"/>.
            </summary>
            <param name="sender">The sender. This is the associated <see cref="T:MySqlConnector.MySqlConnection"/>.</param>
            <param name="args">The <see cref="T:MySqlConnector.MySqlInfoMessageEventArgs"/> containing the errors.</param>
        </member>
        <member name="F:MySqlConnector.MySqlLoadBalance.RoundRobin">
            <summary>
            Each new connection opened for a connection pool uses the next host name (sequentially with wraparound).
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlLoadBalance.FailOver">
            <summary>
            Each new connection tries to connect to the first host; subsequent hosts are used only if connecting to the first one fails.
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlLoadBalance.Random">
            <summary>
            Servers are tried in random order.
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlLoadBalance.LeastConnections">
            <summary>
            Servers are tried in ascending order of number of currently-open connections.
            </summary>
        </member>
        <member name="T:MySqlConnector.MySqlProtocolException">
            <summary>
            <see cref="T:MySqlConnector.MySqlProtocolException"/> is thrown when there is an internal protocol error communicating with MySQL Server.
            </summary>
        </member>
        <member name="M:MySqlConnector.MySqlProtocolException.CreateForPacketOutOfOrder(System.Int32,System.Int32)">
            <summary>
            Creates a new <see cref="T:MySqlConnector.MySqlProtocolException"/> for an out-of-order packet.
            </summary>
            <param name="expectedSequenceNumber">The expected packet sequence number.</param>
            <param name="packetSequenceNumber">The actual packet sequence number.</param>
            <returns>A new <see cref="T:MySqlConnector.MySqlProtocolException"/>.</returns>
        </member>
        <member name="T:MySqlConnector.MySqlProvidePasswordContext">
            <summary>
            Provides context for the <see cref="P:MySqlConnector.MySqlConnection.ProvidePasswordCallback"/> delegate.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlProvidePasswordContext.Server">
            <summary>
            The server to which MySqlConnector is connecting. This is a host name from the <see cref="P:MySqlConnector.MySqlConnectionStringBuilder.Server"/> option.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlProvidePasswordContext.Port">
            <summary>
            The server port. This corresponds to <see cref="P:MySqlConnector.MySqlConnectionStringBuilder.Port"/>.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlProvidePasswordContext.UserId">
            <summary>
            The user ID being used for authentication. This corresponds to <see cref="P:MySqlConnector.MySqlConnectionStringBuilder.UserID"/>.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlProvidePasswordContext.Database">
            <summary>
            The optional initial database; this value may be the empty string. This corresponds to <see cref="P:MySqlConnector.MySqlConnectionStringBuilder.Database"/>.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlRowsCopiedEventArgs.Abort">
            <summary>
            Gets or sets a value that indicates whether the bulk copy operation should be aborted.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlRowsCopiedEventArgs.RowsCopied">
            <summary>
            Gets a value that returns the number of rows copied during the current bulk copy operation.
            </summary>
        </member>
        <member name="T:MySqlConnector.MySqlRowsCopiedEventHandler">
            <summary>
            Represents the method that handles the <see cref="E:MySqlConnector.MySqlBulkCopy.MySqlRowsCopied"/> event of a <see cref="T:MySqlConnector.MySqlBulkCopy"/>.
            </summary>
        </member>
        <member name="T:MySqlConnector.MySqlServerRedirectionMode">
            <summary>
            Server redirection configuration.
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlServerRedirectionMode.Disabled">
            <summary>
            Server redirection will not be performed.
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlServerRedirectionMode.Preferred">
            <summary>
            Server redirection will occur if possible, otherwise the original connection will be used.
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlServerRedirectionMode.Required">
            <summary>
            Server redirection must occur, otherwise connecting fails.
            </summary>
        </member>
        <member name="T:MySqlConnector.MySqlSslMode">
            <summary>
            SSL connection options.
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlSslMode.None">
            <summary>
            Do not use SSL.
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlSslMode.Disabled">
            <summary>
            Do not use SSL. This is the same as <see cref="F:MySqlConnector.MySqlSslMode.None"/>.
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlSslMode.Preferred">
            <summary>
            Use SSL if the server supports it.
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlSslMode.Required">
            <summary>
            Always use SSL. Deny connection if server does not support SSL.
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlSslMode.VerifyCA">
            <summary>
             Always use SSL. Validate the Certificate Authority but tolerate name mismatch.
            </summary>
        </member>
        <member name="F:MySqlConnector.MySqlSslMode.VerifyFull">
            <summary>
            Always use SSL. Fail if the host name is not correct.
            </summary>
        </member>
        <member name="T:MySqlConnector.MySqlTransaction">
            <summary>
            <see cref="T:MySqlConnector.MySqlTransaction"/> represents an in-progress transaction on a MySQL Server.
            </summary>
        </member>
        <member name="M:MySqlConnector.MySqlTransaction.Commit">
            <summary>
            Commits the database transaction.
            </summary>
        </member>
        <member name="M:MySqlConnector.MySqlTransaction.CommitAsync(System.Threading.CancellationToken)">
            <summary>
            Asynchronously commits the database transaction.
            </summary>
            <param name="cancellationToken">A token to cancel the asynchronous operation.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> representing the asynchronous operation.</returns>
        </member>
        <member name="M:MySqlConnector.MySqlTransaction.Rollback">
            <summary>
            Rolls back the database transaction.
            </summary>
        </member>
        <member name="M:MySqlConnector.MySqlTransaction.RollbackAsync(System.Threading.CancellationToken)">
            <summary>
            Asynchronously rolls back the database transaction.
            </summary>
            <param name="cancellationToken">A token to cancel the asynchronous operation.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> representing the asynchronous operation.</returns>
        </member>
        <member name="M:MySqlConnector.MySqlTransaction.Release(System.String)">
            <summary>
            Removes the named transaction savepoint with the specified <paramref name="savepointName"/>. No commit or rollback occurs.
            </summary>
            <param name="savepointName">The savepoint name.</param>
            <remarks>The proposed ADO.NET API that this is based on is not finalized; this API may change in the future.</remarks>
        </member>
        <member name="M:MySqlConnector.MySqlTransaction.ReleaseAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Asynchronously removes the named transaction savepoint with the specified <paramref name="savepointName"/>. No commit or rollback occurs.
            </summary>
            <param name="savepointName">The savepoint name.</param>
            <param name="cancellationToken">A token to cancel the asynchronous operation.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> representing the asynchronous operation.</returns>
            <remarks>The proposed ADO.NET API that this is based on is not finalized; this API may change in the future.</remarks>
        </member>
        <member name="M:MySqlConnector.MySqlTransaction.Rollback(System.String)">
            <summary>
            Rolls back the current transaction to the savepoint with the specified <paramref name="savepointName"/> without aborting the transaction.
            </summary>
            <param name="savepointName">The savepoint name.</param>
            <remarks><para>The name must have been created with <see cref="M:MySqlConnector.MySqlTransaction.Save(System.String)"/>, but not released by calling <see cref="M:MySqlConnector.MySqlTransaction.Release(System.String)"/>.</para>
            <para>The proposed ADO.NET API that this is based on is not finalized; this API may change in the future.</para></remarks>
        </member>
        <member name="M:MySqlConnector.MySqlTransaction.RollbackAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Asynchronously rolls back the current transaction to the savepoint with the specified <paramref name="savepointName"/> without aborting the transaction.
            </summary>
            <param name="savepointName">The savepoint name.</param>
            <param name="cancellationToken">A token to cancel the asynchronous operation.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> representing the asynchronous operation.</returns>
            <remarks><para>The name must have been created with <see cref="M:MySqlConnector.MySqlTransaction.SaveAsync(System.String,System.Threading.CancellationToken)"/>, but not released by calling <see cref="M:MySqlConnector.MySqlTransaction.ReleaseAsync(System.String,System.Threading.CancellationToken)"/>.</para>
            <para>The proposed ADO.NET API that this is based on is not finalized; this API may change in the future.</para></remarks>
        </member>
        <member name="M:MySqlConnector.MySqlTransaction.Save(System.String)">
            <summary>
            Sets a named transaction savepoint with the specified <paramref name="savepointName"/>. If the current transaction
            already has a savepoint with the same name, the old savepoint is deleted and a new one is set.
            </summary>
            <param name="savepointName">The savepoint name.</param>
            <remarks>The proposed ADO.NET API that this is based on is not finalized; this API may change in the future.</remarks>
        </member>
        <member name="M:MySqlConnector.MySqlTransaction.SaveAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Asynchronously sets a named transaction savepoint with the specified <paramref name="savepointName"/>. If the current transaction
            already has a savepoint with the same name, the old savepoint is deleted and a new one is set.
            </summary>
            <param name="savepointName">The savepoint name.</param>
            <param name="cancellationToken">A token to cancel the asynchronous operation.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> representing the asynchronous operation.</returns>
            <remarks>The proposed ADO.NET API that this is based on is not finalized; this API may change in the future.</remarks>
        </member>
        <member name="P:MySqlConnector.MySqlTransaction.Connection">
            <summary>
            Gets the <see cref="T:MySqlConnector.MySqlConnection"/> that this transaction is associated with.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlTransaction.DbConnection">
            <summary>
            Gets the <see cref="T:MySqlConnector.MySqlConnection"/> that this transaction is associated with.
            </summary>
        </member>
        <member name="P:MySqlConnector.MySqlTransaction.IsolationLevel">
            <summary>
            Gets the <see cref="P:MySqlConnector.MySqlTransaction.IsolationLevel"/> of this transaction. This value is set from <see cref="M:MySqlConnector.MySqlConnection.BeginTransaction(System.Data.IsolationLevel)"/>
            or any other overload that specifies an <see cref="P:MySqlConnector.MySqlTransaction.IsolationLevel"/>.
            </summary>
        </member>
        <member name="M:MySqlConnector.MySqlTransaction.Dispose(System.Boolean)">
            <summary>
            Releases any resources associated with this transaction. If it was not committed, it will be rolled back.
            </summary>
            <param name="disposing"><c>true</c> if this method is being called from <c>Dispose</c>; <c>false</c> if being called from a finalizer.</param>
        </member>
        <member name="M:MySqlConnector.MySqlTransaction.DisposeAsync">
            <summary>
            Asynchronously releases any resources associated with this transaction. If it was not committed, it will be rolled back.
            </summary>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> representing the asynchronous operation.</returns>
        </member>
        <member name="T:MySqlConnector.Protocol.CharacterSet">
            <summary>
            MySQL character set codes.
            </summary>
            <remarks>Obtained from <c>SELECT id, collation_name FROM information_schema.collations ORDER BY id;</c> on MySQL 8.0.30.</remarks>
        </member>
        <member name="F:MySqlConnector.Protocol.ColumnFlags.NotNull">
            <summary>
            Field cannot be <c>NULL</c>.
            </summary>
        </member>
        <member name="F:MySqlConnector.Protocol.ColumnFlags.PrimaryKey">
            <summary>
            Field is part of a primary key.
            </summary>
        </member>
        <member name="F:MySqlConnector.Protocol.ColumnFlags.UniqueKey">
            <summary>
            Field is part of a unique key.
            </summary>
        </member>
        <member name="F:MySqlConnector.Protocol.ColumnFlags.MultipleKey">
            <summary>
            Field is part of a nonunique key.
            </summary>
        </member>
        <member name="F:MySqlConnector.Protocol.ColumnFlags.Blob">
            <summary>
            Field is a <c>BLOB</c> or <c>TEXT</c> (deprecated).
            </summary>
        </member>
        <member name="F:MySqlConnector.Protocol.ColumnFlags.Unsigned">
            <summary>
            Field has the <c>UNSIGNED</c> attribute.
            </summary>
        </member>
        <member name="F:MySqlConnector.Protocol.ColumnFlags.ZeroFill">
            <summary>
            Field has the <c>ZEROFILL</c> attribute.
            </summary>
        </member>
        <member name="F:MySqlConnector.Protocol.ColumnFlags.Binary">
            <summary>
            Field has the <c>BINARY</c> attribute.
            </summary>
        </member>
        <member name="F:MySqlConnector.Protocol.ColumnFlags.Enum">
            <summary>
            Field is an <c>ENUM</c>.
            </summary>
        </member>
        <member name="F:MySqlConnector.Protocol.ColumnFlags.AutoIncrement">
            <summary>
            Field has the <c>AUTO_INCREMENT</c> attribute.
            </summary>
        </member>
        <member name="F:MySqlConnector.Protocol.ColumnFlags.Timestamp">
            <summary>
            Field is a <c>TIMESTAMP</c> (deprecated).
            </summary>
        </member>
        <member name="F:MySqlConnector.Protocol.ColumnFlags.Set">
            <summary>
            Field is a <c>SET</c>.
            </summary>
        </member>
        <member name="F:MySqlConnector.Protocol.ColumnFlags.Number">
            <summary>
            Field is numeric.
            </summary>
        </member>
        <member name="T:MySqlConnector.Protocol.ColumnType">
            <summary>
            See <a href="https://dev.mysql.com/doc/internals/en/com-query-response.html#column-type">MySQL documentation</a>.
            </summary>
        </member>
        <member name="T:MySqlConnector.Protocol.Payloads.ColumnCountPayload">
            <summary>
                Helper class to parse Column count packet.
                https://mariadb.com/kb/en/result-set-packets/#column-count-packet
                Packet contains a columnCount, and - if capability MARIADB_CLIENT_CACHE_METADATA is set - a flag to indicate if metadata follows
            </summary>
        </member>
        <member name="M:MySqlConnector.Protocol.Payloads.EofPayload.IsEof(MySqlConnector.Protocol.PayloadData)">
            <summary>
            Returns <c>true</c> if <paramref name="payload"/> contains an <a href="https://dev.mysql.com/doc/internals/en/packet-EOF_Packet.html">EOF packet</a>.
            Note that EOF packets can appear in places where a length-encoded integer (which starts with the same signature byte) may appear, so the length
            has to be checked to verify that it is an EOF packet.
            </summary>
            <param name="payload">The payload to examine.</param>
            <returns><c>true</c> if this is an EOF packet; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:MySqlConnector.Protocol.Payloads.OkPayload.Create(System.ReadOnlySpan{System.Byte},MySqlConnector.Core.IServerCapabilities)">
            <summary>
            Creates an <see cref="T:MySqlConnector.Protocol.Payloads.OkPayload"/> from the given <paramref name="span"/>, or throws <see cref="T:System.FormatException"/>
            if the bytes do not represent a valid <see cref="T:MySqlConnector.Protocol.Payloads.OkPayload"/>.
            </summary>
            <param name="span">The bytes from which to read an OK packet.</param>
            <param name="serverCapabilities">The server capabilities.</param>
            <returns>A <see cref="T:MySqlConnector.Protocol.Payloads.OkPayload"/> with the contents of the OK packet.</returns>
            <exception cref="T:System.FormatException">Thrown when the bytes are not a valid OK packet.</exception>
        </member>
        <member name="M:MySqlConnector.Protocol.Payloads.OkPayload.Verify(System.ReadOnlySpan{System.Byte},MySqlConnector.Core.IServerCapabilities)">
            <summary>
            Verifies that the bytes in the given <paramref name="span"/> form a valid <see cref="T:MySqlConnector.Protocol.Payloads.OkPayload"/>, or throws
            <see cref="T:System.FormatException"/> if they do not.
            </summary>
            <param name="span">The bytes from which to read an OK packet.</param>
            <param name="serverCapabilities">The server capabilities.</param>
            <exception cref="T:System.FormatException">Thrown when the bytes are not a valid OK packet.</exception>
        </member>
        <member name="T:MySqlConnector.Protocol.ProtocolCapabilities">
            <summary>
            The <a href="https://dev.mysql.com/doc/internals/en/capability-flags.html">MySQL Capability flags</a>.
            </summary>
        </member>
        <member name="F:MySqlConnector.Protocol.ProtocolCapabilities.None">
            <summary>
            No specified capabilities.
            </summary>
        </member>
        <member name="F:MySqlConnector.Protocol.ProtocolCapabilities.LongPassword">
            <summary>
            Use the improved version of Old Password Authentication.
            </summary>
        </member>
        <member name="F:MySqlConnector.Protocol.ProtocolCapabilities.FoundRows">
            <summary>
            Send found rows instead of affected rows in EOF_Packet.
            </summary>
        </member>
        <member name="F:MySqlConnector.Protocol.ProtocolCapabilities.LongFlag">
            <summary>
            Longer flags in Protocol::ColumnDefinition320.
            </summary>
        </member>
        <member name="F:MySqlConnector.Protocol.ProtocolCapabilities.ConnectWithDatabase">
            <summary>
            Database (schema) name can be specified on connect in Handshake Response Packet.
            </summary>
        </member>
        <member name="F:MySqlConnector.Protocol.ProtocolCapabilities.NoSchema">
            <summary>
            Do not permit database.table.column.
            </summary>
        </member>
        <member name="F:MySqlConnector.Protocol.ProtocolCapabilities.Compress">
            <summary>
            Supports compression.
            </summary>
        </member>
        <member name="F:MySqlConnector.Protocol.ProtocolCapabilities.Odbc">
            <summary>
            Special handling of ODBC behavior.
            </summary>
        </member>
        <member name="F:MySqlConnector.Protocol.ProtocolCapabilities.LocalFiles">
            <summary>
            Enables the LOCAL INFILE request of LOAD DATA|XML.
            </summary>
        </member>
        <member name="F:MySqlConnector.Protocol.ProtocolCapabilities.IgnoreSpace">
            <summary>
            Parser can ignore spaces before '('.
            </summary>
        </member>
        <member name="F:MySqlConnector.Protocol.ProtocolCapabilities.Protocol41">
            <summary>
            Supports the 4.1 protocol.
            </summary>
        </member>
        <member name="F:MySqlConnector.Protocol.ProtocolCapabilities.Interactive">
            <summary>
            Server: Supports interactive and noninteractive clients. Client: The session <code>wait_timeout</code> variable is set to the value of the session <code>interactive_timeout</code> variable.
            </summary>
        </member>
        <member name="F:MySqlConnector.Protocol.ProtocolCapabilities.Ssl">
            <summary>
            Supports SSL.
            </summary>
        </member>
        <member name="F:MySqlConnector.Protocol.ProtocolCapabilities.Transactions">
            <summary>
            Can send status flags in EOF_Packet.
            </summary>
        </member>
        <member name="F:MySqlConnector.Protocol.ProtocolCapabilities.SecureConnection">
            <summary>
            Supports Authentication::Native41.
            </summary>
        </member>
        <member name="F:MySqlConnector.Protocol.ProtocolCapabilities.MultiStatements">
            <summary>
            Can handle multiple statements per COM_QUERY and COM_STMT_PREPARE.
            </summary>
        </member>
        <member name="F:MySqlConnector.Protocol.ProtocolCapabilities.MultiResults">
            <summary>
            Can send multiple resultsets for COM_QUERY.
            </summary>
        </member>
        <member name="F:MySqlConnector.Protocol.ProtocolCapabilities.PreparedStatementMultiResults">
            <summary>
            Can send multiple resultsets for COM_STMT_EXECUTE.
            </summary>
        </member>
        <member name="F:MySqlConnector.Protocol.ProtocolCapabilities.PluginAuth">
            <summary>
            Sends extra data in Initial Handshake Packet and supports the pluggable authentication protocol.
            </summary>
        </member>
        <member name="F:MySqlConnector.Protocol.ProtocolCapabilities.ConnectionAttributes">
            <summary>
            Permits connection attributes in Protocol::HandshakeResponse41.
            </summary>
        </member>
        <member name="F:MySqlConnector.Protocol.ProtocolCapabilities.PluginAuthLengthEncodedClientData">
            <summary>
            Understands length-encoded integer for auth response data in Protocol::HandshakeResponse41.
            </summary>
        </member>
        <member name="F:MySqlConnector.Protocol.ProtocolCapabilities.CanHandleExpiredPasswords">
            <summary>
            Announces support for expired password extension.
            </summary>
        </member>
        <member name="F:MySqlConnector.Protocol.ProtocolCapabilities.SessionTrack">
            <summary>
            Can set SERVER_SESSION_STATE_CHANGED in the Status Flags and send session-state change data after a OK packet.
            </summary>
        </member>
        <member name="F:MySqlConnector.Protocol.ProtocolCapabilities.DeprecateEof">
            <summary>
            Can send OK after a Text Resultset.
            </summary>
        </member>
        <member name="F:MySqlConnector.Protocol.ProtocolCapabilities.ClientOptionalResultsetMetadata">
            <summary>
            The client can handle optional metadata information in the resultset.
            </summary>
            <remarks>Corresponds to CLIENT_OPTIONAL_RESULTSET_METADATA.</remarks>
        </member>
        <member name="F:MySqlConnector.Protocol.ProtocolCapabilities.ZstandardCompressionAlgorithm">
            <summary>
            The client supports the Zstandard compression algorithm.
            </summary>
            <remarks>Corresponds to CLIENT_ZSTD_COMPRESSION_ALGORITHM.</remarks>
        </member>
        <member name="F:MySqlConnector.Protocol.ProtocolCapabilities.QueryAttributes">
            <summary>
            Supports query attributes (CLIENT_QUERY_ATTRIBUTES).
            </summary>
        </member>
        <member name="F:MySqlConnector.Protocol.ProtocolCapabilities.MariaDbClientProgress">
            <summary>
            Client supports progress indicator.
            </summary>
        </member>
        <member name="F:MySqlConnector.Protocol.ProtocolCapabilities.MariaDbComMulti">
            <summary>
            Client supports COM_MULTI (i.e., CommandKind.Multi)
            </summary>
        </member>
        <member name="F:MySqlConnector.Protocol.ProtocolCapabilities.MariaDbStatementBulkOperations">
            <summary>
            Support of array binding.
            </summary>
        </member>
        <member name="F:MySqlConnector.Protocol.ProtocolCapabilities.MariaDbExtendedTypeInfo">
            <summary>
            metadata extended information
            </summary>
        </member>
        <member name="F:MySqlConnector.Protocol.ProtocolCapabilities.MariaDbCacheMetadata">
            <summary>
            Client supports caching metadata for prepared statements (MARIADB_CLIENT_CACHE_METADATA).
            </summary>
        </member>
        <member name="T:MySqlConnector.Protocol.Serialization.ArraySegmentHolder`1">
            <summary>
            <see cref="T:MySqlConnector.Protocol.Serialization.ArraySegmentHolder`1"/> is a class that holds an instance of <see cref="T:System.ArraySegment`1"/>.
            Its primary difference from <see cref="T:System.ArraySegment`1"/> is that it's a reference type, so mutations
            to this object are visible to other objects that hold a reference to it.
            </summary>
        </member>
        <member name="M:MySqlConnector.Protocol.Serialization.AuthenticationUtility.GetNullTerminatedPasswordBytes(System.String)">
            <summary>
            Returns the UTF-8 bytes for <paramref name="password"/>, followed by a null byte.
            </summary>
        </member>
        <member name="M:MySqlConnector.Protocol.Serialization.AuthenticationUtility.HashPassword(System.ReadOnlySpan{System.Byte},System.String,System.Boolean)">
            <summary>
            Hashes a password with the "Secure Password Authentication" method.
            </summary>
            <param name="challenge">The 20-byte random challenge (from the "auth-plugin-data" in the initial handshake).</param>
            <param name="password">The password to hash.</param>
            <param name="onlyHashPassword">If true, <paramref name="challenge"/> is ignored and only the twice-hashed password
            is returned, instead of performing the full "secure password authentication" algorithm that XORs the hashed password against
            a hash derived from the challenge.</param>
            <returns>A 20-byte password hash.</returns>
            <remarks>See <a href="https://dev.mysql.com/doc/internals/en/secure-password-authentication.html">Secure Password Authentication</a>.</remarks>
        </member>
        <member name="T:MySqlConnector.Protocol.Serialization.NegotiateToMySqlConverterStream">
             <summary>
             Helper class to translate NegotiateStream framing for SPNEGO token
             into MySQL protocol packets.
            
             Serves as underlying stream for System.Net.NegotiateStream
             to perform MariaDB's auth_gssapi_client authentication.
            
             NegotiateStream protocol is described in e.g here
             https://winprotocoldoc.blob.core.windows.net/productionwindowsarchives/MS-NNS/[MS-NNS].pdf
             We only use Handshake Messages for authentication.
             </summary>
        </member>
        <member name="P:MySqlConnector.Protocol.Serialization.IByteHandler.RemainingTimeout">
            <summary>
            The remaining timeout (in milliseconds) for the next I/O read. Use <see cref="F:MySqlConnector.Utilities.Constants.InfiniteTimeout"/> to represent no (or, infinite) timeout.
            </summary>
        </member>
        <member name="M:MySqlConnector.Protocol.Serialization.IByteHandler.ReadBytesAsync(System.Memory{System.Byte},MySqlConnector.Protocol.Serialization.IOBehavior)">
            <summary>
            Reads data from this byte handler.
            </summary>
            <param name="buffer">The buffer to read into.</param>
            <param name="ioBehavior">The <see cref="T:MySqlConnector.Protocol.Serialization.IOBehavior"/> to use when reading data.</param>
            <returns>A <see cref="T:System.Threading.Tasks.ValueTask`1"/> holding the number of bytes read. If reading failed, this will be zero.</returns>
        </member>
        <member name="M:MySqlConnector.Protocol.Serialization.IByteHandler.WriteBytesAsync(System.ReadOnlyMemory{System.Byte},MySqlConnector.Protocol.Serialization.IOBehavior)">
            <summary>
            Writes data to this byte handler.
            </summary>
            <param name="data">The data to write.</param>
            <param name="ioBehavior">The <see cref="T:MySqlConnector.Protocol.Serialization.IOBehavior"/> to use when writing.</param>
        </member>
        <member name="T:MySqlConnector.Protocol.Serialization.IOBehavior">
            <summary>
            Specifies whether to perform synchronous or asynchronous I/O.
            </summary>
        </member>
        <member name="F:MySqlConnector.Protocol.Serialization.IOBehavior.Synchronous">
            <summary>
            Use synchronous I/O.
            </summary>
        </member>
        <member name="F:MySqlConnector.Protocol.Serialization.IOBehavior.Asynchronous">
            <summary>
            Use asynchronous I/O.
            </summary>
        </member>
        <member name="M:MySqlConnector.Protocol.Serialization.IPayloadHandler.StartNewConversation">
            <summary>
            Starts a new "conversation" with the MySQL Server. This resets the "<a href="https://dev.mysql.com/doc/internals/en/sequence-id.html">sequence id</a>"
            and should be called when a new command begins.
            </summary>
        </member>
        <member name="M:MySqlConnector.Protocol.Serialization.IPayloadHandler.SetNextSequenceNumber(System.Int32)">
            <summary>
            Forces the next sequence number to be the specified value.
            </summary>
            <param name="sequenceNumber">The next sequence number.</param>
            <remarks>This should only be used in advanced scenarios.</remarks>
        </member>
        <member name="P:MySqlConnector.Protocol.Serialization.IPayloadHandler.ByteHandler">
            <summary>
            Gets or sets the underlying <see cref="T:MySqlConnector.Protocol.Serialization.IByteHandler"/> that data is read from and written to.
            </summary>
        </member>
        <member name="M:MySqlConnector.Protocol.Serialization.IPayloadHandler.ReadPayloadAsync(MySqlConnector.Protocol.Serialization.ArraySegmentHolder{System.Byte},MySqlConnector.Protocol.Serialization.ProtocolErrorBehavior,MySqlConnector.Protocol.Serialization.IOBehavior)">
            <summary>
            Reads the next payload.
            </summary>
            <param name="cache">An <see cref="T:MySqlConnector.Protocol.Serialization.ArraySegmentHolder`1"/> that will cache any buffers allocated during this
            read. (To disable caching, pass <code>new ArraySegmentHolder&lt;byte&gt;</code> so the cache will be garbage-collected
            when this method returns.)</param>
            <param name="protocolErrorBehavior">The <see cref="T:MySqlConnector.Protocol.Serialization.ProtocolErrorBehavior"/> to use if there is a protocol error.</param>
            <param name="ioBehavior">The <see cref="T:MySqlConnector.Protocol.Serialization.IOBehavior"/> to use when reading data.</param>
            <returns>An <see cref="T:System.ArraySegment`1"/> containing the data that was read. This
            <see cref="T:System.ArraySegment`1"/> will be valid to read from until the next time <see cref="M:MySqlConnector.Protocol.Serialization.IPayloadHandler.ReadPayloadAsync(MySqlConnector.Protocol.Serialization.ArraySegmentHolder{System.Byte},MySqlConnector.Protocol.Serialization.ProtocolErrorBehavior,MySqlConnector.Protocol.Serialization.IOBehavior)"/> or
            <see cref="M:MySqlConnector.Protocol.Serialization.IPayloadHandler.WritePayloadAsync(System.ReadOnlyMemory{System.Byte},MySqlConnector.Protocol.Serialization.IOBehavior)"/> is called.</returns>
        </member>
        <member name="M:MySqlConnector.Protocol.Serialization.IPayloadHandler.WritePayloadAsync(System.ReadOnlyMemory{System.Byte},MySqlConnector.Protocol.Serialization.IOBehavior)">
            <summary>
            Writes a payload.
            </summary>
            <param name="payload">The data to write.</param>
            <param name="ioBehavior">The <see cref="T:MySqlConnector.Protocol.Serialization.IOBehavior"/> to use when writing.</param>
        </member>
        <member name="T:MySqlConnector.Protocol.Serialization.ProtocolErrorBehavior">
            <summary>
            Specifies how to handle protocol errors.
            </summary>
        </member>
        <member name="F:MySqlConnector.Protocol.Serialization.ProtocolErrorBehavior.Throw">
            <summary>
            Throw an exception when there is a protocol error. This is the default.
            </summary>
        </member>
        <member name="F:MySqlConnector.Protocol.Serialization.ProtocolErrorBehavior.Ignore">
            <summary>
            Ignore any protocol errors.
            </summary>
        </member>
        <member name="F:MySqlConnector.Protocol.ServerStatus.InTransaction">
            <summary>
            A transaction is active.
            </summary>
        </member>
        <member name="F:MySqlConnector.Protocol.ServerStatus.AutoCommit">
            <summary>
            Auto-commit is enabled
            </summary>
        </member>
        <member name="F:MySqlConnector.Protocol.ServerStatus.CursorExists">
            <summary>
            Used by Binary Protocol Resultset to signal that COM_STMT_FETCH must be used to fetch the row-data.
            </summary>
        </member>
        <member name="F:MySqlConnector.Protocol.ServerStatus.InReadOnlyTransaction">
            <summary>
            In a read-only transaction.
            </summary>
        </member>
        <member name="F:MySqlConnector.Protocol.ServerStatus.SessionStateChanged">
            <summary>
            Connection state information has changed.
            </summary>
        </member>
        <member name="F:MySqlConnector.Protocol.SessionTrackKind.SystemVariables">
            <summary>
            SESSION_TRACK_SYSTEM_VARIABLES: one or more system variables changed
            </summary>
        </member>
        <member name="F:MySqlConnector.Protocol.SessionTrackKind.Schema">
            <summary>
            SESSION_TRACK_SCHEMA: schema changed
            </summary>
        </member>
        <member name="F:MySqlConnector.Protocol.SessionTrackKind.StateChange">
            <summary>
            SESSION_TRACK_STATE_CHANGE: "track state change" changed
            </summary>
        </member>
        <member name="F:MySqlConnector.Protocol.SessionTrackKind.Gtids">
            <summary>
            SESSION_TRACK_GTIDS: "track GTIDs" changed
            </summary>
        </member>
        <member name="T:MySqlConnector.Utilities.Adler32">
            <summary>
            Calculates the 32 bit Adler checksum of a given buffer according to
            RFC 1950. ZLIB Compressed Data Format Specification version 3.3)
            </summary>
        </member>
        <member name="F:MySqlConnector.Utilities.Adler32.SeedValue">
            <summary>
            The default initial seed value of a Adler32 checksum calculation.
            </summary>
        </member>
        <member name="M:MySqlConnector.Utilities.Adler32.Calculate(System.ReadOnlySpan{System.Byte})">
            <summary>
            Calculates the Adler32 checksum with the bytes taken from the span.
            </summary>
            <param name="buffer">The readonly span of bytes.</param>
            <returns>The <see cref="T:System.UInt32"/>.</returns>
        </member>
        <member name="F:MySqlConnector.Utilities.Constants.InfiniteTimeout">
            <summary>
            A sentinel value indicating no (or infinite) timeout.
            </summary>
        </member>
        <member name="T:MySqlConnector.Utilities.ResizableArray`1">
            <summary>
            A wrapper around a resizable array. This type is intended to be used with <see cref="T:MySqlConnector.Utilities.ResizableArraySegment`1"/>.
            </summary>
        </member>
        <member name="M:MySqlConnector.Utilities.ResizableArray`1.DoResize(System.Int32)">
            <summary>
            Do not call this method directly; use <see cref="M:MySqlConnector.Utilities.Utility.Resize``1(MySqlConnector.Utilities.ResizableArray{``0}@,System.Int32)"/>.
            </summary>
        </member>
        <member name="T:MySqlConnector.Utilities.ResizableArraySegment`1">
            <summary>
            An <see cref="T:System.ArraySegment`1"/> that supports having its underlying array reallocated and resized.
            </summary>
        </member>
        <member name="M:MySqlConnector.Utilities.ResizableArraySegment`1.#ctor(MySqlConnector.Utilities.ResizableArray{`0},System.Int32,System.Int32)">
            <summary>
            An <see cref="T:System.ArraySegment`1"/> that supports having its underlying array reallocated and resized.
            </summary>
        </member>
        <member name="M:MySqlConnector.Utilities.TimerQueue.Add(System.Int32,System.Action)">
            <summary>
            Adds a timer that will invoke <paramref name="action"/> in approximately <paramref name="delay"/> milliseconds.
            </summary>
            <param name="delay">The time (in milliseconds) to wait before invoking <paramref name="action"/>.</param>
            <param name="action">The callback to invoke.</param>
            <returns>A timer ID that can be passed to <see cref="M:MySqlConnector.Utilities.TimerQueue.Remove(System.UInt32)"/> to cancel the timer.</returns>
        </member>
        <member name="M:MySqlConnector.Utilities.TimerQueue.Remove(System.UInt32)">
            <summary>
            Cancels the timer with the specified ID.
            </summary>
            <param name="id">The timer ID (returned from <see cref="M:MySqlConnector.Utilities.TimerQueue.Add(System.Int32,System.Action)"/>).</param>
            <returns><c>true</c> if the timer was removed; otherwise, <c>false</c>. This method will return <c>false</c> if the specified timer has already fired.</returns>
        </member>
        <member name="M:MySqlConnector.Utilities.Utility.GetRsaParameters(System.String)">
            <summary>
            Loads a RSA key from a PEM string.
            </summary>
        </member>
        <member name="M:MySqlConnector.Utilities.Utility.Slice``1(System.ArraySegment{``0},System.Int32)">
            <summary>
            Returns a new <see cref="T:System.ArraySegment`1"/> that starts at index <paramref name="index"/> into <paramref name="arraySegment"/>.
            </summary>
            <param name="arraySegment">The <see cref="T:System.ArraySegment`1"/> from which to create a slice.</param>
            <param name="index">The non-negative, zero-based starting index of the new slice (relative to <see cref="P:System.ArraySegment`1.Offset"/> of <paramref name="arraySegment"/>.</param>
            <returns>A new <see cref="T:System.ArraySegment`1"/> starting at the <paramref name="index"/>th element of <paramref name="arraySegment"/> and continuing to the end of <paramref name="arraySegment"/>.</returns>
        </member>
        <member name="M:MySqlConnector.Utilities.Utility.Slice``1(System.ArraySegment{``0},System.Int32,System.Int32)">
            <summary>
            Returns a new <see cref="T:System.ArraySegment`1"/> that starts at index <paramref name="index"/> into <paramref name="arraySegment"/> and has a length of <paramref name="length"/>.
            </summary>
            <param name="arraySegment">The <see cref="T:System.ArraySegment`1"/> from which to create a slice.</param>
            <param name="index">The non-negative, zero-based starting index of the new slice (relative to <see cref="P:System.ArraySegment`1.Offset"/> of <paramref name="arraySegment"/>.</param>
            <param name="length">The non-negative length of the new slice.</param>
            <returns>A new <see cref="T:System.ArraySegment`1"/> of length <paramref name="length"/>, starting at the <paramref name="index"/>th element of <paramref name="arraySegment"/>.</returns>
        </member>
        <member name="M:MySqlConnector.Utilities.Utility.ArraySlice(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Returns a new <see cref="T:System.Byte"/> array that is a slice of <paramref name="input"/> starting at <paramref name="offset"/>.
            </summary>
            <param name="input">The array to slice.</param>
            <param name="offset">The offset at which to slice.</param>
            <param name="length">The length of the slice.</param>
            <returns>A new <see cref="T:System.Byte"/> array that is a slice of <paramref name="input"/> from <paramref name="offset"/> to the end.</returns>
        </member>
        <member name="M:MySqlConnector.Utilities.Utility.FindNextIndex(System.ReadOnlySpan{System.Byte},System.Int32,System.ReadOnlySpan{System.Byte})">
            <summary>
            Finds the next index of <paramref name="pattern"/> in <paramref name="data"/>, starting at index <paramref name="offset"/>.
            </summary>
            <param name="data">The array to search.</param>
            <param name="offset">The offset at which to start searching.</param>
            <param name="pattern">The pattern to find in <paramref name="data"/>.</param>
            <returns>The offset of <paramref name="pattern"/> within <paramref name="data"/>, or <c>-1</c> if <paramref name="pattern"/> was not found.</returns>
        </member>
        <member name="M:MySqlConnector.Utilities.Utility.Resize``1(MySqlConnector.Utilities.ResizableArray{``0}@,System.Int32)">
            <summary>
            Resizes <paramref name="resizableArray"/> to hold at least <paramref name="newLength"/> items.
            </summary>
            <remarks><paramref name="resizableArray"/> may be <c>null</c>, in which case a new <see cref="T:MySqlConnector.Utilities.ResizableArray`1"/> will be allocated.</remarks>
        </member>
        <member name="M:MySqlConnector.Utilities.Utility.GetElapsedMilliseconds(System.Int64)">
            <summary>
            Gets the elapsed time (in milliseconds) since the specified <paramref name="startingTimestamp"/> (which must be a value returned from <see cref="M:System.Diagnostics.Stopwatch.GetTimestamp"/>.
            </summary>
        </member>
        <member name="M:MySqlConnector.Utilities.Utility.GetElapsedSeconds(System.Int64,System.Int64)">
            <summary>
            Gets the elapsed time (in seconds) between the specified <paramref name="startingTimestamp"/> and <paramref name="endingTimestamp"/>. (These must be values returned from <see cref="M:System.Diagnostics.Stopwatch.GetTimestamp"/>.)
            </summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.AllowNullAttribute">
            <summary>Specifies that null is allowed as an input even if the corresponding type disallows it.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.NotNullAttribute">
            <summary>Specifies that an output will not be null even if the corresponding type allows it.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute">
            <summary>Specifies that when a method returns <see cref="P:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute.ReturnValue"/>, the parameter will not be null even if the corresponding type allows it.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute.#ctor(System.Boolean)">
            <summary>Initializes the attribute with the specified return value condition.</summary>
            <param name="returnValue">
            The return value condition. If the method returns this value, the associated parameter will not be null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute.ReturnValue">
            <summary>Gets the return value condition.</summary>
        </member>
    </members>
</doc>
