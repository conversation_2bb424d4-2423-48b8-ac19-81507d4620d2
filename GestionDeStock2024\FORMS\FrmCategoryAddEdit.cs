using System;
using System.Drawing;
using System.Windows.Forms;

namespace GestionDeStock2024.FORMS
{
    /// <summary>
    /// Formulaire d'ajout/modification de catégorie
    /// </summary>
    public partial class FrmCategoryAddEdit : Form
    {
        private TextBox txtCode, txtNom, txtDescription;
        private ComboBox cmbCategorieParent, cmbStatut;
        private Button btnSave, btnCancel;
        private bool isEditMode = false;
        private string editCode;

        public FrmCategoryAddEdit()
        {
            InitializeComponent();
            InitializeForm();
        }

        public FrmCategoryAddEdit(string code) : this()
        {
            isEditMode = true;
            editCode = code;
            LoadCategoryData();
        }

        private void InitializeForm()
        {
            this.Text = isEditMode ? "Modifier la catégorie" : "Ajouter une catégorie";
            this.Size = new Size(500, 350);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            CreateLayout();
            LoadParentCategories();
        }

        private void CreateLayout()
        {
            var mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20),
                BackColor = Color.FromArgb(248, 249, 250)
            };
            this.Controls.Add(mainPanel);

            // Titre
            var titleLabel = new Label
            {
                Text = isEditMode ? "✏️ Modifier la catégorie" : "➕ Ajouter une catégorie",
                Font = new Font("Segoe UI", 16, FontStyle.Bold),
                ForeColor = Color.FromArgb(33, 37, 41),
                AutoSize = true,
                Location = new Point(0, 0)
            };
            mainPanel.Controls.Add(titleLabel);

            // Code
            var lblCode = new Label
            {
                Text = "Code *:",
                Location = new Point(0, 50),
                Size = new Size(120, 20),
                Font = new Font("Segoe UI", 10, FontStyle.Bold)
            };
            mainPanel.Controls.Add(lblCode);

            txtCode = new TextBox
            {
                Location = new Point(130, 47),
                Size = new Size(300, 25),
                Font = new Font("Segoe UI", 10),
                MaxLength = 20
            };
            mainPanel.Controls.Add(txtCode);

            // Nom
            var lblNom = new Label
            {
                Text = "Nom *:",
                Location = new Point(0, 85),
                Size = new Size(120, 20),
                Font = new Font("Segoe UI", 10, FontStyle.Bold)
            };
            mainPanel.Controls.Add(lblNom);

            txtNom = new TextBox
            {
                Location = new Point(130, 82),
                Size = new Size(300, 25),
                Font = new Font("Segoe UI", 10),
                MaxLength = 100
            };
            mainPanel.Controls.Add(txtNom);

            // Catégorie parent
            var lblCategorieParent = new Label
            {
                Text = "Catégorie parent:",
                Location = new Point(0, 120),
                Size = new Size(120, 20),
                Font = new Font("Segoe UI", 10, FontStyle.Bold)
            };
            mainPanel.Controls.Add(lblCategorieParent);

            cmbCategorieParent = new ComboBox
            {
                Location = new Point(130, 117),
                Size = new Size(300, 25),
                Font = new Font("Segoe UI", 10),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            mainPanel.Controls.Add(cmbCategorieParent);

            // Description
            var lblDescription = new Label
            {
                Text = "Description:",
                Location = new Point(0, 155),
                Size = new Size(120, 20),
                Font = new Font("Segoe UI", 10, FontStyle.Bold)
            };
            mainPanel.Controls.Add(lblDescription);

            txtDescription = new TextBox
            {
                Location = new Point(130, 152),
                Size = new Size(300, 60),
                Font = new Font("Segoe UI", 10),
                Multiline = true,
                MaxLength = 500
            };
            mainPanel.Controls.Add(txtDescription);

            // Statut
            var lblStatut = new Label
            {
                Text = "Statut *:",
                Location = new Point(0, 225),
                Size = new Size(120, 20),
                Font = new Font("Segoe UI", 10, FontStyle.Bold)
            };
            mainPanel.Controls.Add(lblStatut);

            cmbStatut = new ComboBox
            {
                Location = new Point(130, 222),
                Size = new Size(150, 25),
                Font = new Font("Segoe UI", 10),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            cmbStatut.Items.AddRange(new[] { "Actif", "Inactif" });
            cmbStatut.SelectedIndex = 0;
            mainPanel.Controls.Add(cmbStatut);

            // Boutons
            btnSave = new Button
            {
                Text = "💾 Enregistrer",
                Location = new Point(230, 265),
                Size = new Size(120, 35),
                Font = new Font("Segoe UI", 10, FontStyle.Bold),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                FlatAppearance = { BorderSize = 0 },
                Cursor = Cursors.Hand
            };
            btnSave.Click += BtnSave_Click;
            mainPanel.Controls.Add(btnSave);

            btnCancel = new Button
            {
                Text = "❌ Annuler",
                Location = new Point(360, 265),
                Size = new Size(100, 35),
                Font = new Font("Segoe UI", 10),
                BackColor = Color.FromArgb(108, 117, 125),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                FlatAppearance = { BorderSize = 0 },
                Cursor = Cursors.Hand
            };
            btnCancel.Click += BtnCancel_Click;
            mainPanel.Controls.Add(btnCancel);

            // Note obligatoire
            var lblNote = new Label
            {
                Text = "* Champs obligatoires",
                Location = new Point(0, 310),
                Size = new Size(200, 15),
                Font = new Font("Segoe UI", 8, FontStyle.Italic),
                ForeColor = Color.FromArgb(108, 117, 125)
            };
            mainPanel.Controls.Add(lblNote);
        }

        private void LoadParentCategories()
        {
            cmbCategorieParent.Items.Clear();
            cmbCategorieParent.Items.Add("(Aucune - Catégorie racine)");
            
            // Ajouter les catégories parentes possibles
            var parentCategories = new[]
            {
                "Alimentaire",
                "Boissons", 
                "Hygiène",
                "Électronique",
                "Textile",
                "Maison"
            };
            
            foreach (var category in parentCategories)
            {
                cmbCategorieParent.Items.Add(category);
            }
            
            cmbCategorieParent.SelectedIndex = 0;
        }

        private void LoadCategoryData()
        {
            if (isEditMode && !string.IsNullOrEmpty(editCode))
            {
                txtCode.Text = editCode;
                txtCode.ReadOnly = true; // Le code ne peut pas être modifié
                
                // Données fictives pour la démonstration
                switch (editCode)
                {
                    case "ALI":
                        txtNom.Text = "Alimentaire";
                        txtDescription.Text = "Produits alimentaires de base";
                        cmbCategorieParent.SelectedIndex = 0; // Aucune
                        break;
                    case "ALI-LAI":
                        txtNom.Text = "Produits laitiers";
                        txtDescription.Text = "Lait, fromage, yaourt";
                        cmbCategorieParent.SelectedItem = "Alimentaire";
                        break;
                    case "BOI":
                        txtNom.Text = "Boissons";
                        txtDescription.Text = "Boissons diverses";
                        cmbCategorieParent.SelectedIndex = 0; // Aucune
                        break;
                    default:
                        txtNom.Text = "Catégorie exemple";
                        txtDescription.Text = "Description exemple";
                        cmbCategorieParent.SelectedIndex = 0;
                        break;
                }
                cmbStatut.SelectedItem = "Actif";
            }
        }

        private bool ValidateForm()
        {
            if (string.IsNullOrWhiteSpace(txtCode.Text))
            {
                MessageBox.Show("Le code est obligatoire", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtCode.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtNom.Text))
            {
                MessageBox.Show("Le nom est obligatoire", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtNom.Focus();
                return false;
            }

            return true;
        }

        private void BtnSave_Click(object sender, EventArgs e)
        {
            if (ValidateForm())
            {
                // Ici, vous ajouteriez la logique pour sauvegarder en base de données
                MessageBox.Show(
                    isEditMode ? "Catégorie modifiée avec succès" : "Catégorie ajoutée avec succès",
                    "Succès",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private void FrmCategoryAddEdit_Load(object sender, EventArgs e)
        {
            if (!isEditMode)
            {
                txtCode.Focus();
            }
            else
            {
                txtNom.Focus();
            }
        }
    }
}
