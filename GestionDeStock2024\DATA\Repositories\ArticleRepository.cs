using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Dapper;
using GestionDeStock2024.MODELS;

namespace GestionDeStock2024.DATA.Repositories
{
    /// <summary>
    /// Repository pour la gestion des articles
    /// </summary>
    public interface IArticleRepository : IMasterRepository<Article>
    {
        Task<IEnumerable<Article>> GetArticlesWithLowStockAsync();
        Task<IEnumerable<Article>> GetArticlesOutOfStockAsync();
        Task<IEnumerable<Article>> GetArticlesByCategoryAsync(int categoryId);
        Task<IEnumerable<Article>> GetArticlesExpiringAsync(int daysFromNow = 30);
        Task<Article> GetByBarcodeAsync(string barcode);
        Task<bool> UpdateStockAsync(int articleId, decimal newStock, int userId, string motif = "Ajustement manuel");
        Task<bool> UpdatePMPAsync(int articleId, decimal newPMP);
        Task<decimal> CalculateStockValueAsync();
        Task<IEnumerable<Article>> GetArticlesForInventoryAsync();
        Task<bool> ReserveStockAsync(int articleId, decimal quantity);
        Task<bool> ReleaseReservedStockAsync(int articleId, decimal quantity);
    }

    public class ArticleRepository : BaseRepository<Article>, IArticleRepository
    {
        public ArticleRepository() : base("articles", "id") { }

        /// <summary>
        /// Obtient un article avec ses relations (catégorie, unité de mesure, codes-barres)
        /// </summary>
        public override async Task<Article> GetWithRelationsAsync(int id, params string[] includes)
        {
            using var connection = await _dbConnection.GetConnectionAsync();
            
            var sql = @"
                SELECT a.*, c.*, u.*, cb.*
                FROM articles a
                LEFT JOIN categories c ON a.categorie_id = c.id
                LEFT JOIN unites_mesure u ON a.unite_mesure_id = u.id
                LEFT JOIN codes_barres cb ON a.id = cb.article_id
                WHERE a.id = @Id";

            var articleDict = new Dictionary<int, Article>();

            var result = await connection.QueryAsync<Article, Categorie, UniteMesure, CodeBarre, Article>(
                sql,
                (article, categorie, uniteMesure, codeBarre) =>
                {
                    if (!articleDict.TryGetValue(article.Id, out var articleEntry))
                    {
                        articleEntry = article;
                        articleEntry.Categorie = categorie;
                        articleEntry.UniteMesure = uniteMesure;
                        articleEntry.CodesBarres = new List<CodeBarre>();
                        articleDict.Add(article.Id, articleEntry);
                    }

                    if (codeBarre != null)
                    {
                        articleEntry.CodesBarres.Add(codeBarre);
                    }

                    return articleEntry;
                },
                new { Id = id },
                splitOn: "id,id,id"
            );

            return articleDict.Values.FirstOrDefault();
        }

        /// <summary>
        /// Obtient les articles avec un stock faible
        /// </summary>
        public async Task<IEnumerable<Article>> GetArticlesWithLowStockAsync()
        {
            using var connection = await _dbConnection.GetConnectionAsync();
            
            var sql = @"
                SELECT a.*, c.nom_categorie as CategorieName, u.nom_unite as UniteName
                FROM articles a
                LEFT JOIN categories c ON a.categorie_id = c.id
                LEFT JOIN unites_mesure u ON a.unite_mesure_id = u.id
                WHERE a.stock_actuel <= a.stock_minimum 
                AND a.stock_actuel > 0 
                AND a.statut = 'Actif'
                ORDER BY (a.stock_actuel / NULLIF(a.stock_minimum, 0)) ASC";

            return await connection.QueryAsync<Article>(sql);
        }

        /// <summary>
        /// Obtient les articles en rupture de stock
        /// </summary>
        public async Task<IEnumerable<Article>> GetArticlesOutOfStockAsync()
        {
            using var connection = await _dbConnection.GetConnectionAsync();
            
            var sql = @"
                SELECT a.*, c.nom_categorie as CategorieName, u.nom_unite as UniteName
                FROM articles a
                LEFT JOIN categories c ON a.categorie_id = c.id
                LEFT JOIN unites_mesure u ON a.unite_mesure_id = u.id
                WHERE a.stock_actuel <= 0 
                AND a.statut = 'Actif'
                ORDER BY a.nom_article";

            return await connection.QueryAsync<Article>(sql);
        }

        /// <summary>
        /// Obtient les articles par catégorie
        /// </summary>
        public async Task<IEnumerable<Article>> GetArticlesByCategoryAsync(int categoryId)
        {
            using var connection = await _dbConnection.GetConnectionAsync();
            
            var sql = @"
                SELECT a.*, c.nom_categorie as CategorieName, u.nom_unite as UniteName
                FROM articles a
                LEFT JOIN categories c ON a.categorie_id = c.id
                LEFT JOIN unites_mesure u ON a.unite_mesure_id = u.id
                WHERE a.categorie_id = @CategoryId 
                AND a.statut = 'Actif'
                ORDER BY a.nom_article";

            return await connection.QueryAsync<Article>(sql, new { CategoryId = categoryId });
        }

        /// <summary>
        /// Obtient les articles qui expirent bientôt
        /// </summary>
        public async Task<IEnumerable<Article>> GetArticlesExpiringAsync(int daysFromNow = 30)
        {
            using var connection = await _dbConnection.GetConnectionAsync();
            
            var sql = @"
                SELECT a.*, c.nom_categorie as CategorieName, u.nom_unite as UniteName
                FROM articles a
                LEFT JOIN categories c ON a.categorie_id = c.id
                LEFT JOIN unites_mesure u ON a.unite_mesure_id = u.id
                WHERE a.date_expiration IS NOT NULL 
                AND a.date_expiration <= DATE_ADD(CURDATE(), INTERVAL @Days DAY)
                AND a.date_expiration > CURDATE()
                AND a.statut = 'Actif'
                ORDER BY a.date_expiration ASC";

            return await connection.QueryAsync<Article>(sql, new { Days = daysFromNow });
        }

        /// <summary>
        /// Obtient un article par son code-barre
        /// </summary>
        public async Task<Article> GetByBarcodeAsync(string barcode)
        {
            using var connection = await _dbConnection.GetConnectionAsync();
            
            var sql = @"
                SELECT a.*, c.*, u.*
                FROM articles a
                LEFT JOIN categories c ON a.categorie_id = c.id
                LEFT JOIN unites_mesure u ON a.unite_mesure_id = u.id
                INNER JOIN codes_barres cb ON a.id = cb.article_id
                WHERE cb.code_barre = @Barcode 
                AND cb.statut = 'Actif'
                AND a.statut = 'Actif'";

            return await connection.QueryFirstOrDefaultAsync<Article>(sql, new { Barcode = barcode });
        }

        /// <summary>
        /// Met à jour le stock d'un article avec traçabilité
        /// </summary>
        public async Task<bool> UpdateStockAsync(int articleId, decimal newStock, int userId, string motif = "Ajustement manuel")
        {
            using var connection = await _dbConnection.GetConnectionAsync();
            using var transaction = connection.BeginTransaction();

            try
            {
                // Récupérer le stock actuel
                var currentStock = await connection.QuerySingleAsync<decimal>(
                    "SELECT stock_actuel FROM articles WHERE id = @Id",
                    new { Id = articleId },
                    transaction);

                // Mettre à jour le stock
                var updateResult = await connection.ExecuteAsync(
                    "UPDATE articles SET stock_actuel = @NewStock, updated_at = @UpdatedAt WHERE id = @Id",
                    new { NewStock = newStock, UpdatedAt = DateTime.Now, Id = articleId },
                    transaction);

                if (updateResult > 0)
                {
                    // Enregistrer le mouvement de stock
                    await connection.ExecuteAsync(@"
                        INSERT INTO mouvements_stock 
                        (article_id, type_mouvement, reference_document, type_document, 
                         quantite_avant, quantite_mouvement, quantite_apres, 
                         utilisateur_id, date_mouvement, heure_mouvement, motif)
                        VALUES 
                        (@ArticleId, @TypeMouvement, @Reference, @TypeDocument,
                         @QuantiteAvant, @QuantiteMouvement, @QuantiteApres,
                         @UserId, @DateMouvement, @HeureMouvement, @Motif)",
                        new
                        {
                            ArticleId = articleId,
                            TypeMouvement = "Ajustement",
                            Reference = $"ADJ-{DateTime.Now:yyyyMMddHHmmss}",
                            TypeDocument = "Ajustement_manuel",
                            QuantiteAvant = currentStock,
                            QuantiteMouvement = newStock - currentStock,
                            QuantiteApres = newStock,
                            UserId = userId,
                            DateMouvement = DateTime.Today,
                            HeureMouvement = DateTime.Now.TimeOfDay,
                            Motif = motif
                        },
                        transaction);
                }

                transaction.Commit();
                return updateResult > 0;
            }
            catch
            {
                transaction.Rollback();
                return false;
            }
        }

        /// <summary>
        /// Met à jour le Prix Moyen Pondéré d'un article
        /// </summary>
        public async Task<bool> UpdatePMPAsync(int articleId, decimal newPMP)
        {
            using var connection = await _dbConnection.GetConnectionAsync();
            
            var sql = "UPDATE articles SET pmp = @NewPMP, updated_at = @UpdatedAt WHERE id = @Id";
            var rowsAffected = await connection.ExecuteAsync(sql, 
                new { NewPMP = newPMP, UpdatedAt = DateTime.Now, Id = articleId });
            
            return rowsAffected > 0;
        }

        /// <summary>
        /// Calcule la valeur totale du stock
        /// </summary>
        public async Task<decimal> CalculateStockValueAsync()
        {
            using var connection = await _dbConnection.GetConnectionAsync();
            
            var sql = "SELECT COALESCE(SUM(stock_actuel * pmp), 0) FROM articles WHERE statut = 'Actif'";
            return await connection.QuerySingleAsync<decimal>(sql);
        }

        /// <summary>
        /// Obtient les articles pour inventaire
        /// </summary>
        public async Task<IEnumerable<Article>> GetArticlesForInventoryAsync()
        {
            using var connection = await _dbConnection.GetConnectionAsync();
            
            var sql = @"
                SELECT a.id, a.code_article as Code, a.nom_article as NomArticle, 
                       a.stock_actuel as StockActuel, a.pmp as PMP,
                       c.nom_categorie as CategorieName, u.abreviation as UniteAbreviation
                FROM articles a
                LEFT JOIN categories c ON a.categorie_id = c.id
                LEFT JOIN unites_mesure u ON a.unite_mesure_id = u.id
                WHERE a.statut = 'Actif'
                ORDER BY c.nom_categorie, a.nom_article";

            return await connection.QueryAsync<Article>(sql);
        }

        /// <summary>
        /// Réserve du stock pour un article
        /// </summary>
        public async Task<bool> ReserveStockAsync(int articleId, decimal quantity)
        {
            using var connection = await _dbConnection.GetConnectionAsync();
            
            var sql = @"
                UPDATE articles 
                SET stock_reserve = stock_reserve + @Quantity, updated_at = @UpdatedAt
                WHERE id = @Id 
                AND (stock_actuel - stock_reserve) >= @Quantity";
            
            var rowsAffected = await connection.ExecuteAsync(sql, 
                new { Quantity = quantity, UpdatedAt = DateTime.Now, Id = articleId });
            
            return rowsAffected > 0;
        }

        /// <summary>
        /// Libère du stock réservé pour un article
        /// </summary>
        public async Task<bool> ReleaseReservedStockAsync(int articleId, decimal quantity)
        {
            using var connection = await _dbConnection.GetConnectionAsync();
            
            var sql = @"
                UPDATE articles 
                SET stock_reserve = GREATEST(0, stock_reserve - @Quantity), updated_at = @UpdatedAt
                WHERE id = @Id";
            
            var rowsAffected = await connection.ExecuteAsync(sql, 
                new { Quantity = quantity, UpdatedAt = DateTime.Now, Id = articleId });
            
            return rowsAffected > 0;
        }

        /// <summary>
        /// Recherche avancée d'articles
        /// </summary>
        public override async Task<IEnumerable<Article>> SearchAsync(string searchTerm, string[] searchFields = null)
        {
            if (string.IsNullOrEmpty(searchTerm)) return await GetAllAsync();

            using var connection = await _dbConnection.GetConnectionAsync();
            
            var sql = @"
                SELECT a.*, c.nom_categorie as CategorieName, u.nom_unite as UniteName
                FROM articles a
                LEFT JOIN categories c ON a.categorie_id = c.id
                LEFT JOIN unites_mesure u ON a.unite_mesure_id = u.id
                LEFT JOIN codes_barres cb ON a.id = cb.article_id
                WHERE (a.nom_article LIKE @SearchTerm 
                   OR a.code_article LIKE @SearchTerm 
                   OR a.description LIKE @SearchTerm
                   OR cb.code_barre = @ExactTerm)
                AND a.statut = 'Actif'
                GROUP BY a.id
                ORDER BY a.nom_article";

            return await connection.QueryAsync<Article>(sql, 
                new { SearchTerm = $"%{searchTerm}%", ExactTerm = searchTerm });
        }
    }
}
