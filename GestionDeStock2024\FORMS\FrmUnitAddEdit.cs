using System;
using System.Drawing;
using System.Windows.Forms;

namespace GestionDeStock2024.FORMS
{
    /// <summary>
    /// Formulaire d'ajout/modification d'unité de mesure
    /// </summary>
    public partial class FrmUnitAddEdit : Form
    {
        private bool isEditMode = false;
        private string editCode;

        public FrmUnitAddEdit()
        {
            InitializeComponent();
            cmbStatut.SelectedIndex = 0; // Actif par défaut
        }

        public FrmUnitAddEdit(string code) : this()
        {
            isEditMode = true;
            editCode = code;
            this.Text = "Modifier l'unité";
            titleLabel.Text = "✏️ Modifier l'unité";
            LoadUnitData();
        }



        private void LoadUnitData()
        {
            if (isEditMode && !string.IsNullOrEmpty(editCode))
            {
                // Simuler le chargement des données
                txtCode.Text = editCode;
                txtCode.ReadOnly = true; // Le code ne peut pas être modifié
                
                // Données fictives pour la démonstration
                switch (editCode)
                {
                    case "KG":
                        txtNom.Text = "Kilogramme";
                        txtAbreviation.Text = "kg";
                        txtDescription.Text = "Unité de poids";
                        cmbTypeUnite.SelectedItem = "Poids";
                        break;
                    case "L":
                        txtNom.Text = "Litre";
                        txtAbreviation.Text = "l";
                        txtDescription.Text = "Unité de volume";
                        cmbTypeUnite.SelectedItem = "Volume";
                        break;
                    default:
                        txtNom.Text = "Unité exemple";
                        txtAbreviation.Text = "ex";
                        txtDescription.Text = "Description exemple";
                        cmbTypeUnite.SelectedIndex = 0;
                        break;
                }
                cmbStatut.SelectedItem = "Actif";
            }
        }

        private bool ValidateForm()
        {
            if (string.IsNullOrWhiteSpace(txtCode.Text))
            {
                MessageBox.Show("Le code est obligatoire", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtCode.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtNom.Text))
            {
                MessageBox.Show("Le nom est obligatoire", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtNom.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtAbreviation.Text))
            {
                MessageBox.Show("L'abréviation est obligatoire", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtAbreviation.Focus();
                return false;
            }

            if (cmbTypeUnite.SelectedIndex == -1)
            {
                MessageBox.Show("Le type d'unité est obligatoire", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                cmbTypeUnite.Focus();
                return false;
            }

            return true;
        }

        private void BtnSave_Click(object sender, EventArgs e)
        {
            if (ValidateForm())
            {
                // Ici, vous ajouteriez la logique pour sauvegarder en base de données
                MessageBox.Show(
                    isEditMode ? "Unité modifiée avec succès" : "Unité ajoutée avec succès",
                    "Succès",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private void FrmUnitAddEdit_Load(object sender, EventArgs e)
        {
            if (!isEditMode)
            {
                txtCode.Focus();
            }
            else
            {
                txtNom.Focus();
            }
        }
    }
}
