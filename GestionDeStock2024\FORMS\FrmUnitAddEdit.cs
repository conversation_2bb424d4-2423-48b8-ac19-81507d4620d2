using System;
using System.Drawing;
using System.Windows.Forms;

namespace GestionDeStock2024.FORMS
{
    /// <summary>
    /// Formulaire d'ajout/modification d'unité de mesure
    /// </summary>
    public partial class FrmUnitAddEdit : Form
    {
        private TextBox txtCode, txtNom, txtAbreviation, txtDescription;
        private ComboBox cmbTypeUnite, cmbStatut;
        private Button btnSave, btnCancel;
        private bool isEditMode = false;
        private string editCode;

        public FrmUnitAddEdit()
        {
            InitializeComponent();
            InitializeForm();
        }

        public FrmUnitAddEdit(string code) : this()
        {
            isEditMode = true;
            editCode = code;
            LoadUnitData();
        }

        private void InitializeForm()
        {
            this.Text = isEditMode ? "Modifier l'unité" : "Ajouter une unité";
            this.Size = new Size(500, 400);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            CreateLayout();
        }

        private void CreateLayout()
        {
            var mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20),
                BackColor = Color.FromArgb(248, 249, 250)
            };
            this.Controls.Add(mainPanel);

            // Titre
            var titleLabel = new Label
            {
                Text = isEditMode ? "✏️ Modifier l'unité" : "➕ Ajouter une unité",
                Font = new Font("Segoe UI", 16, FontStyle.Bold),
                ForeColor = Color.FromArgb(33, 37, 41),
                AutoSize = true,
                Location = new Point(0, 0)
            };
            mainPanel.Controls.Add(titleLabel);

            // Code
            var lblCode = new Label
            {
                Text = "Code *:",
                Location = new Point(0, 50),
                Size = new Size(100, 20),
                Font = new Font("Segoe UI", 10, FontStyle.Bold)
            };
            mainPanel.Controls.Add(lblCode);

            txtCode = new TextBox
            {
                Location = new Point(120, 47),
                Size = new Size(300, 25),
                Font = new Font("Segoe UI", 10),
                MaxLength = 10
            };
            mainPanel.Controls.Add(txtCode);

            // Nom
            var lblNom = new Label
            {
                Text = "Nom *:",
                Location = new Point(0, 85),
                Size = new Size(100, 20),
                Font = new Font("Segoe UI", 10, FontStyle.Bold)
            };
            mainPanel.Controls.Add(lblNom);

            txtNom = new TextBox
            {
                Location = new Point(120, 82),
                Size = new Size(300, 25),
                Font = new Font("Segoe UI", 10),
                MaxLength = 100
            };
            mainPanel.Controls.Add(txtNom);

            // Abréviation
            var lblAbreviation = new Label
            {
                Text = "Abréviation *:",
                Location = new Point(0, 120),
                Size = new Size(100, 20),
                Font = new Font("Segoe UI", 10, FontStyle.Bold)
            };
            mainPanel.Controls.Add(lblAbreviation);

            txtAbreviation = new TextBox
            {
                Location = new Point(120, 117),
                Size = new Size(150, 25),
                Font = new Font("Segoe UI", 10),
                MaxLength = 10
            };
            mainPanel.Controls.Add(txtAbreviation);

            // Type d'unité
            var lblTypeUnite = new Label
            {
                Text = "Type *:",
                Location = new Point(0, 155),
                Size = new Size(100, 20),
                Font = new Font("Segoe UI", 10, FontStyle.Bold)
            };
            mainPanel.Controls.Add(lblTypeUnite);

            cmbTypeUnite = new ComboBox
            {
                Location = new Point(120, 152),
                Size = new Size(200, 25),
                Font = new Font("Segoe UI", 10),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            cmbTypeUnite.Items.AddRange(new[] { "Poids", "Volume", "Longueur", "Surface", "Quantité", "Emballage", "Temps" });
            mainPanel.Controls.Add(cmbTypeUnite);

            // Description
            var lblDescription = new Label
            {
                Text = "Description:",
                Location = new Point(0, 190),
                Size = new Size(100, 20),
                Font = new Font("Segoe UI", 10, FontStyle.Bold)
            };
            mainPanel.Controls.Add(lblDescription);

            txtDescription = new TextBox
            {
                Location = new Point(120, 187),
                Size = new Size(300, 60),
                Font = new Font("Segoe UI", 10),
                Multiline = true,
                MaxLength = 500
            };
            mainPanel.Controls.Add(txtDescription);

            // Statut
            var lblStatut = new Label
            {
                Text = "Statut *:",
                Location = new Point(0, 260),
                Size = new Size(100, 20),
                Font = new Font("Segoe UI", 10, FontStyle.Bold)
            };
            mainPanel.Controls.Add(lblStatut);

            cmbStatut = new ComboBox
            {
                Location = new Point(120, 257),
                Size = new Size(150, 25),
                Font = new Font("Segoe UI", 10),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            cmbStatut.Items.AddRange(new[] { "Actif", "Inactif" });
            cmbStatut.SelectedIndex = 0;
            mainPanel.Controls.Add(cmbStatut);

            // Boutons
            btnSave = new Button
            {
                Text = "💾 Enregistrer",
                Location = new Point(220, 300),
                Size = new Size(120, 35),
                Font = new Font("Segoe UI", 10, FontStyle.Bold),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                FlatAppearance = { BorderSize = 0 },
                Cursor = Cursors.Hand
            };
            btnSave.Click += BtnSave_Click;
            mainPanel.Controls.Add(btnSave);

            btnCancel = new Button
            {
                Text = "❌ Annuler",
                Location = new Point(350, 300),
                Size = new Size(100, 35),
                Font = new Font("Segoe UI", 10),
                BackColor = Color.FromArgb(108, 117, 125),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                FlatAppearance = { BorderSize = 0 },
                Cursor = Cursors.Hand
            };
            btnCancel.Click += BtnCancel_Click;
            mainPanel.Controls.Add(btnCancel);

            // Note obligatoire
            var lblNote = new Label
            {
                Text = "* Champs obligatoires",
                Location = new Point(0, 345),
                Size = new Size(200, 15),
                Font = new Font("Segoe UI", 8, FontStyle.Italic),
                ForeColor = Color.FromArgb(108, 117, 125)
            };
            mainPanel.Controls.Add(lblNote);
        }

        private void LoadUnitData()
        {
            if (isEditMode && !string.IsNullOrEmpty(editCode))
            {
                // Simuler le chargement des données
                txtCode.Text = editCode;
                txtCode.ReadOnly = true; // Le code ne peut pas être modifié
                
                // Données fictives pour la démonstration
                switch (editCode)
                {
                    case "KG":
                        txtNom.Text = "Kilogramme";
                        txtAbreviation.Text = "kg";
                        txtDescription.Text = "Unité de poids";
                        cmbTypeUnite.SelectedItem = "Poids";
                        break;
                    case "L":
                        txtNom.Text = "Litre";
                        txtAbreviation.Text = "l";
                        txtDescription.Text = "Unité de volume";
                        cmbTypeUnite.SelectedItem = "Volume";
                        break;
                    default:
                        txtNom.Text = "Unité exemple";
                        txtAbreviation.Text = "ex";
                        txtDescription.Text = "Description exemple";
                        cmbTypeUnite.SelectedIndex = 0;
                        break;
                }
                cmbStatut.SelectedItem = "Actif";
            }
        }

        private bool ValidateForm()
        {
            if (string.IsNullOrWhiteSpace(txtCode.Text))
            {
                MessageBox.Show("Le code est obligatoire", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtCode.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtNom.Text))
            {
                MessageBox.Show("Le nom est obligatoire", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtNom.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtAbreviation.Text))
            {
                MessageBox.Show("L'abréviation est obligatoire", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtAbreviation.Focus();
                return false;
            }

            if (cmbTypeUnite.SelectedIndex == -1)
            {
                MessageBox.Show("Le type d'unité est obligatoire", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                cmbTypeUnite.Focus();
                return false;
            }

            return true;
        }

        private void BtnSave_Click(object sender, EventArgs e)
        {
            if (ValidateForm())
            {
                // Ici, vous ajouteriez la logique pour sauvegarder en base de données
                MessageBox.Show(
                    isEditMode ? "Unité modifiée avec succès" : "Unité ajoutée avec succès",
                    "Succès",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private void FrmUnitAddEdit_Load(object sender, EventArgs e)
        {
            if (!isEditMode)
            {
                txtCode.Focus();
            }
            else
            {
                txtNom.Focus();
            }
        }
    }
}
