using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dapper;
using GestionDeStock2024.MODELS;

namespace GestionDeStock2024.DATA.Repositories
{
    /// <summary>
    /// Repository pour la gestion des factures de vente
    /// </summary>
    public interface IFactureVenteRepository : IMasterRepository<FactureVente>
    {
        Task<IEnumerable<FactureVente>> GetFacturesByClientAsync(int clientId);
        Task<IEnumerable<FactureVente>> GetFacturesUnpaidAsync();
        Task<IEnumerable<FactureVente>> GetFacturesByDateRangeAsync(DateTime dateDebut, DateTime dateFin);
        Task<FactureVente> GetWithDetailsAsync(int factureId);
        Task<bool> AddWithDetailsAsync(FactureVente facture, IEnumerable<DetailFactureVente> details);
        Task<bool> UpdateWithDetailsAsync(FactureVente facture, IEnumerable<DetailFactureVente> details);
        Task<decimal> GetChiffreAffairesAsync(DateTime? dateDebut = null, DateTime? dateFin = null);
        Task<IEnumerable<object>> GetVentesStatisticsAsync(DateTime dateDebut, DateTime dateFin);
        Task<string> GenerateNextFactureNumberAsync();
        Task<bool> ValidateFactureAsync(int factureId, int userId);
        Task<bool> CancelFactureAsync(int factureId, int userId, string motif);
    }

    public class FactureVenteRepository : BaseRepository<FactureVente>, IFactureVenteRepository
    {
        public FactureVenteRepository() : base("factures_vente", "id") { }

        /// <summary>
        /// Obtient une facture avec tous ses détails
        /// </summary>
        public async Task<FactureVente> GetWithDetailsAsync(int factureId)
        {
            using var connection = await _dbConnection.GetConnectionAsync();
            
            var sql = @"
                SELECT fv.*, c.*, u.*, ca.*, mp.*
                FROM factures_vente fv
                LEFT JOIN clients c ON fv.client_id = c.id
                LEFT JOIN utilisateurs u ON fv.utilisateur_id = u.id
                LEFT JOIN caisses ca ON fv.caisse_id = ca.id
                LEFT JOIN modes_paiement mp ON fv.mode_paiement_id = mp.id
                WHERE fv.id = @FactureId";

            var facture = await connection.QueryFirstOrDefaultAsync<FactureVente>(sql, new { FactureId = factureId });

            if (facture != null)
            {
                // Récupérer les détails de la facture
                var detailsSql = @"
                    SELECT dfv.*, a.nom_article, a.code_article, u.abreviation as UniteAbreviation
                    FROM details_facture_vente dfv
                    INNER JOIN articles a ON dfv.article_id = a.id
                    LEFT JOIN unites_mesure u ON a.unite_mesure_id = u.id
                    WHERE dfv.facture_vente_id = @FactureId
                    ORDER BY dfv.id";

                facture.DetailsFacture = (await connection.QueryAsync<DetailFactureVente>(detailsSql, new { FactureId = factureId })).ToList();
            }

            return facture;
        }

        /// <summary>
        /// Obtient les factures d'un client
        /// </summary>
        public async Task<IEnumerable<FactureVente>> GetFacturesByClientAsync(int clientId)
        {
            using var connection = await _dbConnection.GetConnectionAsync();
            
            var sql = @"
                SELECT fv.*, c.nom_client, c.prenom_client, c.raison_sociale,
                       u.nom_complet as UtilisateurNom
                FROM factures_vente fv
                LEFT JOIN clients c ON fv.client_id = c.id
                LEFT JOIN utilisateurs u ON fv.utilisateur_id = u.id
                WHERE fv.client_id = @ClientId
                ORDER BY fv.date_facture DESC, fv.heure_facture DESC";

            return await connection.QueryAsync<FactureVente>(sql, new { ClientId = clientId });
        }

        /// <summary>
        /// Obtient les factures non payées
        /// </summary>
        public async Task<IEnumerable<FactureVente>> GetFacturesUnpaidAsync()
        {
            using var connection = await _dbConnection.GetConnectionAsync();
            
            var sql = @"
                SELECT fv.*, c.nom_client, c.prenom_client, c.raison_sociale,
                       DATEDIFF(CURDATE(), fv.date_facture) as JoursRetard
                FROM factures_vente fv
                LEFT JOIN clients c ON fv.client_id = c.id
                WHERE fv.statut_paiement IN ('Non_payé', 'Partiellement_payé')
                AND fv.statut_facture = 'Validée'
                ORDER BY fv.date_facture ASC";

            return await connection.QueryAsync<FactureVente>(sql);
        }

        /// <summary>
        /// Obtient les factures par période
        /// </summary>
        public async Task<IEnumerable<FactureVente>> GetFacturesByDateRangeAsync(DateTime dateDebut, DateTime dateFin)
        {
            using var connection = await _dbConnection.GetConnectionAsync();
            
            var sql = @"
                SELECT fv.*, c.nom_client, c.prenom_client, c.raison_sociale,
                       u.nom_complet as UtilisateurNom
                FROM factures_vente fv
                LEFT JOIN clients c ON fv.client_id = c.id
                LEFT JOIN utilisateurs u ON fv.utilisateur_id = u.id
                WHERE fv.date_facture BETWEEN @DateDebut AND @DateFin
                AND fv.statut_facture = 'Validée'
                ORDER BY fv.date_facture DESC, fv.heure_facture DESC";

            return await connection.QueryAsync<FactureVente>(sql, new { DateDebut = dateDebut, DateFin = dateFin });
        }

        /// <summary>
        /// Ajoute une facture avec ses détails dans une transaction
        /// </summary>
        public async Task<bool> AddWithDetailsAsync(FactureVente facture, IEnumerable<DetailFactureVente> details)
        {
            using var connection = await _dbConnection.GetConnectionAsync();
            using var transaction = connection.BeginTransaction();

            try
            {
                // Générer le numéro de facture si nécessaire
                if (string.IsNullOrEmpty(facture.NumeroFacture))
                {
                    facture.NumeroFacture = await GenerateNextFactureNumberAsync();
                }

                // Insérer la facture
                var factureId = await connection.QuerySingleAsync<int>(@"
                    INSERT INTO factures_vente 
                    (numero_facture, type_facture, client_id, utilisateur_id, caisse_id,
                     date_facture, heure_facture, montant_ht, montant_tva, montant_ttc,
                     remise_globale, remise_pourcentage, timbre_fiscal, montant_total,
                     montant_paye, montant_restant, mode_paiement_id, statut_paiement,
                     statut_facture, notes, created_at, updated_at)
                    VALUES 
                    (@NumeroFacture, @TypeFacture, @ClientId, @UtilisateurId, @CaisseId,
                     @DateFacture, @HeureFacture, @MontantHT, @MontantTVA, @MontantTTC,
                     @RemiseGlobale, @RemisePourcentage, @TimbreFiscal, @MontantTotal,
                     @MontantPaye, @MontantRestant, @ModePaiementId, @StatutPaiement,
                     @StatutFacture, @Notes, @CreatedAt, @UpdatedAt);
                    SELECT LAST_INSERT_ID();", facture, transaction);

                // Insérer les détails
                foreach (var detail in details)
                {
                    detail.FactureVenteId = factureId;
                    detail.CalculerMontants();

                    await connection.ExecuteAsync(@"
                        INSERT INTO details_facture_vente 
                        (facture_vente_id, article_id, code_barre_utilise, quantite,
                         prix_unitaire_ht, prix_unitaire_ttc, remise_ligne, remise_pourcentage,
                         tva_taux, montant_ht, montant_tva, montant_ttc, created_at)
                        VALUES 
                        (@FactureVenteId, @ArticleId, @CodeBarreUtilise, @Quantite,
                         @PrixUnitaireHT, @PrixUnitaireTTC, @RemiseLigne, @RemisePourcentage,
                         @TvaTaux, @MontantHT, @MontantTVA, @MontantTTC, @CreatedAt)", detail, transaction);

                    // Mettre à jour le stock de l'article
                    await connection.ExecuteAsync(@"
                        UPDATE articles 
                        SET stock_actuel = stock_actuel - @Quantite, updated_at = @UpdatedAt
                        WHERE id = @ArticleId", 
                        new { detail.Quantite, UpdatedAt = DateTime.Now, detail.ArticleId }, transaction);

                    // Enregistrer le mouvement de stock
                    await connection.ExecuteAsync(@"
                        INSERT INTO mouvements_stock 
                        (article_id, type_mouvement, reference_document, type_document, document_id,
                         quantite_avant, quantite_mouvement, quantite_apres, prix_unitaire,
                         cout_total, utilisateur_id, date_mouvement, heure_mouvement)
                        SELECT @ArticleId, 'Sortie', @Reference, 'Facture_vente', @DocumentId,
                               a.stock_actuel + @Quantite, @Quantite, a.stock_actuel, @PrixUnitaire,
                               @CoutTotal, @UserId, @DateMouvement, @HeureMouvement
                        FROM articles a WHERE a.id = @ArticleId",
                        new
                        {
                            detail.ArticleId,
                            Reference = facture.NumeroFacture,
                            DocumentId = factureId,
                            detail.Quantite,
                            PrixUnitaire = detail.PrixUnitaireTTC,
                            CoutTotal = detail.MontantTTC,
                            UserId = facture.UtilisateurId,
                            DateMouvement = facture.DateFacture,
                            HeureMouvement = facture.HeureFacture
                        }, transaction);
                }

                // Mettre à jour le solde client si nécessaire
                if (facture.ClientId.HasValue && facture.MontantRestant > 0)
                {
                    await connection.ExecuteAsync(@"
                        UPDATE clients 
                        SET solde_actuel = solde_actuel + @Montant, updated_at = @UpdatedAt
                        WHERE id = @ClientId",
                        new { Montant = facture.MontantRestant, UpdatedAt = DateTime.Now, ClientId = facture.ClientId }, transaction);
                }

                transaction.Commit();
                return true;
            }
            catch
            {
                transaction.Rollback();
                return false;
            }
        }

        /// <summary>
        /// Met à jour une facture avec ses détails
        /// </summary>
        public async Task<bool> UpdateWithDetailsAsync(FactureVente facture, IEnumerable<DetailFactureVente> details)
        {
            using var connection = await _dbConnection.GetConnectionAsync();
            using var transaction = connection.BeginTransaction();

            try
            {
                // Mettre à jour la facture
                await connection.ExecuteAsync(@"
                    UPDATE factures_vente SET
                    type_facture = @TypeFacture, client_id = @ClientId, caisse_id = @CaisseId,
                    montant_ht = @MontantHT, montant_tva = @MontantTVA, montant_ttc = @MontantTTC,
                    remise_globale = @RemiseGlobale, remise_pourcentage = @RemisePourcentage,
                    timbre_fiscal = @TimbreFiscal, montant_total = @MontantTotal,
                    montant_paye = @MontantPaye, montant_restant = @MontantRestant,
                    mode_paiement_id = @ModePaiementId, statut_paiement = @StatutPaiement,
                    statut_facture = @StatutFacture, notes = @Notes, updated_at = @UpdatedAt
                    WHERE id = @Id", facture, transaction);

                // Supprimer les anciens détails
                await connection.ExecuteAsync(
                    "DELETE FROM details_facture_vente WHERE facture_vente_id = @FactureId",
                    new { FactureId = facture.Id }, transaction);

                // Insérer les nouveaux détails
                foreach (var detail in details)
                {
                    detail.FactureVenteId = facture.Id;
                    detail.CalculerMontants();

                    await connection.ExecuteAsync(@"
                        INSERT INTO details_facture_vente 
                        (facture_vente_id, article_id, code_barre_utilise, quantite,
                         prix_unitaire_ht, prix_unitaire_ttc, remise_ligne, remise_pourcentage,
                         tva_taux, montant_ht, montant_tva, montant_ttc, created_at)
                        VALUES 
                        (@FactureVenteId, @ArticleId, @CodeBarreUtilise, @Quantite,
                         @PrixUnitaireHT, @PrixUnitaireTTC, @RemiseLigne, @RemisePourcentage,
                         @TvaTaux, @MontantHT, @MontantTVA, @MontantTTC, @CreatedAt)", detail, transaction);
                }

                transaction.Commit();
                return true;
            }
            catch
            {
                transaction.Rollback();
                return false;
            }
        }

        /// <summary>
        /// Calcule le chiffre d'affaires
        /// </summary>
        public async Task<decimal> GetChiffreAffairesAsync(DateTime? dateDebut = null, DateTime? dateFin = null)
        {
            using var connection = await _dbConnection.GetConnectionAsync();
            
            var sql = "SELECT COALESCE(SUM(montant_total), 0) FROM factures_vente WHERE statut_facture = 'Validée'";
            object parameters = null;

            if (dateDebut.HasValue && dateFin.HasValue)
            {
                sql += " AND date_facture BETWEEN @DateDebut AND @DateFin";
                parameters = new { DateDebut = dateDebut.Value, DateFin = dateFin.Value };
            }
            else if (dateDebut.HasValue)
            {
                sql += " AND date_facture >= @DateDebut";
                parameters = new { DateDebut = dateDebut.Value };
            }
            else if (dateFin.HasValue)
            {
                sql += " AND date_facture <= @DateFin";
                parameters = new { DateFin = dateFin.Value };
            }

            return await connection.QuerySingleAsync<decimal>(sql, parameters);
        }

        /// <summary>
        /// Obtient les statistiques de ventes
        /// </summary>
        public async Task<IEnumerable<object>> GetVentesStatisticsAsync(DateTime dateDebut, DateTime dateFin)
        {
            using var connection = await _dbConnection.GetConnectionAsync();
            
            var sql = @"
                SELECT 
                    DATE(date_facture) as Date,
                    COUNT(*) as NombreFactures,
                    SUM(montant_ht) as TotalHT,
                    SUM(montant_tva) as TotalTVA,
                    SUM(montant_ttc) as TotalTTC,
                    SUM(timbre_fiscal) as TotalTimbre,
                    SUM(montant_total) as ChiffreAffaires,
                    AVG(montant_total) as MoyenneFacture
                FROM factures_vente 
                WHERE date_facture BETWEEN @DateDebut AND @DateFin
                AND statut_facture = 'Validée'
                GROUP BY DATE(date_facture)
                ORDER BY Date DESC";

            return await connection.QueryAsync(sql, new { DateDebut = dateDebut, DateFin = dateFin });
        }

        /// <summary>
        /// Génère le prochain numéro de facture
        /// </summary>
        public async Task<string> GenerateNextFactureNumberAsync()
        {
            using var connection = await _dbConnection.GetConnectionAsync();
            
            var year = DateTime.Now.Year;
            var month = DateTime.Now.Month;
            
            var sql = @"
                SELECT COALESCE(MAX(CAST(SUBSTRING_INDEX(numero_facture, '-', -1) AS UNSIGNED)), 0) + 1
                FROM factures_vente 
                WHERE YEAR(date_facture) = @Year AND MONTH(date_facture) = @Month";
            
            var nextNumber = await connection.QuerySingleAsync<int>(sql, new { Year = year, Month = month });
            
            return $"FAC-{year}-{month:D2}-{nextNumber:D4}";
        }

        /// <summary>
        /// Valide une facture
        /// </summary>
        public async Task<bool> ValidateFactureAsync(int factureId, int userId)
        {
            using var connection = await _dbConnection.GetConnectionAsync();
            
            var sql = @"
                UPDATE factures_vente 
                SET statut_facture = 'Validée', updated_at = @UpdatedAt
                WHERE id = @Id AND statut_facture = 'Brouillon'";
            
            var rowsAffected = await connection.ExecuteAsync(sql, 
                new { UpdatedAt = DateTime.Now, Id = factureId });
            
            return rowsAffected > 0;
        }

        /// <summary>
        /// Annule une facture
        /// </summary>
        public async Task<bool> CancelFactureAsync(int factureId, int userId, string motif)
        {
            using var connection = await _dbConnection.GetConnectionAsync();
            using var transaction = connection.BeginTransaction();

            try
            {
                // Récupérer les détails de la facture pour restaurer le stock
                var details = await connection.QueryAsync<DetailFactureVente>(
                    "SELECT * FROM details_facture_vente WHERE facture_vente_id = @FactureId",
                    new { FactureId = factureId }, transaction);

                // Restaurer le stock pour chaque article
                foreach (var detail in details)
                {
                    await connection.ExecuteAsync(@"
                        UPDATE articles 
                        SET stock_actuel = stock_actuel + @Quantite, updated_at = @UpdatedAt
                        WHERE id = @ArticleId",
                        new { detail.Quantite, UpdatedAt = DateTime.Now, detail.ArticleId }, transaction);
                }

                // Marquer la facture comme annulée
                await connection.ExecuteAsync(@"
                    UPDATE factures_vente 
                    SET statut_facture = 'Annulée', notes = CONCAT(COALESCE(notes, ''), ' - Annulée: ', @Motif), updated_at = @UpdatedAt
                    WHERE id = @Id",
                    new { Motif = motif, UpdatedAt = DateTime.Now, Id = factureId }, transaction);

                transaction.Commit();
                return true;
            }
            catch
            {
                transaction.Rollback();
                return false;
            }
        }
    }
}
