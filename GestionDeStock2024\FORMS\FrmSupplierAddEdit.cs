using System;
using System.Drawing;
using System.Windows.Forms;

namespace GestionDeStock2024.FORMS
{
    /// <summary>
    /// Formulaire d'ajout/modification de fournisseur
    /// </summary>
    public partial class FrmSupplierAddEdit : Form
    {
        private bool isEditMode = false;
        private string editCode;

        public FrmSupplierAddEdit()
        {
            InitializeComponent();
            cmbStatut.SelectedIndex = 0; // Actif par défaut
            cmbType.SelectedIndex = 0; // Premier type par défaut
        }

        public FrmSupplierAddEdit(string code) : this()
        {
            isEditMode = true;
            editCode = code;
            this.Text = "Modifier le fournisseur";
            titleLabel.Text = "✏️ Modifier le fournisseur";
            LoadSupplierData();
        }

        private void LoadSupplierData()
        {
            if (isEditMode && !string.IsNullOrEmpty(editCode))
            {
                txtCode.Text = editCode;
                txtCode.ReadOnly = true;
                
                // Données fictives pour la démonstration
                switch (editCode)
                {
                    case "FOUR001":
                        txtNom.Text = "SARL ALPHA DISTRIBUTION";
                        cmbType.SelectedItem = "Grossiste";
                        txtTelephone.Text = "021-123456";
                        txtEmail.Text = "<EMAIL>";
                        txtAdresse.Text = "Zone Industrielle Rouiba, Alger";
                        txtNIF.Text = "123456789012345";
                        txtContact.Text = "Ahmed Benali";
                        txtDescription.Text = "Grossiste spécialisé dans la distribution alimentaire";
                        break;
                    case "FOUR002":
                        txtNom.Text = "BETA IMPORT EXPORT";
                        cmbType.SelectedItem = "Importateur";
                        txtTelephone.Text = "021-789012";
                        txtEmail.Text = "<EMAIL>";
                        txtAdresse.Text = "Port d'Alger, Alger";
                        txtNIF.Text = "987654321098765";
                        txtContact.Text = "Fatima Khelifi";
                        txtDescription.Text = "Importateur de produits électroniques";
                        break;
                    default:
                        txtNom.Text = "Fournisseur exemple";
                        cmbType.SelectedIndex = 0;
                        txtTelephone.Text = "";
                        txtEmail.Text = "";
                        txtAdresse.Text = "";
                        txtNIF.Text = "";
                        txtContact.Text = "";
                        txtDescription.Text = "";
                        break;
                }
                cmbStatut.SelectedItem = "Actif";
            }
        }

        private bool ValidateForm()
        {
            if (string.IsNullOrWhiteSpace(txtCode.Text))
            {
                MessageBox.Show("Le code est obligatoire", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtCode.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtNom.Text))
            {
                MessageBox.Show("Le nom est obligatoire", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtNom.Focus();
                return false;
            }

            if (cmbType.SelectedIndex == -1)
            {
                MessageBox.Show("Le type est obligatoire", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                cmbType.Focus();
                return false;
            }

            // Validation de l'email si fourni
            if (!string.IsNullOrWhiteSpace(txtEmail.Text))
            {
                if (!IsValidEmail(txtEmail.Text))
                {
                    MessageBox.Show("L'adresse email n'est pas valide", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtEmail.Focus();
                    return false;
                }
            }

            // Validation du NIF si fourni (doit être numérique et 15 chiffres)
            if (!string.IsNullOrWhiteSpace(txtNIF.Text))
            {
                if (txtNIF.Text.Length != 15 || !long.TryParse(txtNIF.Text, out _))
                {
                    MessageBox.Show("Le NIF doit contenir exactement 15 chiffres", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtNIF.Focus();
                    return false;
                }
            }

            return true;
        }

        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }

        private void BtnSave_Click(object sender, EventArgs e)
        {
            if (ValidateForm())
            {
                // Ici, vous ajouteriez la logique pour sauvegarder en base de données
                MessageBox.Show(
                    isEditMode ? "Fournisseur modifié avec succès" : "Fournisseur ajouté avec succès",
                    "Succès",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private void TxtNIF_KeyPress(object sender, KeyPressEventArgs e)
        {
            // Autoriser seulement les chiffres et les touches de contrôle
            if (!char.IsControl(e.KeyChar) && !char.IsDigit(e.KeyChar))
            {
                e.Handled = true;
            }
        }

        private void TxtTelephone_KeyPress(object sender, KeyPressEventArgs e)
        {
            // Autoriser les chiffres, espaces, tirets et parenthèses pour le téléphone
            if (!char.IsControl(e.KeyChar) && !char.IsDigit(e.KeyChar) && 
                e.KeyChar != ' ' && e.KeyChar != '-' && e.KeyChar != '(' && e.KeyChar != ')')
            {
                e.Handled = true;
            }
        }

        private void FrmSupplierAddEdit_Load(object sender, EventArgs e)
        {
            // Initialiser les ComboBox avec les valeurs par défaut
            if (cmbType.SelectedIndex == -1)
                cmbType.SelectedIndex = 0;
            
            if (cmbStatut.SelectedIndex == -1)
                cmbStatut.SelectedIndex = 0;

            if (!isEditMode)
            {
                txtCode.Focus();
            }
            else
            {
                txtNom.Focus();
            }
        }
    }
}
