using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq.Expressions;

namespace GestionDeStock2024.DATA
{
    /// <summary>
    /// Interface générique pour les repositories
    /// </summary>
    /// <typeparam name="T">Type de l'entité</typeparam>
    public interface IRepository<T> where T : class
    {
        // Opérations de base
        Task<T> GetByIdAsync(int id);
        Task<IEnumerable<T>> GetAllAsync();
        Task<IEnumerable<T>> GetPagedAsync(int page, int pageSize);
        Task<int> GetCountAsync();
        
        // Opérations CRUD
        Task<int> AddAsync(T entity);
        Task<bool> UpdateAsync(T entity);
        Task<bool> DeleteAsync(int id);
        Task<bool> DeleteAsync(T entity);
        
        // Recherche et filtrage
        Task<IEnumerable<T>> FindAsync(string whereClause, object parameters = null);
        Task<T> FindFirstAsync(string whereClause, object parameters = null);
        Task<int> CountAsync(string whereClause, object parameters = null);
        
        // Opérations en lot
        Task<bool> AddRangeAsync(IEnumerable<T> entities);
        Task<bool> UpdateRangeAsync(IEnumerable<T> entities);
        Task<bool> DeleteRangeAsync(IEnumerable<int> ids);
        
        // Vérifications d'existence
        Task<bool> ExistsAsync(int id);
        Task<bool> ExistsAsync(string whereClause, object parameters = null);
    }

    /// <summary>
    /// Interface pour les repositories avec code
    /// </summary>
    /// <typeparam name="T">Type de l'entité</typeparam>
    public interface ICodeRepository<T> : IRepository<T> where T : class
    {
        Task<T> GetByCodeAsync(string code);
        Task<bool> CodeExistsAsync(string code, int? excludeId = null);
        Task<string> GenerateNextCodeAsync(string prefix = "");
    }

    /// <summary>
    /// Interface pour les repositories avec statut
    /// </summary>
    /// <typeparam name="T">Type de l'entité</typeparam>
    public interface IStatutRepository<T> : IRepository<T> where T : class
    {
        Task<IEnumerable<T>> GetActiveAsync();
        Task<IEnumerable<T>> GetInactiveAsync();
        Task<bool> ActivateAsync(int id);
        Task<bool> DeactivateAsync(int id);
        Task<bool> ChangeStatusAsync(int id, string newStatus);
    }

    /// <summary>
    /// Interface combinée pour les repositories avec code et statut
    /// </summary>
    /// <typeparam name="T">Type de l'entité</typeparam>
    public interface ICodeStatutRepository<T> : ICodeRepository<T>, IStatutRepository<T> where T : class
    {
        Task<IEnumerable<T>> GetActiveByCodeAsync(string codePattern);
    }

    /// <summary>
    /// Interface pour les opérations de recherche avancée
    /// </summary>
    /// <typeparam name="T">Type de l'entité</typeparam>
    public interface ISearchableRepository<T> : IRepository<T> where T : class
    {
        Task<IEnumerable<T>> SearchAsync(string searchTerm, string[] searchFields = null);
        Task<IEnumerable<T>> SearchPagedAsync(string searchTerm, int page, int pageSize, string[] searchFields = null);
        Task<int> SearchCountAsync(string searchTerm, string[] searchFields = null);
    }

    /// <summary>
    /// Interface pour les repositories avec tri
    /// </summary>
    /// <typeparam name="T">Type de l'entité</typeparam>
    public interface ISortableRepository<T> : IRepository<T> where T : class
    {
        Task<IEnumerable<T>> GetSortedAsync(string sortField, bool ascending = true);
        Task<IEnumerable<T>> GetPagedSortedAsync(int page, int pageSize, string sortField, bool ascending = true);
    }

    /// <summary>
    /// Interface complète combinant toutes les fonctionnalités
    /// </summary>
    /// <typeparam name="T">Type de l'entité</typeparam>
    public interface IFullRepository<T> : ICodeStatutRepository<T>, ISearchableRepository<T>, ISortableRepository<T> where T : class
    {
        // Interface complète héritant de toutes les autres
    }

    /// <summary>
    /// Interface pour les repositories avec gestion des relations
    /// </summary>
    /// <typeparam name="T">Type de l'entité</typeparam>
    public interface IRelationalRepository<T> : IRepository<T> where T : class
    {
        Task<T> GetWithRelationsAsync(int id, params string[] includes);
        Task<IEnumerable<T>> GetAllWithRelationsAsync(params string[] includes);
        Task<IEnumerable<T>> GetPagedWithRelationsAsync(int page, int pageSize, params string[] includes);
    }

    /// <summary>
    /// Interface pour les repositories avec cache
    /// </summary>
    /// <typeparam name="T">Type de l'entité</typeparam>
    public interface ICacheableRepository<T> : IRepository<T> where T : class
    {
        Task<T> GetByIdCachedAsync(int id, TimeSpan? cacheExpiry = null);
        Task<IEnumerable<T>> GetAllCachedAsync(TimeSpan? cacheExpiry = null);
        Task ClearCacheAsync();
        Task RefreshCacheAsync();
    }

    /// <summary>
    /// Interface pour les repositories avec audit
    /// </summary>
    /// <typeparam name="T">Type de l'entité</typeparam>
    public interface IAuditableRepository<T> : IRepository<T> where T : class
    {
        Task<int> AddWithAuditAsync(T entity, int userId);
        Task<bool> UpdateWithAuditAsync(T entity, int userId);
        Task<bool> DeleteWithAuditAsync(int id, int userId);
        Task<IEnumerable<object>> GetAuditHistoryAsync(int entityId);
    }

    /// <summary>
    /// Interface pour les repositories avec validation
    /// </summary>
    /// <typeparam name="T">Type de l'entité</typeparam>
    public interface IValidatableRepository<T> : IRepository<T> where T : class
    {
        Task<(bool IsValid, List<string> Errors)> ValidateAsync(T entity);
        Task<(bool IsValid, List<string> Errors)> ValidateForAddAsync(T entity);
        Task<(bool IsValid, List<string> Errors)> ValidateForUpdateAsync(T entity);
        Task<(bool IsValid, List<string> Errors)> ValidateForDeleteAsync(int id);
    }

    /// <summary>
    /// Interface pour les repositories avec transactions
    /// </summary>
    /// <typeparam name="T">Type de l'entité</typeparam>
    public interface ITransactionalRepository<T> : IRepository<T> where T : class
    {
        Task<TResult> ExecuteInTransactionAsync<TResult>(Func<Task<TResult>> operation);
        Task ExecuteInTransactionAsync(Func<Task> operation);
        Task<bool> BulkOperationAsync(Func<Task<bool>> operation);
    }

    /// <summary>
    /// Interface maître combinant toutes les fonctionnalités avancées
    /// </summary>
    /// <typeparam name="T">Type de l'entité</typeparam>
    public interface IMasterRepository<T> : 
        IFullRepository<T>, 
        IRelationalRepository<T>, 
        ICacheableRepository<T>, 
        IAuditableRepository<T>, 
        IValidatableRepository<T>, 
        ITransactionalRepository<T> 
        where T : class
    {
        // Interface maître héritant de toutes les fonctionnalités
    }
}
