using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using static GestionDeStock2024.MODELS.Enums;

namespace GestionDeStock2024.MODELS
{
    /// <summary>
    /// Modèle pour les mouvements de stock
    /// </summary>
    public class MouvementStock : BaseEntity
    {
        [Required(ErrorMessage = "L'article est obligatoire")]
        public int ArticleId { get; set; }

        [Required(ErrorMessage = "Le type de mouvement est obligatoire")]
        public string TypeMouvement { get; set; }

        [StringLength(100)]
        public string ReferenceDocument { get; set; }

        [Required(ErrorMessage = "Le type de document est obligatoire")]
        public string TypeDocument { get; set; }

        public int? DocumentId { get; set; }

        [Required(ErrorMessage = "La quantité avant est obligatoire")]
        public decimal QuantiteAvant { get; set; }

        [Required(ErrorMessage = "La quantité de mouvement est obligatoire")]
        public decimal QuantiteMouvement { get; set; }

        [Required(ErrorMessage = "La quantité après est obligatoire")]
        public decimal QuantiteApres { get; set; }

        public decimal PrixUnitaire { get; set; } = 0;

        public decimal CoutTotal { get; set; } = 0;

        [Required(ErrorMessage = "L'utilisateur est obligatoire")]
        public int UtilisateurId { get; set; }

        [Required(ErrorMessage = "La date de mouvement est obligatoire")]
        public DateTime DateMouvement { get; set; } = DateTime.Today;

        [Required(ErrorMessage = "L'heure de mouvement est obligatoire")]
        public TimeSpan HeureMouvement { get; set; } = DateTime.Now.TimeOfDay;

        [StringLength(255)]
        public string Motif { get; set; }

        public string Notes { get; set; }

        // Navigation
        public virtual Article Article { get; set; }
        public virtual Utilisateur Utilisateur { get; set; }

        // Propriétés calculées
        public bool EstEntree => TypeMouvement == TypeMouvement.Entrée.ToString();
        public bool EstSortie => TypeMouvement == TypeMouvement.Sortie.ToString();
        public bool EstAjustement => TypeMouvement == TypeMouvement.Ajustement.ToString();
        public bool EstTransfert => TypeMouvement == TypeMouvement.Transfert.ToString();
        public bool EstInventaire => TypeMouvement == TypeMouvement.Inventaire.ToString();

        public decimal EcartQuantite => QuantiteApres - QuantiteAvant;

        public string DescriptionMouvement => $"{TypeMouvement} - {QuantiteMouvement} {Article?.UniteMesure?.Abreviation}";

        /// <summary>
        /// Calcule le coût total du mouvement
        /// </summary>
        public void CalculerCoutTotal()
        {
            CoutTotal = Math.Abs(QuantiteMouvement) * PrixUnitaire;
        }
    }

    /// <summary>
    /// Modèle pour les inventaires
    /// </summary>
    public class Inventaire : BaseEntity, IStatutEntity
    {
        [Required(ErrorMessage = "Le numéro d'inventaire est obligatoire")]
        [StringLength(50)]
        public string NumeroInventaire { get; set; }

        [Required(ErrorMessage = "Le nom de l'inventaire est obligatoire")]
        [StringLength(255)]
        public string NomInventaire { get; set; }

        [Required(ErrorMessage = "La date d'inventaire est obligatoire")]
        public DateTime DateInventaire { get; set; } = DateTime.Today;

        [Required(ErrorMessage = "L'utilisateur est obligatoire")]
        public int UtilisateurId { get; set; }

        public string Statut { get; set; } = "En_cours";

        public string Notes { get; set; }

        // Navigation
        public virtual Utilisateur Utilisateur { get; set; }
        public virtual ICollection<DetailInventaire> DetailsInventaire { get; set; }

        // Propriétés calculées
        public bool EstEnCours => Statut == "En_cours";
        public bool EstTermine => Statut == "Terminé";
        public bool EstValide => Statut == "Validé";
        public bool EstAnnule => Statut == "Annulé";

        public int NombreArticles => DetailsInventaire?.Count ?? 0;

        public int NombreEcarts => DetailsInventaire?.Count(d => d.Ecart != 0) ?? 0;

        public decimal ValeurTotaleEcarts => DetailsInventaire?.Sum(d => d.ValeurEcart) ?? 0;

        public decimal PourcentageEcarts => NombreArticles > 0 ? 
            (decimal)NombreEcarts / NombreArticles * 100 : 0;
    }

    /// <summary>
    /// Modèle pour les détails d'inventaire
    /// </summary>
    public class DetailInventaire : BaseEntity
    {
        [Required(ErrorMessage = "L'inventaire est obligatoire")]
        public int InventaireId { get; set; }

        [Required(ErrorMessage = "L'article est obligatoire")]
        public int ArticleId { get; set; }

        [Required(ErrorMessage = "Le stock théorique est obligatoire")]
        public decimal StockTheorique { get; set; }

        [Required(ErrorMessage = "Le stock physique est obligatoire")]
        public decimal StockPhysique { get; set; }

        [Required(ErrorMessage = "L'écart est obligatoire")]
        public decimal Ecart { get; set; }

        public decimal ValeurEcart { get; set; } = 0;

        [StringLength(255)]
        public string MotifEcart { get; set; }

        // Navigation
        public virtual Inventaire Inventaire { get; set; }
        public virtual Article Article { get; set; }

        // Propriétés calculées
        public bool AEcart => Ecart != 0;

        public bool EcartPositif => Ecart > 0;

        public bool EcartNegatif => Ecart < 0;

        public decimal PourcentageEcart => StockTheorique != 0 ? 
            Math.Abs(Ecart) / StockTheorique * 100 : 0;

        public string TypeEcart
        {
            get
            {
                if (Ecart > 0) return "Excédent";
                if (Ecart < 0) return "Manquant";
                return "Conforme";
            }
        }

        /// <summary>
        /// Calcule l'écart et sa valeur
        /// </summary>
        public void CalculerEcart()
        {
            Ecart = StockPhysique - StockTheorique;
            if (Article != null)
            {
                ValeurEcart = Ecart * Article.PMP;
            }
        }
    }

    /// <summary>
    /// Modèle pour les promotions
    /// </summary>
    public class Promotion : BaseEntity, IStatutEntity
    {
        [Required(ErrorMessage = "Le nom de la promotion est obligatoire")]
        [StringLength(255)]
        public string NomPromotion { get; set; }

        public string Description { get; set; }

        [Required(ErrorMessage = "Le type de promotion est obligatoire")]
        public string TypePromotion { get; set; }

        public decimal ValeurRemise { get; set; } = 0;

        [Range(1, int.MaxValue, ErrorMessage = "La quantité requise doit être au moins 1")]
        public int QuantiteRequise { get; set; } = 1;

        public int QuantiteGratuite { get; set; } = 0;

        [Required(ErrorMessage = "La date de début est obligatoire")]
        public DateTime DateDebut { get; set; } = DateTime.Today;

        [Required(ErrorMessage = "La date de fin est obligatoire")]
        public DateTime DateFin { get; set; } = DateTime.Today.AddDays(7);

        public TimeSpan HeureDebut { get; set; } = TimeSpan.Zero;

        public TimeSpan HeureFin { get; set; } = new TimeSpan(23, 59, 59);

        public string JoursSemaine { get; set; } = "Lundi,Mardi,Mercredi,Jeudi,Vendredi,Samedi,Dimanche";

        public int LimiteUtilisation { get; set; } = 0; // 0 = illimité

        public int UtilisationsActuelles { get; set; } = 0;

        public decimal MontantMinimumAchat { get; set; } = 0;

        public string ApplicableClients { get; set; } = "Tous";

        public string Statut { get; set; } = StatutGeneral.Actif.ToString();

        // Navigation
        public virtual ICollection<ArticlePromotion> ArticlesPromotion { get; set; }

        // Propriétés calculées
        public bool EstActive => Statut == StatutGeneral.Actif.ToString() && 
                                EstDansLaPeriode && 
                                !EstLimiteAtteinte;

        public bool EstDansLaPeriode => DateTime.Now.Date >= DateDebut.Date && 
                                       DateTime.Now.Date <= DateFin.Date &&
                                       EstDansLesHeures &&
                                       EstDansLesJours;

        public bool EstDansLesHeures => DateTime.Now.TimeOfDay >= HeureDebut && 
                                       DateTime.Now.TimeOfDay <= HeureFin;

        public bool EstDansLesJours => JoursSemaine.Contains(DateTime.Now.DayOfWeek.ToString());

        public bool EstLimiteAtteinte => LimiteUtilisation > 0 && 
                                        UtilisationsActuelles >= LimiteUtilisation;

        public bool EstExpiree => DateTime.Now.Date > DateFin.Date;

        public bool EstPourcentage => TypePromotion == TypePromotion.Pourcentage.ToString();

        public bool EstMontantFixe => TypePromotion == TypePromotion.Montant_fixe.ToString();

        public bool EstAchetezXObtenezY => TypePromotion == TypePromotion.Achetez_X_Obtenez_Y.ToString();

        public bool EstPack => TypePromotion == TypePromotion.Pack.ToString();

        public int UtilisationsRestantes => LimiteUtilisation > 0 ? 
            Math.Max(0, LimiteUtilisation - UtilisationsActuelles) : int.MaxValue;

        /// <summary>
        /// Vérifie si la promotion est applicable pour un client
        /// </summary>
        public bool EstApplicablePourClient(Client client)
        {
            if (ApplicableClients == "Tous") return true;
            if (client == null) return ApplicableClients == "Particuliers";

            return ApplicableClients switch
            {
                "Particuliers" => client.EstParticulier,
                "Entreprises" => client.EstEntreprise,
                "Revendeurs" => client.EstRevendeur,
                _ => true
            };
        }

        /// <summary>
        /// Calcule la remise pour un montant donné
        /// </summary>
        public decimal CalculerRemise(decimal montant)
        {
            if (!EstActive || montant < MontantMinimumAchat) return 0;

            return TypePromotion switch
            {
                "Pourcentage" => montant * (ValeurRemise / 100),
                "Montant_fixe" => ValeurRemise,
                _ => 0
            };
        }
    }

    /// <summary>
    /// Modèle pour les articles en promotion
    /// </summary>
    public class ArticlePromotion : BaseEntity
    {
        [Required(ErrorMessage = "La promotion est obligatoire")]
        public int PromotionId { get; set; }

        [Required(ErrorMessage = "L'article est obligatoire")]
        public int ArticleId { get; set; }

        public decimal? PrixPromo { get; set; }

        // Navigation
        public virtual Promotion Promotion { get; set; }
        public virtual Article Article { get; set; }

        // Propriétés calculées
        public bool APrixSpecial => PrixPromo.HasValue && PrixPromo.Value > 0;

        public decimal PrixApplicable => APrixSpecial ? PrixPromo.Value : Article?.PrixVenteUnitaire ?? 0;

        public decimal EconomieRealisee => Article != null && APrixSpecial ? 
            Article.PrixVenteUnitaire - PrixPromo.Value : 0;

        public decimal PourcentageEconomie => Article != null && Article.PrixVenteUnitaire > 0 && APrixSpecial ? 
            (EconomieRealisee / Article.PrixVenteUnitaire) * 100 : 0;
    }

    /// <summary>
    /// Modèle pour les packs/lots
    /// </summary>
    public class Pack : BaseEntity, IStatutEntity, ICodeEntity
    {
        [Required(ErrorMessage = "Le nom du pack est obligatoire")]
        [StringLength(255)]
        public string NomPack { get; set; }

        public string Description { get; set; }

        [Required(ErrorMessage = "Le code du pack est obligatoire")]
        [StringLength(50)]
        public string Code { get; set; }

        [Required(ErrorMessage = "Le prix du pack est obligatoire")]
        [Range(0, double.MaxValue, ErrorMessage = "Le prix du pack doit être positif")]
        public decimal PrixPack { get; set; }

        public decimal PrixUnitaireCalcule { get; set; } = 0;

        public decimal RemisePack { get; set; } = 0;

        [Range(0, 100, ErrorMessage = "La TVA doit être entre 0 et 100%")]
        public decimal TvaPack { get; set; } = 19.00m;

        public decimal StockPack { get; set; } = 0;

        public string Statut { get; set; } = StatutGeneral.Actif.ToString();

        // Navigation
        public virtual ICollection<ArticlePack> ArticlesPack { get; set; }

        // Propriétés calculées
        public int NombreArticles => ArticlesPack?.Count ?? 0;

        public decimal PrixTotalArticles => ArticlesPack?.Sum(ap => ap.PrixUnitaire * ap.Quantite) ?? 0;

        public decimal EconomieRealisee => PrixTotalArticles - PrixPack;

        public decimal PourcentageEconomie => PrixTotalArticles > 0 ? 
            (EconomieRealisee / PrixTotalArticles) * 100 : 0;

        public bool EstRentable => EconomieRealisee > 0;

        /// <summary>
        /// Calcule le prix unitaire du pack
        /// </summary>
        public void CalculerPrixUnitaire()
        {
            var quantiteTotale = ArticlesPack?.Sum(ap => ap.Quantite) ?? 0;
            PrixUnitaireCalcule = quantiteTotale > 0 ? PrixPack / quantiteTotale : 0;
        }

        /// <summary>
        /// Vérifie si le pack peut être assemblé (stock suffisant)
        /// </summary>
        public bool PeutEtreAssemble()
        {
            if (ArticlesPack == null || !ArticlesPack.Any()) return false;

            return ArticlesPack.All(ap => ap.Article != null && 
                                         ap.Article.StockDisponible >= ap.Quantite);
        }
    }

    /// <summary>
    /// Modèle pour les articles dans les packs
    /// </summary>
    public class ArticlePack : BaseEntity
    {
        [Required(ErrorMessage = "Le pack est obligatoire")]
        public int PackId { get; set; }

        [Required(ErrorMessage = "L'article est obligatoire")]
        public int ArticleId { get; set; }

        [Required(ErrorMessage = "La quantité est obligatoire")]
        [Range(0.01, double.MaxValue, ErrorMessage = "La quantité doit être positive")]
        public decimal Quantite { get; set; } = 1;

        [Required(ErrorMessage = "Le prix unitaire est obligatoire")]
        [Range(0, double.MaxValue, ErrorMessage = "Le prix unitaire doit être positif")]
        public decimal PrixUnitaire { get; set; }

        // Navigation
        public virtual Pack Pack { get; set; }
        public virtual Article Article { get; set; }

        // Propriétés calculées
        public decimal MontantTotal => Quantite * PrixUnitaire;

        public bool StockSuffisant => Article != null && Article.StockDisponible >= Quantite;

        public decimal QuantiteManquante => Article != null && !StockSuffisant ? 
            Quantite - Article.StockDisponible : 0;
    }
}
