using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dapper;
using GestionDeStock2024.MODELS;

namespace GestionDeStock2024.DATA.Repositories
{
    /// <summary>
    /// Repository pour la gestion des clients
    /// </summary>
    public interface IClientRepository : IMasterRepository<Client>
    {
        Task<IEnumerable<Client>> GetClientsByTypeAsync(string typeClient);
        Task<IEnumerable<Client>> GetClientsWithCreditAsync();
        Task<IEnumerable<Client>> GetClientsWithOverduePaymentsAsync();
        Task<Client> GetByNIFAsync(string nif);
        Task<bool> UpdateSoldeAsync(int clientId, decimal montant, bool isCredit = true);
        Task<decimal> GetTotalCreditAsync();
        Task<IEnumerable<object>> GetClientStatisticsAsync(int clientId);
        Task<bool> ValidateNIFAsync(string nif, int? excludeClientId = null);
        Task<IEnumerable<Client>> GetTopClientsAsync(int count = 10);
    }

    public class ClientRepository : BaseRepository<Client>, IClientRepository
    {
        public ClientRepository() : base("clients", "id") { }

        /// <summary>
        /// Obtient un client avec ses relations (factures, retours)
        /// </summary>
        public override async Task<Client> GetWithRelationsAsync(int id, params string[] includes)
        {
            using var connection = await _dbConnection.GetConnectionAsync();
            
            var sql = @"
                SELECT c.*, 
                       COUNT(DISTINCT fv.id) as NombreFactures,
                       COALESCE(SUM(fv.montant_total), 0) as TotalAchats,
                       COUNT(DISTINCT rc.id) as NombreRetours
                FROM clients c
                LEFT JOIN factures_vente fv ON c.id = fv.client_id AND fv.statut_facture = 'Validée'
                LEFT JOIN retours_clients rc ON c.id = rc.client_id
                WHERE c.id = @Id
                GROUP BY c.id";

            return await connection.QueryFirstOrDefaultAsync<Client>(sql, new { Id = id });
        }

        /// <summary>
        /// Obtient les clients par type
        /// </summary>
        public async Task<IEnumerable<Client>> GetClientsByTypeAsync(string typeClient)
        {
            using var connection = await _dbConnection.GetConnectionAsync();
            
            var sql = @"
                SELECT c.*, 
                       COUNT(DISTINCT fv.id) as NombreFactures,
                       COALESCE(SUM(fv.montant_total), 0) as TotalAchats
                FROM clients c
                LEFT JOIN factures_vente fv ON c.id = fv.client_id AND fv.statut_facture = 'Validée'
                WHERE c.type_client = @TypeClient AND c.statut = 'Actif'
                GROUP BY c.id
                ORDER BY c.nom_client";

            return await connection.QueryAsync<Client>(sql, new { TypeClient = typeClient });
        }

        /// <summary>
        /// Obtient les clients ayant un crédit
        /// </summary>
        public async Task<IEnumerable<Client>> GetClientsWithCreditAsync()
        {
            using var connection = await _dbConnection.GetConnectionAsync();
            
            var sql = @"
                SELECT c.*, 
                       (c.credit_limite - c.solde_actuel) as CreditDisponible,
                       COUNT(DISTINCT fv.id) as NombreFacturesImpayees
                FROM clients c
                LEFT JOIN factures_vente fv ON c.id = fv.client_id 
                    AND fv.statut_paiement IN ('Non_payé', 'Partiellement_payé')
                WHERE c.solde_actuel > 0 AND c.statut = 'Actif'
                GROUP BY c.id
                ORDER BY c.solde_actuel DESC";

            return await connection.QueryAsync<Client>(sql);
        }

        /// <summary>
        /// Obtient les clients avec des paiements en retard
        /// </summary>
        public async Task<IEnumerable<Client>> GetClientsWithOverduePaymentsAsync()
        {
            using var connection = await _dbConnection.GetConnectionAsync();
            
            var sql = @"
                SELECT DISTINCT c.*, 
                       COUNT(fv.id) as NombreFacturesEnRetard,
                       SUM(fv.montant_restant) as MontantEnRetard,
                       MIN(fv.date_facture) as PlusAncienneFacture
                FROM clients c
                INNER JOIN factures_vente fv ON c.id = fv.client_id
                WHERE fv.statut_paiement IN ('Non_payé', 'Partiellement_payé')
                AND fv.date_facture < DATE_SUB(CURDATE(), INTERVAL 30 DAY)
                AND c.statut = 'Actif'
                GROUP BY c.id
                ORDER BY MontantEnRetard DESC";

            return await connection.QueryAsync<Client>(sql);
        }

        /// <summary>
        /// Obtient un client par son NIF
        /// </summary>
        public async Task<Client> GetByNIFAsync(string nif)
        {
            if (string.IsNullOrEmpty(nif)) return null;

            using var connection = await _dbConnection.GetConnectionAsync();
            
            var sql = "SELECT * FROM clients WHERE nif = @NIF AND statut = 'Actif'";
            return await connection.QueryFirstOrDefaultAsync<Client>(sql, new { NIF = nif });
        }

        /// <summary>
        /// Met à jour le solde d'un client
        /// </summary>
        public async Task<bool> UpdateSoldeAsync(int clientId, decimal montant, bool isCredit = true)
        {
            using var connection = await _dbConnection.GetConnectionAsync();
            
            var operation = isCredit ? "+" : "-";
            var sql = $@"
                UPDATE clients 
                SET solde_actuel = solde_actuel {operation} @Montant, 
                    updated_at = @UpdatedAt
                WHERE id = @Id";

            var rowsAffected = await connection.ExecuteAsync(sql, 
                new { Montant = Math.Abs(montant), UpdatedAt = DateTime.Now, Id = clientId });
            
            return rowsAffected > 0;
        }

        /// <summary>
        /// Obtient le total des crédits clients
        /// </summary>
        public async Task<decimal> GetTotalCreditAsync()
        {
            using var connection = await _dbConnection.GetConnectionAsync();
            
            var sql = "SELECT COALESCE(SUM(solde_actuel), 0) FROM clients WHERE solde_actuel > 0 AND statut = 'Actif'";
            return await connection.QuerySingleAsync<decimal>(sql);
        }

        /// <summary>
        /// Obtient les statistiques d'un client
        /// </summary>
        public async Task<IEnumerable<object>> GetClientStatisticsAsync(int clientId)
        {
            using var connection = await _dbConnection.GetConnectionAsync();
            
            var sql = @"
                SELECT 
                    'Factures' as Type,
                    COUNT(*) as Nombre,
                    COALESCE(SUM(montant_total), 0) as Montant,
                    YEAR(date_facture) as Annee,
                    MONTH(date_facture) as Mois
                FROM factures_vente 
                WHERE client_id = @ClientId AND statut_facture = 'Validée'
                GROUP BY YEAR(date_facture), MONTH(date_facture)
                
                UNION ALL
                
                SELECT 
                    'Retours' as Type,
                    COUNT(*) as Nombre,
                    COALESCE(SUM(montant_ttc), 0) as Montant,
                    YEAR(date_retour) as Annee,
                    MONTH(date_retour) as Mois
                FROM retours_clients 
                WHERE client_id = @ClientId AND statut = 'Approuvé'
                GROUP BY YEAR(date_retour), MONTH(date_retour)
                
                ORDER BY Annee DESC, Mois DESC";

            return await connection.QueryAsync(sql, new { ClientId = clientId });
        }

        /// <summary>
        /// Valide l'unicité du NIF
        /// </summary>
        public async Task<bool> ValidateNIFAsync(string nif, int? excludeClientId = null)
        {
            if (string.IsNullOrEmpty(nif)) return true; // NIF optionnel

            using var connection = await _dbConnection.GetConnectionAsync();
            
            var sql = "SELECT COUNT(*) FROM clients WHERE nif = @NIF";
            var parameters = new { NIF = nif };

            if (excludeClientId.HasValue)
            {
                sql += " AND id != @ExcludeId";
                parameters = new { NIF = nif, ExcludeId = excludeClientId.Value };
            }

            var count = await connection.QuerySingleAsync<int>(sql, parameters);
            return count == 0;
        }

        /// <summary>
        /// Obtient les meilleurs clients
        /// </summary>
        public async Task<IEnumerable<Client>> GetTopClientsAsync(int count = 10)
        {
            using var connection = await _dbConnection.GetConnectionAsync();
            
            var sql = @"
                SELECT c.*, 
                       COUNT(DISTINCT fv.id) as NombreFactures,
                       COALESCE(SUM(fv.montant_total), 0) as TotalAchats,
                       COALESCE(AVG(fv.montant_total), 0) as MoyenneAchats
                FROM clients c
                INNER JOIN factures_vente fv ON c.id = fv.client_id 
                WHERE fv.statut_facture = 'Validée' 
                AND c.statut = 'Actif'
                AND fv.date_facture >= DATE_SUB(CURDATE(), INTERVAL 12 MONTH)
                GROUP BY c.id
                ORDER BY TotalAchats DESC
                LIMIT @Count";

            return await connection.QueryAsync<Client>(sql, new { Count = count });
        }

        /// <summary>
        /// Recherche avancée de clients
        /// </summary>
        public override async Task<IEnumerable<Client>> SearchAsync(string searchTerm, string[] searchFields = null)
        {
            if (string.IsNullOrEmpty(searchTerm)) return await GetAllAsync();

            using var connection = await _dbConnection.GetConnectionAsync();
            
            var sql = @"
                SELECT c.*, 
                       COUNT(DISTINCT fv.id) as NombreFactures,
                       COALESCE(SUM(fv.montant_total), 0) as TotalAchats
                FROM clients c
                LEFT JOIN factures_vente fv ON c.id = fv.client_id AND fv.statut_facture = 'Validée'
                WHERE (c.nom_client LIKE @SearchTerm 
                   OR c.prenom_client LIKE @SearchTerm 
                   OR c.raison_sociale LIKE @SearchTerm
                   OR c.code_client LIKE @SearchTerm
                   OR c.telephone LIKE @SearchTerm
                   OR c.email LIKE @SearchTerm
                   OR c.nif = @ExactTerm)
                AND c.statut = 'Actif'
                GROUP BY c.id
                ORDER BY c.nom_client";

            return await connection.QueryAsync<Client>(sql, 
                new { SearchTerm = $"%{searchTerm}%", ExactTerm = searchTerm });
        }

        /// <summary>
        /// Validation spécifique pour les clients
        /// </summary>
        public override async Task<(bool IsValid, List<string> Errors)> ValidateForAddAsync(Client entity)
        {
            var (isValid, errors) = await base.ValidateForAddAsync(entity);

            // Vérifier l'unicité du code client
            if (await CodeExistsAsync(entity.Code))
            {
                errors.Add("Ce code client existe déjà");
                isValid = false;
            }

            // Vérifier l'unicité du NIF pour les entreprises
            if (entity.EstEntreprise && !string.IsNullOrEmpty(entity.NIF))
            {
                if (!await ValidateNIFAsync(entity.NIF))
                {
                    errors.Add("Ce NIF existe déjà");
                    isValid = false;
                }
            }

            // Validation métier spécifique
            if (entity.EstEntreprise && string.IsNullOrEmpty(entity.RaisonSociale))
            {
                errors.Add("La raison sociale est obligatoire pour les entreprises");
                isValid = false;
            }

            if (entity.CreditLimite < 0)
            {
                errors.Add("La limite de crédit ne peut pas être négative");
                isValid = false;
            }

            return (isValid, errors);
        }

        /// <summary>
        /// Validation pour la mise à jour
        /// </summary>
        public override async Task<(bool IsValid, List<string> Errors)> ValidateForUpdateAsync(Client entity)
        {
            var (isValid, errors) = await base.ValidateForUpdateAsync(entity);

            // Vérifier l'unicité du code client (exclure l'entité actuelle)
            if (await CodeExistsAsync(entity.Code, entity.Id))
            {
                errors.Add("Ce code client existe déjà");
                isValid = false;
            }

            // Vérifier l'unicité du NIF pour les entreprises (exclure l'entité actuelle)
            if (entity.EstEntreprise && !string.IsNullOrEmpty(entity.NIF))
            {
                if (!await ValidateNIFAsync(entity.NIF, entity.Id))
                {
                    errors.Add("Ce NIF existe déjà");
                    isValid = false;
                }
            }

            return (isValid, errors);
        }
    }
}
