using System;
using System.Collections.Generic;
using System.Configuration;
using System.Globalization;
using System.Threading.Tasks;

namespace GestionDeStock2024.UTILS
{
    /// <summary>
    /// Gestionnaire de configuration pour l'application
    /// </summary>
    public static class ConfigurationManager
    {
        private static readonly Dictionary<string, object> _cache = new Dictionary<string, object>();
        private static readonly object _lockObject = new object();

        #region Configuration de base

        /// <summary>
        /// Obtient la chaîne de connexion à la base de données
        /// </summary>
        public static string ConnectionString
        {
            get
            {
                return GetConnectionString("DefaultConnection") 
                    ?? "Server=localhost;Database=gestion_supermarche_dz;Uid=root;Pwd=;Charset=utf8mb4;";
            }
        }

        /// <summary>
        /// Obtient une chaîne de connexion par nom
        /// </summary>
        /// <param name="name">Nom de la chaîne de connexion</param>
        /// <returns>Chaîne de connexion</returns>
        public static string GetConnectionString(string name)
        {
            return System.Configuration.ConfigurationManager.ConnectionStrings[name]?.ConnectionString;
        }

        /// <summary>
        /// Obtient un paramètre d'application
        /// </summary>
        /// <param name="key">Clé du paramètre</param>
        /// <param name="defaultValue">Valeur par défaut</param>
        /// <returns>Valeur du paramètre</returns>
        public static string GetAppSetting(string key, string defaultValue = null)
        {
            return System.Configuration.ConfigurationManager.AppSettings[key] ?? defaultValue;
        }

        /// <summary>
        /// Obtient un paramètre d'application typé
        /// </summary>
        /// <typeparam name="T">Type du paramètre</typeparam>
        /// <param name="key">Clé du paramètre</param>
        /// <param name="defaultValue">Valeur par défaut</param>
        /// <returns>Valeur typée du paramètre</returns>
        public static T GetAppSetting<T>(string key, T defaultValue = default(T))
        {
            var value = GetAppSetting(key);
            if (string.IsNullOrEmpty(value))
                return defaultValue;

            try
            {
                return (T)Convert.ChangeType(value, typeof(T), CultureInfo.InvariantCulture);
            }
            catch
            {
                return defaultValue;
            }
        }

        #endregion

        #region Configuration métier

        /// <summary>
        /// Configuration fiscale algérienne
        /// </summary>
        public static class Fiscal
        {
            public static decimal TVANormale => GetAppSetting<decimal>("TVA_Normale", 19.00m);
            public static decimal TVAReduite => GetAppSetting<decimal>("TVA_Reduite", 9.00m);
            public static decimal TimbreSeuilMin => GetAppSetting<decimal>("Timbre_Seuil_Min", 300.00m);
            public static decimal TimbreSeuilMax => GetAppSetting<decimal>("Timbre_Seuil_Max", 30000.00m);
            public static decimal TimbreTaux1 => GetAppSetting<decimal>("Timbre_Taux_1", 1.00m);
            public static decimal TimbreTaux2 => GetAppSetting<decimal>("Timbre_Taux_2", 1.50m);
            public static string DeviseDefaut => GetAppSetting("Devise_Defaut", "DA");
        }

        /// <summary>
        /// Configuration de l'interface utilisateur
        /// </summary>
        public static class Interface
        {
            public static string LangueDefaut => GetAppSetting("Langue_Defaut", "fr");
            public static string FormatDate => GetAppSetting("Format_Date", "dd/MM/yyyy");
            public static string FormatHeure => GetAppSetting("Format_Heure", "HH:mm");
            public static int PrecisionDecimale => GetAppSetting<int>("Precision_Decimale", 2);
            public static int TaillePageDefaut => GetAppSetting<int>("Taille_Page_Defaut", 20);
            public static string ThemeDefaut => GetAppSetting("Theme_Defaut", "Default");
        }

        /// <summary>
        /// Configuration du système
        /// </summary>
        public static class Systeme
        {
            public static bool SauvegardeAuto => GetAppSetting<bool>("Sauvegarde_Auto", true);
            public static int FrequenceSauvegarde => GetAppSetting<int>("Frequence_Sauvegarde", 7);
            public static bool AlertesStockActives => GetAppSetting<bool>("Alertes_Stock_Actives", true);
            public static int TimeoutSession => GetAppSetting<int>("Timeout_Session", 30);
            public static bool LoggingActive => GetAppSetting<bool>("Logging_Active", true);
            public static string NiveauLog => GetAppSetting("Niveau_Log", "Info");
            public static string CheminLogs => GetAppSetting("Chemin_Logs", @".\Logs");
        }

        /// <summary>
        /// Configuration de l'impression
        /// </summary>
        public static class Impression
        {
            public static string ImprimanteDefaut => GetAppSetting("Imprimante_Defaut", "");
            public static string FormatPapier => GetAppSetting("Format_Papier", "A4");
            public static bool ImprimerLogo => GetAppSetting<bool>("Imprimer_Logo", true);
            public static bool ImprimerCodeBarre => GetAppSetting<bool>("Imprimer_CodeBarre", true);
            public static bool ImprimerQRCode => GetAppSetting<bool>("Imprimer_QRCode", false);
            public static int NombreCopies => GetAppSetting<int>("Nombre_Copies", 1);
        }

        /// <summary>
        /// Configuration de sécurité
        /// </summary>
        public static class Securite
        {
            public static int LongueurMotDePasseMin => GetAppSetting<int>("Longueur_MotDePasse_Min", 6);
            public static bool MotDePasseComplexe => GetAppSetting<bool>("MotDePasse_Complexe", false);
            public static int TentativesConnexionMax => GetAppSetting<int>("Tentatives_Connexion_Max", 3);
            public static int DureeVerrouillage => GetAppSetting<int>("Duree_Verrouillage", 15);
            public static bool ChiffrementDonnees => GetAppSetting<bool>("Chiffrement_Donnees", false);
            public static string CleChiffrement => GetAppSetting("Cle_Chiffrement", "");
        }

        #endregion

        #region Gestion du cache

        /// <summary>
        /// Met en cache une valeur
        /// </summary>
        /// <param name="key">Clé</param>
        /// <param name="value">Valeur</param>
        public static void SetCacheValue(string key, object value)
        {
            lock (_lockObject)
            {
                _cache[key] = value;
            }
        }

        /// <summary>
        /// Obtient une valeur du cache
        /// </summary>
        /// <typeparam name="T">Type de la valeur</typeparam>
        /// <param name="key">Clé</param>
        /// <param name="defaultValue">Valeur par défaut</param>
        /// <returns>Valeur du cache</returns>
        public static T GetCacheValue<T>(string key, T defaultValue = default(T))
        {
            lock (_lockObject)
            {
                if (_cache.TryGetValue(key, out var value) && value is T)
                {
                    return (T)value;
                }
                return defaultValue;
            }
        }

        /// <summary>
        /// Supprime une valeur du cache
        /// </summary>
        /// <param name="key">Clé</param>
        public static void RemoveCacheValue(string key)
        {
            lock (_lockObject)
            {
                _cache.Remove(key);
            }
        }

        /// <summary>
        /// Vide le cache
        /// </summary>
        public static void ClearCache()
        {
            lock (_lockObject)
            {
                _cache.Clear();
            }
        }

        #endregion

        #region Validation et utilitaires

        /// <summary>
        /// Valide la configuration
        /// </summary>
        /// <returns>Liste des erreurs de configuration</returns>
        public static List<string> ValidateConfiguration()
        {
            var errors = new List<string>();

            // Validation de la chaîne de connexion
            if (string.IsNullOrEmpty(ConnectionString))
            {
                errors.Add("Chaîne de connexion manquante");
            }

            // Validation des paramètres fiscaux
            if (Fiscal.TVANormale < 0 || Fiscal.TVANormale > 100)
            {
                errors.Add("Taux de TVA normale invalide");
            }

            if (Fiscal.TVAReduite < 0 || Fiscal.TVAReduite > 100)
            {
                errors.Add("Taux de TVA réduite invalide");
            }

            // Validation des paramètres système
            if (Systeme.TimeoutSession <= 0)
            {
                errors.Add("Timeout de session invalide");
            }

            if (Systeme.FrequenceSauvegarde <= 0)
            {
                errors.Add("Fréquence de sauvegarde invalide");
            }

            return errors;
        }

        /// <summary>
        /// Obtient toutes les configurations sous forme de dictionnaire
        /// </summary>
        /// <returns>Dictionnaire des configurations</returns>
        public static Dictionary<string, object> GetAllConfigurations()
        {
            var config = new Dictionary<string, object>();

            // Configuration fiscale
            config["Fiscal.TVANormale"] = Fiscal.TVANormale;
            config["Fiscal.TVAReduite"] = Fiscal.TVAReduite;
            config["Fiscal.TimbreSeuilMin"] = Fiscal.TimbreSeuilMin;
            config["Fiscal.TimbreSeuilMax"] = Fiscal.TimbreSeuilMax;
            config["Fiscal.DeviseDefaut"] = Fiscal.DeviseDefaut;

            // Configuration interface
            config["Interface.LangueDefaut"] = Interface.LangueDefaut;
            config["Interface.FormatDate"] = Interface.FormatDate;
            config["Interface.PrecisionDecimale"] = Interface.PrecisionDecimale;
            config["Interface.TaillePageDefaut"] = Interface.TaillePageDefaut;

            // Configuration système
            config["Systeme.SauvegardeAuto"] = Systeme.SauvegardeAuto;
            config["Systeme.FrequenceSauvegarde"] = Systeme.FrequenceSauvegarde;
            config["Systeme.AlertesStockActives"] = Systeme.AlertesStockActives;
            config["Systeme.TimeoutSession"] = Systeme.TimeoutSession;

            // Configuration impression
            config["Impression.FormatPapier"] = Impression.FormatPapier;
            config["Impression.ImprimerLogo"] = Impression.ImprimerLogo;
            config["Impression.NombreCopies"] = Impression.NombreCopies;

            // Configuration sécurité
            config["Securite.LongueurMotDePasseMin"] = Securite.LongueurMotDePasseMin;
            config["Securite.MotDePasseComplexe"] = Securite.MotDePasseComplexe;
            config["Securite.TentativesConnexionMax"] = Securite.TentativesConnexionMax;

            return config;
        }

        /// <summary>
        /// Sauvegarde la configuration
        /// </summary>
        /// <param name="configurations">Configurations à sauvegarder</param>
        /// <returns>True si la sauvegarde a réussi</returns>
        public static bool SaveConfiguration(Dictionary<string, object> configurations)
        {
            try
            {
                var config = System.Configuration.ConfigurationManager.OpenExeConfiguration(ConfigurationUserLevel.None);

                foreach (var kvp in configurations)
                {
                    if (config.AppSettings.Settings[kvp.Key] != null)
                    {
                        config.AppSettings.Settings[kvp.Key].Value = kvp.Value?.ToString();
                    }
                    else
                    {
                        config.AppSettings.Settings.Add(kvp.Key, kvp.Value?.ToString());
                    }
                }

                config.Save(ConfigurationSaveMode.Modified);
                System.Configuration.ConfigurationManager.RefreshSection("appSettings");

                return true;
            }
            catch
            {
                return false;
            }
        }

        #endregion

        #region Formatage et conversion

        /// <summary>
        /// Formate un montant selon la configuration
        /// </summary>
        /// <param name="montant">Montant à formater</param>
        /// <param name="includeDevise">Inclure la devise</param>
        /// <returns>Montant formaté</returns>
        public static string FormatMontant(decimal montant, bool includeDevise = true)
        {
            var precision = Interface.PrecisionDecimale;
            var format = $"N{precision}";
            var montantFormate = montant.ToString(format, CultureInfo.CurrentCulture);

            if (includeDevise)
            {
                montantFormate += " " + Fiscal.DeviseDefaut;
            }

            return montantFormate;
        }

        /// <summary>
        /// Formate une date selon la configuration
        /// </summary>
        /// <param name="date">Date à formater</param>
        /// <returns>Date formatée</returns>
        public static string FormatDate(DateTime date)
        {
            return date.ToString(Interface.FormatDate, CultureInfo.CurrentCulture);
        }

        /// <summary>
        /// Formate une heure selon la configuration
        /// </summary>
        /// <param name="heure">Heure à formater</param>
        /// <returns>Heure formatée</returns>
        public static string FormatHeure(TimeSpan heure)
        {
            return DateTime.Today.Add(heure).ToString(Interface.FormatHeure, CultureInfo.CurrentCulture);
        }

        /// <summary>
        /// Formate une date et heure selon la configuration
        /// </summary>
        /// <param name="dateHeure">Date et heure à formater</param>
        /// <returns>Date et heure formatées</returns>
        public static string FormatDateHeure(DateTime dateHeure)
        {
            var formatComplet = $"{Interface.FormatDate} {Interface.FormatHeure}";
            return dateHeure.ToString(formatComplet, CultureInfo.CurrentCulture);
        }

        #endregion
    }

    /// <summary>
    /// Classe pour les paramètres de configuration personnalisés
    /// </summary>
    public class CustomConfigurationSection : ConfigurationSection
    {
        [ConfigurationProperty("fiscal")]
        public FiscalConfigurationElement Fiscal
        {
            get { return (FiscalConfigurationElement)this["fiscal"]; }
            set { this["fiscal"] = value; }
        }

        [ConfigurationProperty("interface")]
        public InterfaceConfigurationElement Interface
        {
            get { return (InterfaceConfigurationElement)this["interface"]; }
            set { this["interface"] = value; }
        }
    }

    /// <summary>
    /// Élément de configuration fiscale
    /// </summary>
    public class FiscalConfigurationElement : ConfigurationElement
    {
        [ConfigurationProperty("tvaNormale", DefaultValue = 19.00)]
        public decimal TVANormale
        {
            get { return (decimal)this["tvaNormale"]; }
            set { this["tvaNormale"] = value; }
        }

        [ConfigurationProperty("tvaReduite", DefaultValue = 9.00)]
        public decimal TVAReduite
        {
            get { return (decimal)this["tvaReduite"]; }
            set { this["tvaReduite"] = value; }
        }

        [ConfigurationProperty("deviseDefaut", DefaultValue = "DA")]
        public string DeviseDefaut
        {
            get { return (string)this["deviseDefaut"]; }
            set { this["deviseDefaut"] = value; }
        }
    }

    /// <summary>
    /// Élément de configuration interface
    /// </summary>
    public class InterfaceConfigurationElement : ConfigurationElement
    {
        [ConfigurationProperty("langueDefaut", DefaultValue = "fr")]
        public string LangueDefaut
        {
            get { return (string)this["langueDefaut"]; }
            set { this["langueDefaut"] = value; }
        }

        [ConfigurationProperty("formatDate", DefaultValue = "dd/MM/yyyy")]
        public string FormatDate
        {
            get { return (string)this["formatDate"]; }
            set { this["formatDate"] = value; }
        }

        [ConfigurationProperty("precisionDecimale", DefaultValue = 2)]
        public int PrecisionDecimale
        {
            get { return (int)this["precisionDecimale"]; }
            set { this["precisionDecimale"] = value; }
        }
    }
}
