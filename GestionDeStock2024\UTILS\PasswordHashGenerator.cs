using System;
using BCrypt.Net;

namespace GestionDeStock2024.UTILS
{
    /// <summary>
    /// Générateur de hash pour les mots de passe - Utilitaire de développement
    /// </summary>
    public static class PasswordHashGenerator
    {
        /// <summary>
        /// Génère un hash BCrypt pour un mot de passe donné
        /// </summary>
        public static void GenerateHashForPassword(string password)
        {
            var hash = BCrypt.Net.BCrypt.HashPassword(password, 12);
            Console.WriteLine($"Mot de passe: {password}");
            Console.WriteLine($"Hash BCrypt: {hash}");
            Console.WriteLine($"Vérification: {BCrypt.Net.BCrypt.Verify(password, hash)}");
        }

        /// <summary>
        /// Génère les hash pour les mots de passe par défaut
        /// </summary>
        public static void GenerateDefaultPasswords()
        {
            Console.WriteLine("=== Génération des hash pour les mots de passe par défaut ===");
            Console.WriteLine();
            
            GenerateHashForPassword("admin123");
            Console.WriteLine();
            
            GenerateHashForPassword("manager123");
            Console.WriteLine();
            
            GenerateHashForPassword("vendeur123");
            Console.WriteLine();
        }
    }
}

// Pour générer les hash, vous pouvez utiliser ce code dans une application console temporaire :
/*
using GestionDeStock2024.UTILS;

class Program
{
    static void Main()
    {
        PasswordHashGenerator.GenerateDefaultPasswords();
        Console.ReadKey();
    }
}
*/
