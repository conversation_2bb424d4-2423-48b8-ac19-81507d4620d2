using System;
using System.Drawing;
using System.Windows.Forms;
using GestionDeStock2024.MODELS;
using GestionDeStock2024.UTILS;

namespace GestionDeStock2024.FORMS
{
    /// <summary>
    /// Formulaire principal de l'application
    /// </summary>
    public partial class FrmMain : Form
    {
        private Utilisateur currentUser;
        private Form activeChildForm;

        public FrmMain()
        {
            InitializeComponent();
            InitializeForm();
        }

        public FrmMain(Utilisateur user) : this()
        {
            currentUser = user;
            UpdateUserInterface();
        }

        private void InitializeForm()
        {
            // Configuration du formulaire
            this.WindowState = FormWindowState.Maximized;
            this.IsMdiContainer = true;
            this.KeyPreview = true;

            // Appliquer le thème moderne
            ApplyModernTheme();

            // Initialiser la barre de statut
            InitializeStatusBar();

            // Charger les permissions
            LoadUserPermissions();
        }

        private void ApplyModernTheme()
        {
            // Couleurs modernes
            this.BackColor = Color.FromArgb(240, 240, 240);
            
            // Style du panel principal
            panelMain.BackColor = Color.White;
            panelMain.BorderStyle = BorderStyle.None;

            // Style de la barre latérale
            panelSidebar.BackColor = Color.FromArgb(45, 45, 48);
            panelSidebar.BorderStyle = BorderStyle.None;

            // Style du header
            panelHeader.BackColor = Color.FromArgb(0, 122, 204);
            panelHeader.BorderStyle = BorderStyle.None;

            // Style des labels du header
            lblAppTitle.ForeColor = Color.White;
            lblAppTitle.Font = new Font("Segoe UI", 16, FontStyle.Bold);
            
            lblUserInfo.ForeColor = Color.White;
            lblUserInfo.Font = new Font("Segoe UI", 10);

            // Appliquer le style aux boutons de la sidebar
            ApplySidebarButtonTheme();
        }

        private void ApplySidebarButtonTheme()
        {
            var buttons = new[] 
            {
                btnDashboard, btnArticles, btnClients, btnFournisseurs,
                btnVentes, btnAchats, btnStock, btnInventaire,
                btnCaisse, btnComptabilite, btnRapports, btnParametres,
                btnUtilisateurs, btnSauvegarde, btnAPropos, btnDeconnexion
            };

            foreach (var button in buttons)
            {
                ApplyModernButtonTheme(button);
            }
        }

        private void ApplyModernButtonTheme(Button button)
        {
            button.FlatStyle = FlatStyle.Flat;
            button.FlatAppearance.BorderSize = 0;
            button.BackColor = Color.Transparent;
            button.ForeColor = Color.White;
            button.Font = new Font("Segoe UI", 10, FontStyle.Bold);
            button.TextAlign = ContentAlignment.MiddleLeft;
            button.Cursor = Cursors.Hand;
            button.Height = 45;
            button.Padding = new Padding(15, 0, 0, 0);

            // Événements pour l'effet hover
            button.MouseEnter += (s, e) => {
                button.BackColor = Color.FromArgb(70, 70, 70);
            };
            button.MouseLeave += (s, e) => {
                button.BackColor = Color.Transparent;
            };
        }

        private void InitializeStatusBar()
        {
            statusStrip.BackColor = Color.FromArgb(240, 240, 240);
            statusStrip.ForeColor = Color.FromArgb(64, 64, 64);

            // Mettre à jour les informations de statut
            UpdateStatusBar();

            // Timer pour mettre à jour l'heure
            var timer = new Timer();
            timer.Interval = 1000; // 1 seconde
            timer.Tick += (s, e) => UpdateStatusBar();
            timer.Start();
        }

        private void UpdateStatusBar()
        {
            lblStatusUser.Text = $"Utilisateur: {currentUser?.NomComplet ?? "Non connecté"}";
            lblStatusRole.Text = $"Rôle: {currentUser?.Role ?? "N/A"}";
            lblStatusDate.Text = ConfigurationManager.FormatDateHeure(DateTime.Now);
            lblStatusDatabase.Text = "Base de données: Connectée";
        }

        private void LoadUserPermissions()
        {
            if (currentUser == null) return;

            // Masquer/afficher les boutons selon les permissions
            bool isAdmin = currentUser.Role == "Administrateur";
            bool isManager = currentUser.Role == "Gestionnaire" || isAdmin;

            btnUtilisateurs.Visible = isAdmin;
            btnParametres.Visible = isManager;
            btnSauvegarde.Visible = isManager;
            btnComptabilite.Visible = isManager;
        }

        private void UpdateUserInterface()
        {
            if (currentUser != null)
            {
                lblUserInfo.Text = $"Bienvenue, {currentUser.NomComplet} ({currentUser.Role})";
                this.Text = $"Gestion Supermarché DZ - {currentUser.NomComplet}";
            }
        }

        #region Événements des boutons

        private void btnDashboard_Click(object sender, EventArgs e)
        {
            OpenChildForm(new FrmDashboard(currentUser), "Tableau de bord");
        }

        private void btnArticles_Click(object sender, EventArgs e)
        {
            OpenChildForm(new FrmArticles(currentUser), "Gestion des articles");
        }

        private void btnClients_Click(object sender, EventArgs e)
        {
            OpenChildForm(new FrmClients(currentUser), "Gestion des clients");
        }

        private void btnFournisseurs_Click(object sender, EventArgs e)
        {
            MessageBox.Show("Module Fournisseurs à implémenter", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void btnVentes_Click(object sender, EventArgs e)
        {
            OpenChildForm(new FrmVentes(currentUser), "Point de vente");
        }

        private void btnAchats_Click(object sender, EventArgs e)
        {
            MessageBox.Show("Module Achats à implémenter", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void btnStock_Click(object sender, EventArgs e)
        {
            // Créer un menu contextuel pour le stock
            var contextMenu = new ContextMenuStrip();

            var menuUnits = new ToolStripMenuItem("📏 Unités de mesure");
            menuUnits.Click += (s, args) => OpenChildForm(new FrmUnits(currentUser), "Unités de mesure");
            contextMenu.Items.Add(menuUnits);

            var menuCategories = new ToolStripMenuItem("🏷️ Catégories");
            menuCategories.Click += (s, args) => OpenChildForm(new FrmCategories(currentUser), "Catégories");
            contextMenu.Items.Add(menuCategories);

            var menuWarehouses = new ToolStripMenuItem("🏪 Entrepôts/Magasins");
            menuWarehouses.Click += (s, args) => OpenChildForm(new FrmWarehouses(currentUser), "Entrepôts/Magasins");
            contextMenu.Items.Add(menuWarehouses);

            contextMenu.Items.Add(new ToolStripSeparator());

            var menuStockMovements = new ToolStripMenuItem("📦 Mouvements de stock");
            menuStockMovements.Click += (s, args) => MessageBox.Show("Module Mouvements de stock à implémenter", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
            contextMenu.Items.Add(menuStockMovements);

            // Afficher le menu à la position du bouton
            var btn = sender as Button;
            contextMenu.Show(btn, new Point(0, btn.Height));
        }

        private void btnInventaire_Click(object sender, EventArgs e)
        {
            MessageBox.Show("Module Inventaire à implémenter", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void btnCaisse_Click(object sender, EventArgs e)
        {
            MessageBox.Show("Module Caisse à implémenter", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void btnComptabilite_Click(object sender, EventArgs e)
        {
            MessageBox.Show("Module Comptabilité à implémenter", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void btnRapports_Click(object sender, EventArgs e)
        {
            MessageBox.Show("Module Rapports à implémenter", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void btnParametres_Click(object sender, EventArgs e)
        {
            MessageBox.Show("Module Paramètres système à implémenter", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void btnUtilisateurs_Click(object sender, EventArgs e)
        {
            MessageBox.Show("Module Utilisateurs à implémenter", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void btnSauvegarde_Click(object sender, EventArgs e)
        {
            MessageBox.Show("Module Sauvegarde à implémenter", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void btnAPropos_Click(object sender, EventArgs e)
        {
            MessageBox.Show("Gestion Supermarché DZ\nVersion 1.0\n\n© 2024 Tous droits réservés", "À propos", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void btnDeconnexion_Click(object sender, EventArgs e)
        {
            var result = MessageBox.Show(
                "Êtes-vous sûr de vouloir vous déconnecter ?",
                "Confirmation de déconnexion",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                this.Hide();
                var frmLogin = new FrmLogin();
                if (frmLogin.ShowDialog() == DialogResult.OK)
                {
                    currentUser = frmLogin.CurrentUser;
                    UpdateUserInterface();
                    LoadUserPermissions();
                    this.Show();
                }
                else
                {
                    Application.Exit();
                }
            }
        }

        #endregion

        #region Gestion des formulaires enfants

        private void OpenChildForm(Form childForm, string title)
        {
            // Fermer le formulaire actif s'il existe
            if (activeChildForm != null)
            {
                activeChildForm.Close();
            }

            // Configurer le nouveau formulaire enfant
            activeChildForm = childForm;
            childForm.TopLevel = false;
            childForm.FormBorderStyle = FormBorderStyle.None;
            childForm.Dock = DockStyle.Fill;
            
            // Ajouter au panel principal
            panelContent.Controls.Add(childForm);
            panelContent.Tag = childForm;
            
            // Afficher le formulaire
            childForm.BringToFront();
            childForm.Show();

            // Mettre à jour le titre
            lblCurrentForm.Text = title;
        }

        #endregion

        #region Raccourcis clavier

        private void FrmMain_KeyDown(object sender, KeyEventArgs e)
        {
            // Raccourcis clavier
            if (e.Control)
            {
                switch (e.KeyCode)
                {
                    case Keys.D1:
                        btnDashboard_Click(sender, e);
                        break;
                    case Keys.D2:
                        btnArticles_Click(sender, e);
                        break;
                    case Keys.D3:
                        btnClients_Click(sender, e);
                        break;
                    case Keys.D4:
                        btnVentes_Click(sender, e);
                        break;
                    case Keys.F1:
                        btnAPropos_Click(sender, e);
                        break;
                    case Keys.Q:
                        btnDeconnexion_Click(sender, e);
                        break;
                }
            }

            if (e.KeyCode == Keys.F11)
            {
                // Basculer en plein écran
                ToggleFullScreen();
            }
        }

        private void ToggleFullScreen()
        {
            if (this.WindowState == FormWindowState.Maximized)
            {
                this.WindowState = FormWindowState.Normal;
                this.FormBorderStyle = FormBorderStyle.Sizable;
            }
            else
            {
                this.WindowState = FormWindowState.Maximized;
                this.FormBorderStyle = FormBorderStyle.None;
            }
        }

        #endregion

        private void FrmMain_Load(object sender, EventArgs e)
        {
            // Ouvrir le tableau de bord par défaut
            btnDashboard_Click(sender, e);
        }

        private void FrmMain_FormClosing(object sender, FormClosingEventArgs e)
        {
            var result = MessageBox.Show(
                "Êtes-vous sûr de vouloir fermer l'application ?",
                "Confirmation de fermeture",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);

            if (result == DialogResult.No)
            {
                e.Cancel = true;
            }
        }
    }


}
