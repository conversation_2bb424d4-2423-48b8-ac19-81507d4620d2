using GestionDeStock2024.DATA;
using GestionDeStock2024.UTILS;
using MySqlConnector;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Drawing;
using System.IO;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace GestionDeStock2024.FORMS
{
    public partial class FrmDatabase : Form
    {
        private bool isTestingConnection = false;
        private bool isDatabaseCreated = false;

        public FrmDatabase()
        {
            InitializeComponent();
            InitializeForm();
        }

        private void InitializeForm()
        {
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.StartPosition = FormStartPosition.CenterScreen;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.ShowInTaskbar = true;

            ApplyModernTheme();

            LoadDefaultValues();
        }

        private void ApplyModernTheme()
        {
            this.BackColor = Color.FromArgb(240, 240, 240);

            panelMain.BackColor = Color.White;
            panelMain.BorderStyle = BorderStyle.None;

            groupBoxConnection.ForeColor = Color.FromArgb(64, 64, 64);
            groupBoxConnection.Font = new Font("Segoe UI", 10, FontStyle.Bold);

            groupBoxDatabase.ForeColor = Color.FromArgb(64, 64, 64);
            groupBoxDatabase.Font = new Font("Segoe UI", 10, FontStyle.Bold);

            foreach (Control control in this.Controls)
            {
                ApplyControlTheme(control);
            }
        }

        private void ApplyControlTheme(Control parent)
        {
            foreach (Control control in parent.Controls)
            {
                if (control is Label label)
                {
                    label.ForeColor = Color.FromArgb(64, 64, 64);
                    label.Font = new Font("Segoe UI", 9);
                }
                else if (control is TextBox textBox)
                {
                    textBox.BorderStyle = BorderStyle.FixedSingle;
                    textBox.Font = new Font("Segoe UI", 9);
                    textBox.BackColor = Color.White;
                    textBox.ForeColor = Color.FromArgb(64, 64, 64);
                }
                else if (control is Button button)
                {
                    ApplyButtonTheme(button);
                }
                else if (control is CheckBox checkBox)
                {
                    checkBox.ForeColor = Color.FromArgb(64, 64, 64);
                    checkBox.Font = new Font("Segoe UI", 9);
                }

                if (control.HasChildren)
                {
                    ApplyControlTheme(control);
                }
            }
        }

        private void ApplyButtonTheme(Button button)
        {
            button.FlatStyle = FlatStyle.Flat;
            button.Font = new Font("Segoe UI", 9, FontStyle.Bold);
            button.Cursor = Cursors.Hand;

            if (button == btnTestConnection)
            {
                button.BackColor = Color.FromArgb(0, 122, 204);
                button.ForeColor = Color.White;
                button.FlatAppearance.BorderSize = 0;
            }
            else if (button == btnCreateDatabase)
            {
                button.BackColor = Color.FromArgb(16, 124, 16);
                button.ForeColor = Color.White;
                button.FlatAppearance.BorderSize = 0;
            }
            else if (button == btnSave)
            {
                button.BackColor = Color.FromArgb(46, 125, 50);
                button.ForeColor = Color.White;
                button.FlatAppearance.BorderSize = 0;
            }
            else if (button == btnCancel)
            {
                button.BackColor = Color.FromArgb(158, 158, 158);
                button.ForeColor = Color.White;
                button.FlatAppearance.BorderSize = 0;
            }
        }

        private void LoadDefaultValues()
        {
            txtServer.Text = "localhost";
            txtPort.Text = "3306";
            txtUsername.Text = "root";
            txtPassword.Text = "";
            txtDatabaseName.Text = "gestion_supermarche_dz";
            chkCreateDatabase.Checked = true;
        }

        private async void btnTestConnection_Click(object sender, EventArgs e)
        {
            if (isTestingConnection) return;

            await TestConnection();
        }

        private async Task TestConnection()
        {
            isTestingConnection = true;
            btnTestConnection.Enabled = false;
            btnTestConnection.Text = "Test en cours...";
            lblConnectionStatus.Text = "Test de connexion en cours...";
            lblConnectionStatus.ForeColor = Color.FromArgb(255, 152, 0);

            try
            {
                var connectionString = BuildConnectionString(false);

                using (var connection = new MySqlConnection(connectionString))
                {
                    await connection.OpenAsync();

                    lblConnectionStatus.Text = "✓ Connexion au serveur réussie !";
                    lblConnectionStatus.ForeColor = Color.FromArgb(76, 175, 80);

                    btnCreateDatabase.Enabled = true;

                    await CheckDatabaseExists(connection);
                }
            }
            catch (Exception ex)
            {
                lblConnectionStatus.Text = $"✗ Erreur de connexion : {ex.Message}";
                lblConnectionStatus.ForeColor = Color.FromArgb(244, 67, 54);
                btnCreateDatabase.Enabled = false;
            }
            finally
            {
                isTestingConnection = false;
                btnTestConnection.Enabled = true;
                btnTestConnection.Text = "Tester la connexion";
            }
        }

        private async Task CheckDatabaseExists(MySqlConnection connection)
        {
            try
            {
                var command = new MySqlCommand(
                    "SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = @dbName",
                    connection);
                command.Parameters.AddWithValue("@dbName", txtDatabaseName.Text);

                var result = await command.ExecuteScalarAsync();

                if (result != null)
                {
                    lblDatabaseStatus.Text = "✓ Base de données trouvée !";
                    lblDatabaseStatus.ForeColor = Color.FromArgb(76, 175, 80);
                    isDatabaseCreated = true;
                    btnSave.Enabled = true;
                    btnCreateDatabase.Text = "Base de données existe";
                    btnCreateDatabase.Enabled = false;
                }
                else
                {
                    lblDatabaseStatus.Text = "Base de données non trouvée";
                    lblDatabaseStatus.ForeColor = Color.FromArgb(255, 152, 0);
                    isDatabaseCreated = false;
                    btnSave.Enabled = false;
                    btnCreateDatabase.Text = "Créer la base de données";
                    btnCreateDatabase.Enabled = true;
                }
            }
            catch (Exception ex)
            {
                lblDatabaseStatus.Text = $"Erreur lors de la vérification : {ex.Message}";
                lblDatabaseStatus.ForeColor = Color.FromArgb(244, 67, 54);
            }
        }

        private async void btnCreateDatabase_Click(object sender, EventArgs e)
        {
            if (!chkCreateDatabase.Checked)
            {
                MessageBox.Show(
                    "Veuillez cocher 'Créer la base de données si elle n'existe pas' pour continuer.",
                    "Confirmation requise",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Warning);
                return;
            }

            await CreateDatabase();
        }

        private async Task CreateDatabase()
        {
            btnCreateDatabase.Enabled = false;
            btnCreateDatabase.Text = "Création en cours...";
            lblDatabaseStatus.Text = "Création de la base de données...";
            lblDatabaseStatus.ForeColor = Color.FromArgb(255, 152, 0);

            try
            {
                var connectionString = BuildConnectionString(false);

                using (var connection = new MySqlConnection(connectionString))
                {
                    await connection.OpenAsync();

                    var createDbCommand = new MySqlCommand(
                        $"CREATE DATABASE IF NOT EXISTS `{txtDatabaseName.Text}` " +
                        "CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci",
                        connection);

                    await createDbCommand.ExecuteNonQueryAsync();

                    lblDatabaseStatus.Text = "✓ Base de données créée avec succès !";
                    lblDatabaseStatus.ForeColor = Color.FromArgb(76, 175, 80);

                    isDatabaseCreated = true;
                    btnSave.Enabled = true;
                    btnCreateDatabase.Text = "✓ Base créée";

                    await CreateBaseTables(connection);
                }
            }
            catch (Exception ex)
            {
                lblDatabaseStatus.Text = $"✗ Erreur lors de la création : {ex.Message}";
                lblDatabaseStatus.ForeColor = Color.FromArgb(244, 67, 54);
                btnCreateDatabase.Enabled = true;
                btnCreateDatabase.Text = "Créer la base de données";
            }
        }

        private async Task CreateBaseTables(MySqlConnection connection)
        {
            try
            {
                // Lire le script SQL complet
                var sqlScriptPath = Path.Combine(Application.StartupPath, "DB", "GestionSupermarche_Database.sql");

                if (!File.Exists(sqlScriptPath))
                {
                    // Si le fichier n'existe pas, créer les tables de base
                    await CreateMinimalTables(connection);
                    return;
                }

                var sqlScript = File.ReadAllText(sqlScriptPath);

                // Diviser le script en commandes individuelles
                var commands = sqlScript.Split(new[] { ";\r\n", ";\n", ";" }, StringSplitOptions.RemoveEmptyEntries);

                foreach (var commandText in commands)
                {
                    var cleanCommand = commandText.Trim();
                    if (string.IsNullOrEmpty(cleanCommand) || cleanCommand.StartsWith("--") || cleanCommand.StartsWith("/*"))
                        continue;

                    // Remplacer le nom de la base de données si nécessaire
                    cleanCommand = cleanCommand.Replace("gestion_supermarche_dz", txtDatabaseName.Text);

                    try
                    {
                        var command = new MySqlCommand(cleanCommand, connection);
                        await command.ExecuteNonQueryAsync();
                    }
                    catch (Exception cmdEx)
                    {
                        // Ignorer les erreurs de tables déjà existantes
                        if (!cmdEx.Message.Contains("already exists") && !cmdEx.Message.Contains("Duplicate"))
                        {
                            throw;
                        }
                    }
                }

                lblDatabaseStatus.Text = "✓ Base de données complète créée avec succès !";
            }
            catch (Exception ex)
            {
                lblDatabaseStatus.Text = $"Erreur lors de la création : {ex.Message}";
                lblDatabaseStatus.ForeColor = Color.FromArgb(244, 67, 54);

                // Essayer de créer les tables minimales en cas d'échec
                await CreateMinimalTables(connection);
            }
        }

        private async Task CreateMinimalTables(MySqlConnection connection)
        {
            try
            {
                // Sélectionner la base de données
                var useDbCommand = new MySqlCommand($"USE `{txtDatabaseName.Text}`", connection);
                await useDbCommand.ExecuteNonQueryAsync();

                // Créer les tables minimales pour le fonctionnement de base
                var createTablesScript = @"
                    CREATE TABLE IF NOT EXISTS `entreprises` (
                        `id` int NOT NULL AUTO_INCREMENT,
                        `nom_entreprise` varchar(255) NOT NULL,
                        `raison_sociale` varchar(255) DEFAULT NULL,
                        `nif` varchar(20) DEFAULT NULL,
                        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
                        `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                        PRIMARY KEY (`id`)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

                    CREATE TABLE IF NOT EXISTS `utilisateurs` (
                        `id` int NOT NULL AUTO_INCREMENT,
                        `nom_utilisateur` varchar(100) NOT NULL,
                        `mot_de_passe` varchar(255) NOT NULL,
                        `nom_complet` varchar(255) NOT NULL,
                        `email` varchar(100) DEFAULT NULL,
                        `role` varchar(50) DEFAULT 'Utilisateur',
                        `statut` varchar(20) DEFAULT 'Actif',
                        `derniere_connexion` timestamp NULL,
                        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
                        `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                        PRIMARY KEY (`id`),
                        UNIQUE KEY `nom_utilisateur` (`nom_utilisateur`)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

                    INSERT IGNORE INTO `utilisateurs` (`nom_utilisateur`, `mot_de_passe`, `nom_complet`, `role`)
                    VALUES ('admin', 'admin123', 'Administrateur', 'Administrateur');
                ";

                var createCommand = new MySqlCommand(createTablesScript, connection);
                await createCommand.ExecuteNonQueryAsync();

                lblDatabaseStatus.Text = "✓ Tables de base créées !";
            }
            catch (Exception ex)
            {
                lblDatabaseStatus.Text = $"Avertissement : {ex.Message}";
                lblDatabaseStatus.ForeColor = Color.FromArgb(255, 152, 0);
            }
        }

        private string BuildConnectionString(bool includeDatabase = true)
        {
            var builder = new MySqlConnectionStringBuilder
            {
                Server = txtServer.Text.Trim(),
                Port = uint.Parse(txtPort.Text.Trim()),
                UserID = txtUsername.Text.Trim(),
                Password = txtPassword.Text,
                CharacterSet = "utf8mb4"
            };

            if (includeDatabase && !string.IsNullOrEmpty(txtDatabaseName.Text))
            {
                builder.Database = txtDatabaseName.Text.Trim();
            }

            return builder.ConnectionString;
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            if (!isDatabaseCreated)
            {
                MessageBox.Show(
                    "Veuillez d'abord créer ou vérifier la base de données.",
                    "Base de données requise",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Warning);
                return;
            }

            try
            {
                var connectionString = BuildConnectionString(true);

                SaveConnectionString(connectionString);

                MessageBox.Show(
                    "Configuration sauvegardée avec succès !",
                    "Succès",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"Erreur lors de la sauvegarde : {ex.Message}",
                    "Erreur",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
        }

        private void SaveConnectionString(string connectionString)
        {
            try
            {
                // Sauvegarder dans App.config
                var config = System.Configuration.ConfigurationManager.OpenExeConfiguration(System.Configuration.ConfigurationUserLevel.None);

                // Mettre à jour ou ajouter la chaîne de connexion
                if (config.ConnectionStrings.ConnectionStrings["DefaultConnection"] != null)
                {
                    config.ConnectionStrings.ConnectionStrings["DefaultConnection"].ConnectionString = connectionString;
                }
                else
                {
                    config.ConnectionStrings.ConnectionStrings.Add(new System.Configuration.ConnectionStringSettings(
                        "DefaultConnection",
                        connectionString,
                        "MySql.Data.MySqlClient"));
                }

                config.Save(System.Configuration.ConfigurationSaveMode.Modified);
                System.Configuration.ConfigurationManager.RefreshSection("connectionStrings");

                // Aussi sauvegarder via notre ConfigurationManager personnalisé
                var customConfig = new Dictionary<string, object>
                {
                    ["ConnectionString"] = connectionString
                };

                UTILS.ConfigurationManager.SaveConfiguration(customConfig);
            }
            catch (Exception ex)
            {
                throw new Exception($"Erreur lors de la sauvegarde de la configuration : {ex.Message}");
            }
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private void txtPort_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (!char.IsControl(e.KeyChar) && !char.IsDigit(e.KeyChar))
            {
                e.Handled = true;
            }
        }

        private void FrmDatabase_Load(object sender, EventArgs e)
        {
            txtServer.Focus();
        }
    }
}
