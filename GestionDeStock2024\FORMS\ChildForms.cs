using System;
using System.Drawing;
using System.Windows.Forms;

namespace GestionDeStock2024.FORMS
{
    // Classes temporaires pour les formulaires enfants (à remplacer par les vrais formulaires)
    
    public partial class FrmDashboard : Form 
    { 
        public FrmDashboard() 
        { 
            this.Text = "Tableau de bord"; 
            this.BackColor = Color.White;
            this.Size = new Size(800, 600);
            
            var label = new Label
            {
                Text = "🏠 Tableau de bord\n\nBienvenue dans votre système de gestion de supermarché !",
                Font = new Font("Segoe UI", 16, FontStyle.Bold),
                ForeColor = Color.FromArgb(64, 64, 64),
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };
            this.Controls.Add(label);
        } 
    }
    
    public partial class FrmArticles : Form 
    { 
        public FrmArticles() 
        { 
            this.Text = "Articles"; 
            this.BackColor = Color.White;
            this.Size = new Size(800, 600);
            
            var label = new Label
            {
                Text = "📦 Gestion des Articles\n\nModule de gestion des articles et produits",
                Font = new Font("Segoe UI", 16, FontStyle.Bold),
                ForeColor = Color.FromArgb(64, 64, 64),
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };
            this.Controls.Add(label);
        } 
    }
    
    public partial class FrmClients : Form 
    { 
        public FrmClients() 
        { 
            this.Text = "Clients"; 
            this.BackColor = Color.White;
            this.Size = new Size(800, 600);
            
            var label = new Label
            {
                Text = "👤 Gestion des Clients\n\nModule de gestion de la clientèle",
                Font = new Font("Segoe UI", 16, FontStyle.Bold),
                ForeColor = Color.FromArgb(64, 64, 64),
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };
            this.Controls.Add(label);
        } 
    }
    
    public partial class FrmFournisseurs : Form 
    { 
        public FrmFournisseurs() 
        { 
            this.Text = "Fournisseurs"; 
            this.BackColor = Color.White;
            this.Size = new Size(800, 600);
            
            var label = new Label
            {
                Text = "🏭 Gestion des Fournisseurs\n\nModule de gestion des fournisseurs",
                Font = new Font("Segoe UI", 16, FontStyle.Bold),
                ForeColor = Color.FromArgb(64, 64, 64),
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };
            this.Controls.Add(label);
        } 
    }
    
    public partial class FrmVentes : Form 
    { 
        public FrmVentes() 
        { 
            this.Text = "Ventes"; 
            this.BackColor = Color.White;
            this.Size = new Size(800, 600);
            
            var label = new Label
            {
                Text = "🛍️ Point de Vente\n\nModule de gestion des ventes et facturation",
                Font = new Font("Segoe UI", 16, FontStyle.Bold),
                ForeColor = Color.FromArgb(64, 64, 64),
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };
            this.Controls.Add(label);
        } 
    }
    
    public partial class FrmAchats : Form 
    { 
        public FrmAchats() 
        { 
            this.Text = "Achats"; 
            this.BackColor = Color.White;
            this.Size = new Size(800, 600);
            
            var label = new Label
            {
                Text = "🛒 Gestion des Achats\n\nModule de gestion des achats et approvisionnement",
                Font = new Font("Segoe UI", 16, FontStyle.Bold),
                ForeColor = Color.FromArgb(64, 64, 64),
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };
            this.Controls.Add(label);
        } 
    }
    
    public partial class FrmStock : Form 
    { 
        public FrmStock() 
        { 
            this.Text = "Stock"; 
            this.BackColor = Color.White;
            this.Size = new Size(800, 600);
            
            var label = new Label
            {
                Text = "📦 Gestion du Stock\n\nModule de gestion et suivi du stock",
                Font = new Font("Segoe UI", 16, FontStyle.Bold),
                ForeColor = Color.FromArgb(64, 64, 64),
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };
            this.Controls.Add(label);
        } 
    }
    
    public partial class FrmInventaire : Form 
    { 
        public FrmInventaire() 
        { 
            this.Text = "Inventaire"; 
            this.BackColor = Color.White;
            this.Size = new Size(800, 600);
            
            var label = new Label
            {
                Text = "📋 Inventaire\n\nModule de gestion des inventaires",
                Font = new Font("Segoe UI", 16, FontStyle.Bold),
                ForeColor = Color.FromArgb(64, 64, 64),
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };
            this.Controls.Add(label);
        } 
    }
    
    public partial class FrmCaisse : Form 
    { 
        public FrmCaisse() 
        { 
            this.Text = "Caisse"; 
            this.BackColor = Color.White;
            this.Size = new Size(800, 600);
            
            var label = new Label
            {
                Text = "🏦 Gestion de Caisse\n\nModule de gestion des caisses et encaissements",
                Font = new Font("Segoe UI", 16, FontStyle.Bold),
                ForeColor = Color.FromArgb(64, 64, 64),
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };
            this.Controls.Add(label);
        } 
    }
    
    public partial class FrmComptabilite : Form 
    { 
        public FrmComptabilite() 
        { 
            this.Text = "Comptabilité"; 
            this.BackColor = Color.White;
            this.Size = new Size(800, 600);
            
            var label = new Label
            {
                Text = "💰 Comptabilité\n\nModule de gestion comptable et financière",
                Font = new Font("Segoe UI", 16, FontStyle.Bold),
                ForeColor = Color.FromArgb(64, 64, 64),
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };
            this.Controls.Add(label);
        } 
    }
    
    public partial class FrmRapports : Form 
    { 
        public FrmRapports() 
        { 
            this.Text = "Rapports"; 
            this.BackColor = Color.White;
            this.Size = new Size(800, 600);
            
            var label = new Label
            {
                Text = "📊 Rapports et Statistiques\n\nModule de génération de rapports",
                Font = new Font("Segoe UI", 16, FontStyle.Bold),
                ForeColor = Color.FromArgb(64, 64, 64),
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };
            this.Controls.Add(label);
        } 
    }
    
    public partial class FrmParametres : Form 
    { 
        public FrmParametres() 
        { 
            this.Text = "Paramètres"; 
            this.BackColor = Color.White;
            this.Size = new Size(800, 600);
            
            var label = new Label
            {
                Text = "⚙️ Paramètres Système\n\nConfiguration et paramétrage du système",
                Font = new Font("Segoe UI", 16, FontStyle.Bold),
                ForeColor = Color.FromArgb(64, 64, 64),
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };
            this.Controls.Add(label);
        } 
    }
    
    public partial class FrmUtilisateurs : Form 
    { 
        public FrmUtilisateurs() 
        { 
            this.Text = "Utilisateurs"; 
            this.BackColor = Color.White;
            this.Size = new Size(800, 600);
            
            var label = new Label
            {
                Text = "👥 Gestion des Utilisateurs\n\nModule de gestion des utilisateurs et permissions",
                Font = new Font("Segoe UI", 16, FontStyle.Bold),
                ForeColor = Color.FromArgb(64, 64, 64),
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };
            this.Controls.Add(label);
        } 
    }
    
    public partial class FrmSauvegarde : Form 
    { 
        public FrmSauvegarde() 
        { 
            this.Text = "Sauvegarde"; 
            this.BackColor = Color.White;
            this.Size = new Size(800, 600);
            
            var label = new Label
            {
                Text = "💾 Sauvegarde et Restauration\n\nModule de sauvegarde et restauration des données",
                Font = new Font("Segoe UI", 16, FontStyle.Bold),
                ForeColor = Color.FromArgb(64, 64, 64),
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };
            this.Controls.Add(label);
        } 
    }
    
    public partial class FrmAPropos : Form 
    { 
        public FrmAPropos() 
        { 
            this.Text = "À propos"; 
            this.BackColor = Color.White; 
            this.Size = new Size(400, 300); 
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            
            var label = new Label
            {
                Text = "ℹ️ À Propos\n\nGestion Supermarché DZ\nVersion 1.0\n\n© 2024 Tous droits réservés",
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                ForeColor = Color.FromArgb(64, 64, 64),
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };
            this.Controls.Add(label);
        } 
    }
}
