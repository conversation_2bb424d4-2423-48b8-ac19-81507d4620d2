using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using GestionDeStock2024.MODELS;
using GestionDeStock2024.DATA.Repositories;

namespace GestionDeStock2024.DATA.Services
{
    /// <summary>
    /// Interface pour le service de gestion des ventes
    /// </summary>
    public interface IVenteService : IMasterService<FactureVente>
    {
        // Gestion des factures
        Task<ServiceResult<FactureVente>> CreateFactureAsync(FactureVente facture, IEnumerable<DetailFactureVente> details, int userId);
        Task<ServiceResult<bool>> ValidateFactureAsync(int factureId, int userId);
        Task<ServiceResult<bool>> CancelFactureAsync(int factureId, int userId, string motif);
        Task<FactureVente> GetFactureWithDetailsAsync(int factureId);
        
        // Gestion des paiements
        Task<ServiceResult<bool>> AddPaiementAsync(int factureId, decimal montant, int modePaiementId, int userId);
        Task<ServiceResult<bool>> CalculateTimbreFiscalAsync(FactureVente facture);
        
        // Recherche et filtrage
        Task<IEnumerable<FactureVente>> GetFacturesByClientAsync(int clientId);
        Task<IEnumerable<FactureVente>> GetFacturesUnpaidAsync();
        Task<IEnumerable<FactureVente>> GetFacturesByDateRangeAsync(DateTime dateDebut, DateTime dateFin);
        
        // Statistiques et rapports
        Task<decimal> GetChiffreAffairesAsync(DateTime? dateDebut = null, DateTime? dateFin = null);
        Task<IEnumerable<object>> GetVentesStatisticsAsync(DateTime dateDebut, DateTime dateFin);
        Task<IEnumerable<object>> GetTopClientsAsync(int count = 10);
        
        // Utilitaires
        Task<string> GenerateNextFactureNumberAsync();
        Task<ServiceResult<bool>> ValidateStockAvailabilityAsync(IEnumerable<DetailFactureVente> details);
    }

    /// <summary>
    /// Service de gestion des ventes
    /// </summary>
    public class VenteService : BaseService<FactureVente>, IVenteService
    {
        private readonly IFactureVenteRepository _factureRepository;
        private readonly IArticleService _articleService;
        private readonly IClientRepository _clientRepository;

        public VenteService(
            IFactureVenteRepository factureRepository,
            IArticleService articleService,
            IClientRepository clientRepository) : base(factureRepository)
        {
            _factureRepository = factureRepository;
            _articleService = articleService;
            _clientRepository = clientRepository;
        }

        #region Gestion des factures

        public async Task<ServiceResult<FactureVente>> CreateFactureAsync(FactureVente facture, IEnumerable<DetailFactureVente> details, int userId)
        {
            try
            {
                // Validation des données
                var validationResult = await ValidateFactureDataAsync(facture, details);
                if (!validationResult.Success)
                {
                    return ServiceResult<FactureVente>.ErrorResult("Validation échouée", validationResult.Errors);
                }

                // Vérifier la disponibilité du stock
                var stockValidation = await ValidateStockAvailabilityAsync(details);
                if (!stockValidation.Success)
                {
                    return ServiceResult<FactureVente>.ErrorResult("Stock insuffisant", stockValidation.Errors);
                }

                // Générer le numéro de facture
                if (string.IsNullOrEmpty(facture.NumeroFacture))
                {
                    facture.NumeroFacture = await GenerateNextFactureNumberAsync();
                }

                // Calculer les totaux
                await CalculateFactureTotalsAsync(facture, details);

                // Calculer le timbre fiscal
                await CalculateTimbreFiscalAsync(facture);

                // Créer la facture avec ses détails
                var success = await _factureRepository.AddWithDetailsAsync(facture, details);
                
                if (success)
                {
                    // Récupérer la facture créée avec ses détails
                    var createdFacture = await GetFactureWithDetailsAsync(facture.Id);
                    return ServiceResult<FactureVente>.SuccessResult(createdFacture, "Facture créée avec succès");
                }
                else
                {
                    return ServiceResult<FactureVente>.ErrorResult("Échec de la création de la facture");
                }
            }
            catch (Exception ex)
            {
                return ServiceResult<FactureVente>.ErrorResult(ex, "Erreur lors de la création de la facture");
            }
        }

        public async Task<ServiceResult<bool>> ValidateFactureAsync(int factureId, int userId)
        {
            try
            {
                var facture = await GetByIdAsync(factureId);
                if (facture == null)
                {
                    return ServiceResult<bool>.ErrorResult("Facture non trouvée");
                }

                if (facture.Statut != "Brouillon")
                {
                    return ServiceResult<bool>.ErrorResult("Seules les factures en brouillon peuvent être validées");
                }

                var success = await _factureRepository.ValidateFactureAsync(factureId, userId);
                return success 
                    ? ServiceResult<bool>.SuccessResult(true, "Facture validée avec succès")
                    : ServiceResult<bool>.ErrorResult("Échec de la validation de la facture");
            }
            catch (Exception ex)
            {
                return ServiceResult<bool>.ErrorResult(ex, "Erreur lors de la validation de la facture");
            }
        }

        public async Task<ServiceResult<bool>> CancelFactureAsync(int factureId, int userId, string motif)
        {
            try
            {
                var facture = await GetByIdAsync(factureId);
                if (facture == null)
                {
                    return ServiceResult<bool>.ErrorResult("Facture non trouvée");
                }

                if (facture.Statut == "Annulée")
                {
                    return ServiceResult<bool>.ErrorResult("La facture est déjà annulée");
                }

                var success = await _factureRepository.CancelFactureAsync(factureId, userId, motif);
                return success 
                    ? ServiceResult<bool>.SuccessResult(true, "Facture annulée avec succès")
                    : ServiceResult<bool>.ErrorResult("Échec de l'annulation de la facture");
            }
            catch (Exception ex)
            {
                return ServiceResult<bool>.ErrorResult(ex, "Erreur lors de l'annulation de la facture");
            }
        }

        public async Task<FactureVente> GetFactureWithDetailsAsync(int factureId)
        {
            try
            {
                return await _factureRepository.GetWithDetailsAsync(factureId);
            }
            catch (Exception ex)
            {
                throw new ServiceException($"Erreur lors de la récupération de la facture {factureId} avec détails", ex);
            }
        }

        #endregion

        #region Gestion des paiements

        public async Task<ServiceResult<bool>> AddPaiementAsync(int factureId, decimal montant, int modePaiementId, int userId)
        {
            try
            {
                var facture = await GetByIdAsync(factureId);
                if (facture == null)
                {
                    return ServiceResult<bool>.ErrorResult("Facture non trouvée");
                }

                if (montant <= 0)
                {
                    return ServiceResult<bool>.ErrorResult("Le montant du paiement doit être positif");
                }

                if (montant > facture.MontantRestant)
                {
                    return ServiceResult<bool>.ErrorResult("Le montant du paiement ne peut pas dépasser le montant restant");
                }

                // Mettre à jour les montants de la facture
                facture.MontantPaye += montant;
                facture.MontantRestant -= montant;

                // Mettre à jour le statut de paiement
                if (facture.MontantRestant <= 0)
                {
                    facture.StatutPaiement = "Payé";
                }
                else
                {
                    facture.StatutPaiement = "Partiellement_payé";
                }

                var success = await UpdateAsync(facture);
                return success.Success 
                    ? ServiceResult<bool>.SuccessResult(true, "Paiement ajouté avec succès")
                    : ServiceResult<bool>.ErrorResult("Échec de l'ajout du paiement");
            }
            catch (Exception ex)
            {
                return ServiceResult<bool>.ErrorResult(ex, "Erreur lors de l'ajout du paiement");
            }
        }

        public async Task<ServiceResult<bool>> CalculateTimbreFiscalAsync(FactureVente facture)
        {
            try
            {
                // Calculer le timbre fiscal selon la réglementation algérienne
                var montantApresRemise = facture.MontantTTC - facture.RemiseGlobale;
                
                if (montantApresRemise <= 300)
                {
                    facture.TimbreFiscal = 0;
                }
                else if (montantApresRemise <= 30000)
                {
                    facture.TimbreFiscal = 1;
                }
                else if (montantApresRemise <= 100000)
                {
                    facture.TimbreFiscal = Math.Ceiling((montantApresRemise - 30000) / 100) * 1.5m;
                }
                else
                {
                    facture.TimbreFiscal = Math.Ceiling((decimal)(100000 - 30000) / 100) * 1.5m;
                }

                // Recalculer le montant total
                facture.MontantTotal = montantApresRemise + facture.TimbreFiscal;
                facture.MontantRestant = facture.MontantTotal - facture.MontantPaye;

                return ServiceResult<bool>.SuccessResult(true, "Timbre fiscal calculé");
            }
            catch (Exception ex)
            {
                return ServiceResult<bool>.ErrorResult(ex, "Erreur lors du calcul du timbre fiscal");
            }
        }

        #endregion

        #region Recherche et filtrage

        public async Task<IEnumerable<FactureVente>> GetFacturesByClientAsync(int clientId)
        {
            try
            {
                return await _factureRepository.GetFacturesByClientAsync(clientId);
            }
            catch (Exception ex)
            {
                throw new ServiceException($"Erreur lors de la récupération des factures du client {clientId}", ex);
            }
        }

        public async Task<IEnumerable<FactureVente>> GetFacturesUnpaidAsync()
        {
            try
            {
                return await _factureRepository.GetFacturesUnpaidAsync();
            }
            catch (Exception ex)
            {
                throw new ServiceException("Erreur lors de la récupération des factures impayées", ex);
            }
        }

        public async Task<IEnumerable<FactureVente>> GetFacturesByDateRangeAsync(DateTime dateDebut, DateTime dateFin)
        {
            try
            {
                return await _factureRepository.GetFacturesByDateRangeAsync(dateDebut, dateFin);
            }
            catch (Exception ex)
            {
                throw new ServiceException($"Erreur lors de la récupération des factures entre {dateDebut:d} et {dateFin:d}", ex);
            }
        }

        #endregion

        #region Statistiques et rapports

        public async Task<decimal> GetChiffreAffairesAsync(DateTime? dateDebut = null, DateTime? dateFin = null)
        {
            try
            {
                return await _factureRepository.GetChiffreAffairesAsync(dateDebut, dateFin);
            }
            catch (Exception ex)
            {
                throw new ServiceException("Erreur lors du calcul du chiffre d'affaires", ex);
            }
        }

        public async Task<IEnumerable<object>> GetVentesStatisticsAsync(DateTime dateDebut, DateTime dateFin)
        {
            try
            {
                return await _factureRepository.GetVentesStatisticsAsync(dateDebut, dateFin);
            }
            catch (Exception ex)
            {
                throw new ServiceException("Erreur lors de la récupération des statistiques de ventes", ex);
            }
        }

        public async Task<IEnumerable<object>> GetTopClientsAsync(int count = 10)
        {
            try
            {
                return await _clientRepository.GetTopClientsAsync(count);
            }
            catch (Exception ex)
            {
                throw new ServiceException("Erreur lors de la récupération des meilleurs clients", ex);
            }
        }

        #endregion

        #region Utilitaires

        public async Task<string> GenerateNextFactureNumberAsync()
        {
            try
            {
                return await _factureRepository.GenerateNextFactureNumberAsync();
            }
            catch (Exception ex)
            {
                throw new ServiceException("Erreur lors de la génération du numéro de facture", ex);
            }
        }

        public async Task<ServiceResult<bool>> ValidateStockAvailabilityAsync(IEnumerable<DetailFactureVente> details)
        {
            try
            {
                var errors = new List<string>();

                foreach (var detail in details)
                {
                    var article = await _articleService.GetByIdAsync(detail.ArticleId);
                    if (article == null)
                    {
                        errors.Add($"Article avec ID {detail.ArticleId} non trouvé");
                        continue;
                    }

                    if (article.StockDisponible < detail.Quantite)
                    {
                        errors.Add($"Stock insuffisant pour {article.NomArticle}. Disponible: {article.StockDisponible}, Demandé: {detail.Quantite}");
                    }
                }

                return errors.Count == 0 
                    ? ServiceResult<bool>.SuccessResult(true, "Stock suffisant")
                    : ServiceResult<bool>.ErrorResult("Stock insuffisant", errors);
            }
            catch (Exception ex)
            {
                return ServiceResult<bool>.ErrorResult(ex, "Erreur lors de la validation du stock");
            }
        }

        #endregion

        #region Méthodes privées

        private async Task<ServiceResult<bool>> ValidateFactureDataAsync(FactureVente facture, IEnumerable<DetailFactureVente> details)
        {
            var errors = new List<string>();

            // Validation de la facture
            if (facture.DateFacture > DateTime.Today)
            {
                errors.Add("La date de facture ne peut pas être dans le futur");
            }

            if (facture.ClientId.HasValue)
            {
                var client = await _clientRepository.GetByIdAsync(facture.ClientId.Value);
                if (client == null)
                {
                    errors.Add("Client non trouvé");
                }
                else if (client.Statut != "Actif")
                {
                    errors.Add("Le client n'est pas actif");
                }
            }

            // Validation des détails
            if (details == null || !details.Any())
            {
                errors.Add("La facture doit contenir au moins un article");
            }
            else
            {
                foreach (var detail in details)
                {
                    if (detail.Quantite <= 0)
                    {
                        errors.Add("La quantité doit être positive");
                    }

                    if (detail.PrixUnitaireTTC <= 0)
                    {
                        errors.Add("Le prix unitaire doit être positif");
                    }
                }
            }

            return errors.Count == 0 
                ? ServiceResult<bool>.SuccessResult(true)
                : ServiceResult<bool>.ErrorResult("Validation échouée", errors);
        }

        private async Task CalculateFactureTotalsAsync(FactureVente facture, IEnumerable<DetailFactureVente> details)
        {
            // Calculer les montants de chaque ligne
            foreach (var detail in details)
            {
                detail.CalculerMontants();
            }

            // Calculer les totaux de la facture
            facture.MontantHT = details.Sum(d => d.MontantHT);
            facture.MontantTVA = details.Sum(d => d.MontantTVA);
            facture.MontantTTC = details.Sum(d => d.MontantTTC);

            // Appliquer la remise globale
            if (facture.RemisePourcentage > 0)
            {
                facture.RemiseGlobale = facture.MontantTTC * (facture.RemisePourcentage / 100);
            }

            // Le montant total sera calculé après le timbre fiscal
            var montantApresRemise = facture.MontantTTC - facture.RemiseGlobale;
            facture.MontantTotal = montantApresRemise; // Sera mis à jour avec le timbre fiscal
            facture.MontantRestant = facture.MontantTotal - facture.MontantPaye;
        }

        #endregion
    }
}
