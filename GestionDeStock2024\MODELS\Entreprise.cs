using System;
using System.ComponentModel.DataAnnotations;
using static GestionDeStock2024.MODELS.Enums;

namespace GestionDeStock2024.MODELS
{
    /// <summary>
    /// Modèle pour les informations de l'entreprise
    /// </summary>
    public class Entreprise : BaseEntity, IStatutEntity
    {
        [Required(ErrorMessage = "Le nom de l'entreprise est obligatoire")]
        [StringLength(255, ErrorMessage = "Le nom ne peut pas dépasser 255 caractères")]
        public string NomEntreprise { get; set; }

        [StringLength(255)]
        public string RaisonSociale { get; set; }

        public string Adresse { get; set; }

        [StringLength(100)]
        public string Ville { get; set; }

        [StringLength(100)]
        public string Wilaya { get; set; }

        [StringLength(10)]
        public string CodePostal { get; set; }

        [StringLength(20)]
        [Phone(ErrorMessage = "Format de téléphone invalide")]
        public string Telephone { get; set; }

        [StringLength(20)]
        public string Fax { get; set; }

        [StringLength(100)]
        [EmailAddress(ErrorMessage = "Format d'email invalide")]
        public string Email { get; set; }

        [StringLength(255)]
        public string SiteWeb { get; set; }

        /// <summary>
        /// Numéro d'Identification Fiscale (Algérie)
        /// </summary>
        [StringLength(20)]
        public string NIF { get; set; }

        /// <summary>
        /// Numéro d'Identification Statistique (Algérie)
        /// </summary>
        [StringLength(20)]
        public string NIS { get; set; }

        /// <summary>
        /// Registre de Commerce (Algérie)
        /// </summary>
        [StringLength(20)]
        public string RC { get; set; }

        /// <summary>
        /// Article d'Imposition (Algérie)
        /// </summary>
        [StringLength(20)]
        public string ART { get; set; }

        public byte[] Logo { get; set; }

        [Range(0, double.MaxValue, ErrorMessage = "Le capital social doit être positif")]
        public decimal CapitalSocial { get; set; } = 0;

        [StringLength(100)]
        public string FormeJuridique { get; set; }

        [StringLength(255)]
        public string ActivitePrincipale { get; set; }

        public DateTime? DateCreation { get; set; }

        public string Statut { get; set; } = StatutGeneral.Actif.ToString();

        // Propriétés calculées
        public bool EstValide => !string.IsNullOrEmpty(NomEntreprise) && 
                                !string.IsNullOrEmpty(NIF) && 
                                !string.IsNullOrEmpty(NIS);

        public string AdresseComplete => $"{Adresse}, {Ville}, {Wilaya} {CodePostal}".Trim(' ', ',');
    }
}
