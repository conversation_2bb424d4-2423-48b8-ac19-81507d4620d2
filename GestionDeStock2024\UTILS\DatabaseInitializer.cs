using System;
using System.Threading.Tasks;
using MySqlConnector;
using GestionDeStock2024.DATA;
using Dapper;

namespace GestionDeStock2024.UTILS
{
    /// <summary>
    /// Classe pour initialiser et mettre à jour la base de données
    /// </summary>
    public static class DatabaseInitializer
    {
        /// <summary>
        /// Initialise la base de données avec les données de base
        /// </summary>
        public static async Task InitializeDatabaseAsync()
        {
            try
            {
                var dbConnection = new DatabaseConnection();
                using var connection = await dbConnection.GetConnectionAsync();

                await CreateDefaultUsers(connection as MySqlConnection);
                await CreateDefaultCategories(connection as MySqlConnection);
                await CreateDefaultSettings(connection as MySqlConnection);
            }
            catch (Exception ex)
            {
                throw new Exception($"Erreur lors de l'initialisation de la base de données : {ex.Message}");
            }
        }

        /// <summary>
        /// Crée les utilisateurs par défaut avec des mots de passe hachés
        /// </summary>
        private static async Task CreateDefaultUsers(MySqlConnection connection)
        {
            // Vérifier si l'utilisateur admin existe déjà
            var existingAdmin = await connection.QueryFirstOrDefaultAsync<int>(
                "SELECT COUNT(*) FROM utilisateurs WHERE nom_utilisateur = 'admin'");

            if (existingAdmin == 0)
            {
                // Créer l'utilisateur administrateur avec mot de passe haché
                var adminPassword = PasswordHelper.HashPassword("admin123");
                
                var sql = @"
                    INSERT INTO utilisateurs (nom_utilisateur, mot_de_passe, nom_complet, email, role, statut, created_at, updated_at)
                    VALUES (@NomUtilisateur, @MotDePasse, @NomComplet, @Email, @Role, @Statut, @CreatedAt, @UpdatedAt)";

                await connection.ExecuteAsync(sql, new
                {
                    NomUtilisateur = "admin",
                    MotDePasse = adminPassword,
                    NomComplet = "Administrateur Système",
                    Email = "<EMAIL>",
                    Role = "Administrateur",
                    Statut = "Actif",
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now
                });

                // Créer un utilisateur gestionnaire
                var managerPassword = PasswordHelper.HashPassword("manager123");
                
                await connection.ExecuteAsync(sql, new
                {
                    NomUtilisateur = "gestionnaire",
                    MotDePasse = managerPassword,
                    NomComplet = "Gestionnaire Principal",
                    Email = "<EMAIL>",
                    Role = "Gestionnaire",
                    Statut = "Actif",
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now
                });

                // Créer un utilisateur vendeur
                var vendeurPassword = PasswordHelper.HashPassword("vendeur123");
                
                await connection.ExecuteAsync(sql, new
                {
                    NomUtilisateur = "vendeur",
                    MotDePasse = vendeurPassword,
                    NomComplet = "Vendeur Principal",
                    Email = "<EMAIL>",
                    Role = "Vendeur",
                    Statut = "Actif",
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now
                });
            }
            else
            {
                // Mettre à jour le mot de passe admin s'il n'est pas déjà haché
                await UpdateAdminPasswordIfNeeded(connection);
            }
        }

        /// <summary>
        /// Met à jour le mot de passe admin s'il n'est pas déjà haché avec BCrypt
        /// </summary>
        private static async Task UpdateAdminPasswordIfNeeded(MySqlConnection connection)
        {
            var adminUser = await connection.QueryFirstOrDefaultAsync<dynamic>(
                "SELECT id, mot_de_passe FROM utilisateurs WHERE nom_utilisateur = 'admin'");

            if (adminUser != null)
            {
                string currentPassword = adminUser.mot_de_passe;
                
                // Vérifier si le mot de passe est déjà haché avec BCrypt
                if (!currentPassword.StartsWith("$2a$") && !currentPassword.StartsWith("$2b$") && !currentPassword.StartsWith("$2y$"))
                {
                    // Le mot de passe n'est pas haché, le hacher maintenant
                    string hashedPassword;
                    
                    if (currentPassword == "admin123" || currentPassword == "admin")
                    {
                        hashedPassword = PasswordHelper.HashPassword("admin123");
                    }
                    else
                    {
                        // Garder le mot de passe existant mais le hacher
                        hashedPassword = PasswordHelper.HashPassword(currentPassword);
                    }

                    await connection.ExecuteAsync(
                        "UPDATE utilisateurs SET mot_de_passe = @MotDePasse, updated_at = @UpdatedAt WHERE id = @Id",
                        new
                        {
                            MotDePasse = hashedPassword,
                            UpdatedAt = DateTime.Now,
                            Id = adminUser.id
                        });
                }
            }
        }

        /// <summary>
        /// Crée les catégories par défaut
        /// </summary>
        private static async Task CreateDefaultCategories(MySqlConnection connection)
        {
            var existingCategories = await connection.QueryFirstOrDefaultAsync<int>(
                "SELECT COUNT(*) FROM categories");

            if (existingCategories == 0)
            {
                var categories = new[]
                {
                    new { Code = "ALI", Nom = "Alimentaire", Description = "Produits alimentaires" },
                    new { Code = "BOI", Nom = "Boissons", Description = "Boissons diverses" },
                    new { Code = "HYG", Nom = "Hygiène", Description = "Produits d'hygiène et cosmétiques" },
                    new { Code = "ELE", Nom = "Électronique", Description = "Appareils électroniques" },
                    new { Code = "TEX", Nom = "Textile", Description = "Vêtements et textiles" },
                    new { Code = "MAI", Nom = "Maison", Description = "Articles pour la maison" },
                    new { Code = "JOU", Nom = "Jouets", Description = "Jouets et jeux" },
                    new { Code = "LIV", Nom = "Livres", Description = "Livres et magazines" }
                };

                var sql = @"
                    INSERT INTO categories (code, nom, description, statut, created_at, updated_at)
                    VALUES (@Code, @Nom, @Description, 'Actif', @CreatedAt, @UpdatedAt)";

                foreach (var category in categories)
                {
                    await connection.ExecuteAsync(sql, new
                    {
                        category.Code,
                        category.Nom,
                        category.Description,
                        CreatedAt = DateTime.Now,
                        UpdatedAt = DateTime.Now
                    });
                }
            }
        }

        /// <summary>
        /// Crée les paramètres par défaut du système
        /// </summary>
        private static async Task CreateDefaultSettings(MySqlConnection connection)
        {
            var existingSettings = await connection.QueryFirstOrDefaultAsync<int>(
                "SELECT COUNT(*) FROM parametres_systeme");

            if (existingSettings == 0)
            {
                var settings = new[]
                {
                    new { Cle = "TVA_NORMALE", Valeur = "19.00", Description = "Taux de TVA normal en %" },
                    new { Cle = "TVA_REDUITE", Valeur = "9.00", Description = "Taux de TVA réduit en %" },
                    new { Cle = "TIMBRE_SEUIL_MIN", Valeur = "300.00", Description = "Seuil minimum pour le timbre fiscal en DA" },
                    new { Cle = "TIMBRE_SEUIL_MAX", Valeur = "30000.00", Description = "Seuil maximum pour le timbre fiscal en DA" },
                    new { Cle = "TIMBRE_TAUX_1", Valeur = "1.00", Description = "Montant du timbre fiscal niveau 1 en DA" },
                    new { Cle = "TIMBRE_TAUX_2", Valeur = "1.50", Description = "Montant du timbre fiscal niveau 2 en DA pour 100 DA" },
                    new { Cle = "DEVISE_DEFAUT", Valeur = "DA", Description = "Devise par défaut" },
                    new { Cle = "FORMAT_DATE", Valeur = "dd/MM/yyyy", Description = "Format d'affichage des dates" },
                    new { Cle = "PRECISION_DECIMALE", Valeur = "2", Description = "Nombre de décimales pour les montants" },
                    new { Cle = "STOCK_ALERTE_ACTIVE", Valeur = "true", Description = "Activer les alertes de stock" },
                    new { Cle = "STOCK_ALERTE_SEUIL", Valeur = "10", Description = "Seuil d'alerte de stock en pourcentage" }
                };

                var sql = @"
                    INSERT INTO parametres_systeme (cle, valeur, description, created_at, updated_at)
                    VALUES (@Cle, @Valeur, @Description, @CreatedAt, @UpdatedAt)";

                foreach (var setting in settings)
                {
                    await connection.ExecuteAsync(sql, new
                    {
                        setting.Cle,
                        setting.Valeur,
                        setting.Description,
                        CreatedAt = DateTime.Now,
                        UpdatedAt = DateTime.Now
                    });
                }
            }
        }

        /// <summary>
        /// Met à jour tous les mots de passe existants vers BCrypt
        /// </summary>
        public static async Task MigratePasswordsToBCryptAsync()
        {
            try
            {
                var dbConnection = new DatabaseConnection();
                using var connection = await dbConnection.GetConnectionAsync();

                var users = await connection.QueryAsync<dynamic>(
                    "SELECT id, nom_utilisateur, mot_de_passe FROM utilisateurs WHERE statut = 'Actif'");

                foreach (var user in users)
                {
                    string currentPassword = user.mot_de_passe;
                    
                    // Vérifier si le mot de passe est déjà haché avec BCrypt
                    if (!currentPassword.StartsWith("$2a$") && !currentPassword.StartsWith("$2b$") && !currentPassword.StartsWith("$2y$"))
                    {
                        // Hacher le mot de passe existant
                        string hashedPassword = PasswordHelper.HashPassword(currentPassword);

                        await connection.ExecuteAsync(
                            "UPDATE utilisateurs SET mot_de_passe = @MotDePasse, updated_at = @UpdatedAt WHERE id = @Id",
                            new
                            {
                                MotDePasse = hashedPassword,
                                UpdatedAt = DateTime.Now,
                                Id = user.id
                            });
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Erreur lors de la migration des mots de passe : {ex.Message}");
            }
        }

        /// <summary>
        /// Vérifie et répare l'intégrité de la base de données
        /// </summary>
        public static async Task CheckAndRepairDatabaseAsync()
        {
            try
            {
                var dbConnection = new DatabaseConnection();
                using var connection = await dbConnection.GetConnectionAsync();

                // Vérifier les tables essentielles
                await EnsureEssentialTablesExist(connection as MySqlConnection);

                // Vérifier les index
                await EnsureIndexesExist(connection as MySqlConnection);

                // Nettoyer les données orphelines
                await CleanOrphanedData(connection as MySqlConnection);
            }
            catch (Exception ex)
            {
                throw new Exception($"Erreur lors de la vérification de la base de données : {ex.Message}");
            }
        }

        private static async Task EnsureEssentialTablesExist(MySqlConnection connection)
        {
            var tables = new[] { "utilisateurs", "categories", "articles", "clients", "fournisseurs", "parametres_systeme" };
            
            foreach (var table in tables)
            {
                var exists = await connection.QueryFirstOrDefaultAsync<int>(
                    $"SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = '{table}'");
                
                if (exists == 0)
                {
                    throw new Exception($"Table essentielle manquante : {table}");
                }
            }
        }

        private static async Task EnsureIndexesExist(MySqlConnection connection)
        {
            // Ajouter des index pour améliorer les performances
            var indexes = new[]
            {
                "CREATE INDEX IF NOT EXISTS idx_utilisateurs_nom_utilisateur ON utilisateurs(nom_utilisateur)",
                "CREATE INDEX IF NOT EXISTS idx_articles_code_barre ON articles(code_barre)",
                "CREATE INDEX IF NOT EXISTS idx_articles_nom ON articles(nom)",
                "CREATE INDEX IF NOT EXISTS idx_clients_nom ON clients(nom)",
                "CREATE INDEX IF NOT EXISTS idx_clients_nif ON clients(nif)",
                "CREATE INDEX IF NOT EXISTS idx_fournisseurs_nom ON fournisseurs(nom)"
            };

            foreach (var indexSql in indexes)
            {
                try
                {
                    await connection.ExecuteAsync(indexSql);
                }
                catch
                {
                    // Ignorer les erreurs d'index (peut-être déjà existant)
                }
            }
        }

        private static async Task CleanOrphanedData(MySqlConnection connection)
        {
            // Nettoyer les données orphelines (exemple : articles sans catégorie valide)
            await connection.ExecuteAsync(@"
                UPDATE articles 
                SET categorie_id = (SELECT id FROM categories WHERE nom = 'Alimentaire' LIMIT 1)
                WHERE categorie_id NOT IN (SELECT id FROM categories)");
        }
    }
}
