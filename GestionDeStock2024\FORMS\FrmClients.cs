using System;
using System.Drawing;
using System.Windows.Forms;
using GestionDeStock2024.MODELS;

namespace GestionDeStock2024.FORMS
{
    /// <summary>
    /// Formulaire de gestion des clients
    /// </summary>
    public partial class FrmClients : Form
    {
        private Utilisateur currentUser;
        private DataGridView dgvClients;
        private TextBox txtSearch;
        private Button btnAdd, btnEdit, btnDelete, btnRefresh;
        private ComboBox cmbTypeClient;

        public FrmClients()
        {
            InitializeComponent();
            InitializeForm();
        }

        public FrmClients(Utilisateur user) : this()
        {
            currentUser = user;
            LoadClients();
        }

        private void InitializeForm()
        {
            this.Text = "Gestion des Clients";
            this.BackColor = Color.White;
            this.Size = new Size(1200, 800);
            this.WindowState = FormWindowState.Maximized;

            CreateLayout();
            ApplyModernTheme();
        }

        private void CreateLayout()
        {
            // Panel principal
            var mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20),
                BackColor = Color.FromArgb(248, 249, 250)
            };
            this.Controls.Add(mainPanel);

            // Titre
            var titleLabel = new Label
            {
                Text = "👥 Gestion des Clients",
                Font = new Font("Segoe UI", 24, FontStyle.Bold),
                ForeColor = Color.FromArgb(33, 37, 41),
                AutoSize = true,
                Location = new Point(0, 0)
            };
            mainPanel.Controls.Add(titleLabel);

            // Panel des filtres et boutons
            var toolbarPanel = new Panel
            {
                Location = new Point(0, 60),
                Size = new Size(mainPanel.Width - 40, 60),
                Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right,
                BackColor = Color.White
            };
            mainPanel.Controls.Add(toolbarPanel);

            CreateToolbar(toolbarPanel);

            // DataGridView
            dgvClients = new DataGridView
            {
                Location = new Point(0, 140),
                Size = new Size(mainPanel.Width - 40, mainPanel.Height - 180),
                Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
            };
            mainPanel.Controls.Add(dgvClients);

            SetupDataGridView();
        }

        private void CreateToolbar(Panel parent)
        {
            // Recherche
            var lblSearch = new Label
            {
                Text = "🔍 Rechercher:",
                Font = new Font("Segoe UI", 10, FontStyle.Bold),
                ForeColor = Color.FromArgb(73, 80, 87),
                Location = new Point(10, 20),
                AutoSize = true
            };
            parent.Controls.Add(lblSearch);

            txtSearch = new TextBox
            {
                Location = new Point(110, 17),
                Size = new Size(200, 25),
                Font = new Font("Segoe UI", 10),
            };
            txtSearch.TextChanged += TxtSearch_TextChanged;
            parent.Controls.Add(txtSearch);

            // Type de client
            var lblType = new Label
            {
                Text = "Type:",
                Font = new Font("Segoe UI", 10),
                ForeColor = Color.FromArgb(73, 80, 87),
                Location = new Point(330, 20),
                AutoSize = true
            };
            parent.Controls.Add(lblType);

            cmbTypeClient = new ComboBox
            {
                Location = new Point(370, 17),
                Size = new Size(150, 25),
                Font = new Font("Segoe UI", 10),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            cmbTypeClient.Items.AddRange(new[] { "Tous", "Particulier", "Entreprise", "Revendeur" });
            cmbTypeClient.SelectedIndex = 0;
            cmbTypeClient.SelectedIndexChanged += CmbTypeClient_SelectedIndexChanged;
            parent.Controls.Add(cmbTypeClient);

            // Boutons
            btnAdd = CreateButton("➕ Ajouter", new Point(550, 15), Color.FromArgb(40, 167, 69));
            btnAdd.Click += BtnAdd_Click;
            parent.Controls.Add(btnAdd);

            btnEdit = CreateButton("✏️ Modifier", new Point(660, 15), Color.FromArgb(0, 123, 255));
            btnEdit.Click += BtnEdit_Click;
            parent.Controls.Add(btnEdit);

            btnDelete = CreateButton("🗑️ Supprimer", new Point(770, 15), Color.FromArgb(220, 53, 69));
            btnDelete.Click += BtnDelete_Click;
            parent.Controls.Add(btnDelete);

            btnRefresh = CreateButton("🔄", new Point(890, 15), Color.FromArgb(108, 117, 125));
            btnRefresh.Size = new Size(35, 30);
            btnRefresh.Click += BtnRefresh_Click;
            parent.Controls.Add(btnRefresh);
        }

        private Button CreateButton(string text, Point location, Color backColor)
        {
            return new Button
            {
                Text = text,
                Location = location,
                Size = new Size(100, 30),
                Font = new Font("Segoe UI", 9, FontStyle.Bold),
                BackColor = backColor,
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                FlatAppearance = { BorderSize = 0 },
                Cursor = Cursors.Hand
            };
        }

        private void SetupDataGridView()
        {
            dgvClients.Columns.Clear();

            // Configuration des colonnes
            dgvClients.Columns.Add("Code", "Code");
            dgvClients.Columns.Add("NomComplet", "Nom complet");
            dgvClients.Columns.Add("TypeClient", "Type");
            dgvClients.Columns.Add("Telephone", "Téléphone");
            dgvClients.Columns.Add("Email", "Email");
            dgvClients.Columns.Add("Adresse", "Adresse");
            dgvClients.Columns.Add("NIF", "NIF");
            dgvClients.Columns.Add("Statut", "Statut");

            // Style des colonnes
            dgvClients.Columns["Code"].Width = 80;
            dgvClients.Columns["NomComplet"].Width = 200;
            dgvClients.Columns["TypeClient"].Width = 100;
            dgvClients.Columns["Telephone"].Width = 120;
            dgvClients.Columns["Email"].Width = 180;
            dgvClients.Columns["Adresse"].Width = 200;
            dgvClients.Columns["NIF"].Width = 120;
            dgvClients.Columns["Statut"].Width = 80;
        }

        private void ApplyModernTheme()
        {
            // Style du DataGridView
            dgvClients.EnableHeadersVisualStyles = false;
            dgvClients.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(52, 58, 64);
            dgvClients.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvClients.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 10, FontStyle.Bold);
            dgvClients.ColumnHeadersHeight = 40;

            dgvClients.DefaultCellStyle.BackColor = Color.White;
            dgvClients.DefaultCellStyle.ForeColor = Color.FromArgb(33, 37, 41);
            dgvClients.DefaultCellStyle.Font = new Font("Segoe UI", 9);
            dgvClients.DefaultCellStyle.SelectionBackColor = Color.FromArgb(0, 123, 255);
            dgvClients.DefaultCellStyle.SelectionForeColor = Color.White;

            dgvClients.AlternatingRowsDefaultCellStyle.BackColor = Color.FromArgb(248, 249, 250);
            dgvClients.GridColor = Color.FromArgb(222, 226, 230);
            dgvClients.RowHeadersVisible = false;
            dgvClients.CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal;
        }

        private void LoadClients()
        {
            // Données fictives pour la démonstration
            dgvClients.Rows.Clear();
            
            var sampleData = new[]
            {
                new object[] { "CLI001", "Ahmed Benali", "Particulier", "0555123456", "<EMAIL>", "Rue de la Liberté, Alger", "", "Actif" },
                new object[] { "CLI002", "Fatima Khelifi", "Particulier", "0666789012", "<EMAIL>", "Boulevard Mohamed V, Oran", "", "Actif" },
                new object[] { "CLI003", "SARL ALPHA", "Entreprise", "0777345678", "<EMAIL>", "Zone Industrielle, Constantine", "123456789012345", "Actif" },
                new object[] { "CLI004", "Mohamed Tabet", "Revendeur", "0888901234", "<EMAIL>", "Rue des Frères Bouadou, Annaba", "987654321098765", "Actif" },
                new object[] { "CLI005", "Amina Cherif", "Particulier", "0999567890", "<EMAIL>", "Cité El Badr, Sétif", "", "Actif" }
            };

            foreach (var row in sampleData)
            {
                dgvClients.Rows.Add(row);
            }
        }

        #region Event Handlers

        private void TxtSearch_TextChanged(object sender, EventArgs e)
        {
            FilterClients();
        }

        private void CmbTypeClient_SelectedIndexChanged(object sender, EventArgs e)
        {
            FilterClients();
        }

        private void FilterClients()
        {
            // Ici, vous implémenteriez le filtrage réel
            // Pour le moment, on recharge simplement les données
            LoadClients();
        }

        private void BtnAdd_Click(object sender, EventArgs e)
        {
            MessageBox.Show("Fonctionnalité d'ajout de client à implémenter", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void BtnEdit_Click(object sender, EventArgs e)
        {
            if (dgvClients.SelectedRows.Count > 0)
            {
                MessageBox.Show("Fonctionnalité de modification de client à implémenter", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            else
            {
                MessageBox.Show("Veuillez sélectionner un client à modifier", "Attention", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private void BtnDelete_Click(object sender, EventArgs e)
        {
            if (dgvClients.SelectedRows.Count > 0)
            {
                var result = MessageBox.Show("Êtes-vous sûr de vouloir supprimer ce client ?", "Confirmation", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                if (result == DialogResult.Yes)
                {
                    MessageBox.Show("Fonctionnalité de suppression de client à implémenter", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            else
            {
                MessageBox.Show("Veuillez sélectionner un client à supprimer", "Attention", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private void BtnRefresh_Click(object sender, EventArgs e)
        {
            LoadClients();
        }

        #endregion

        private void FrmClients_Load(object sender, EventArgs e)
        {
            LoadClients();
        }
    }
}
