using System;
using System.Drawing;
using System.Windows.Forms;
using GestionDeStock2024.MODELS;

namespace GestionDeStock2024.FORMS
{
    /// <summary>
    /// Formulaire du tableau de bord principal
    /// </summary>
    public partial class FrmDashboard : Form
    {
        private Utilisateur currentUser;

        public FrmDashboard()
        {
            InitializeComponent();
            InitializeForm();
        }

        public FrmDashboard(Utilisateur user) : this()
        {
            currentUser = user;
            LoadDashboardData();
        }

        private void InitializeForm()
        {
            this.Text = "Tableau de bord";
            this.BackColor = Color.White;
            this.Size = new Size(1000, 700);
            this.WindowState = FormWindowState.Maximized;

            ApplyModernTheme();
            CreateDashboardLayout();
        }

        private void ApplyModernTheme()
        {
            this.BackColor = Color.FromArgb(248, 249, 250);
        }

        private void CreateDashboardLayout()
        {
            // Panel principal
            var mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20),
                BackColor = Color.FromArgb(248, 249, 250)
            };
            this.Controls.Add(mainPanel);

            // Titre
            var titleLabel = new Label
            {
                Text = "📊 Tableau de Bord",
                Font = new Font("Segoe UI", 24, FontStyle.Bold),
                ForeColor = Color.FromArgb(33, 37, 41),
                AutoSize = true,
                Location = new Point(0, 0)
            };
            mainPanel.Controls.Add(titleLabel);

            // Panel des statistiques
            var statsPanel = new Panel
            {
                Location = new Point(0, 60),
                Size = new Size(mainPanel.Width - 40, 150),
                Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right
            };
            mainPanel.Controls.Add(statsPanel);

            // Créer les cartes de statistiques
            CreateStatsCards(statsPanel);

            // Panel des graphiques
            var chartsPanel = new Panel
            {
                Location = new Point(0, 230),
                Size = new Size(mainPanel.Width - 40, 400),
                Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Bottom
            };
            mainPanel.Controls.Add(chartsPanel);

            CreateChartsSection(chartsPanel);
        }

        private void CreateStatsCards(Panel parent)
        {
            var cardWidth = (parent.Width - 60) / 4;
            var cardHeight = 120;

            // Carte Ventes du jour
            var salesCard = CreateStatsCard("💰 Ventes du Jour", "125,450 DA", "+12%", Color.FromArgb(40, 167, 69));
            salesCard.Location = new Point(0, 0);
            salesCard.Size = new Size(cardWidth, cardHeight);
            parent.Controls.Add(salesCard);

            // Carte Articles en stock
            var stockCard = CreateStatsCard("📦 Articles en Stock", "1,247", "-3%", Color.FromArgb(0, 123, 255));
            stockCard.Location = new Point(cardWidth + 20, 0);
            stockCard.Size = new Size(cardWidth, cardHeight);
            parent.Controls.Add(stockCard);

            // Carte Clients
            var clientsCard = CreateStatsCard("👥 Clients", "89", "+5%", Color.FromArgb(255, 193, 7));
            clientsCard.Location = new Point((cardWidth + 20) * 2, 0);
            clientsCard.Size = new Size(cardWidth, cardHeight);
            parent.Controls.Add(clientsCard);

            // Carte Commandes
            var ordersCard = CreateStatsCard("🛒 Commandes", "23", "+8%", Color.FromArgb(220, 53, 69));
            ordersCard.Location = new Point((cardWidth + 20) * 3, 0);
            ordersCard.Size = new Size(cardWidth, cardHeight);
            parent.Controls.Add(ordersCard);
        }

        private Panel CreateStatsCard(string title, string value, string change, Color accentColor)
        {
            var card = new Panel
            {
                BackColor = Color.White,
                BorderStyle = BorderStyle.None
            };

            // Ajouter une bordure personnalisée
            card.Paint += (s, e) =>
            {
                var rect = new Rectangle(0, 0, card.Width - 1, card.Height - 1);
                using (var pen = new Pen(Color.FromArgb(222, 226, 230)))
                {
                    e.Graphics.DrawRectangle(pen, rect);
                }
                // Bordure colorée en haut
                using (var brush = new SolidBrush(accentColor))
                {
                    e.Graphics.FillRectangle(brush, 0, 0, card.Width, 4);
                }
            };

            // Titre
            var titleLabel = new Label
            {
                Text = title,
                Font = new Font("Segoe UI", 10, FontStyle.Bold),
                ForeColor = Color.FromArgb(108, 117, 125),
                Location = new Point(15, 15),
                AutoSize = true
            };
            card.Controls.Add(titleLabel);

            // Valeur
            var valueLabel = new Label
            {
                Text = value,
                Font = new Font("Segoe UI", 18, FontStyle.Bold),
                ForeColor = Color.FromArgb(33, 37, 41),
                Location = new Point(15, 45),
                AutoSize = true
            };
            card.Controls.Add(valueLabel);

            // Changement
            var changeLabel = new Label
            {
                Text = change,
                Font = new Font("Segoe UI", 9),
                ForeColor = change.StartsWith("+") ? Color.FromArgb(40, 167, 69) : Color.FromArgb(220, 53, 69),
                Location = new Point(15, 85),
                AutoSize = true
            };
            card.Controls.Add(changeLabel);

            return card;
        }

        private void CreateChartsSection(Panel parent)
        {
            // Panel gauche - Graphique des ventes
            var leftPanel = new Panel
            {
                Location = new Point(0, 0),
                Size = new Size(parent.Width / 2 - 10, parent.Height),
                BackColor = Color.White,
                BorderStyle = BorderStyle.None
            };
            parent.Controls.Add(leftPanel);

            leftPanel.Paint += (s, e) =>
            {
                var rect = new Rectangle(0, 0, leftPanel.Width - 1, leftPanel.Height - 1);
                using (var pen = new Pen(Color.FromArgb(222, 226, 230)))
                {
                    e.Graphics.DrawRectangle(pen, rect);
                }
            };

            var salesChartTitle = new Label
            {
                Text = "📈 Évolution des Ventes",
                Font = new Font("Segoe UI", 14, FontStyle.Bold),
                ForeColor = Color.FromArgb(33, 37, 41),
                Location = new Point(20, 20),
                AutoSize = true
            };
            leftPanel.Controls.Add(salesChartTitle);

            // Placeholder pour le graphique
            var chartPlaceholder = new Label
            {
                Text = "Graphique des ventes\n(À implémenter avec une bibliothèque de graphiques)",
                Font = new Font("Segoe UI", 12),
                ForeColor = Color.FromArgb(108, 117, 125),
                TextAlign = ContentAlignment.MiddleCenter,
                Location = new Point(20, 80),
                Size = new Size(leftPanel.Width - 40, leftPanel.Height - 120)
            };
            leftPanel.Controls.Add(chartPlaceholder);

            // Panel droit - Activités récentes
            var rightPanel = new Panel
            {
                Location = new Point(parent.Width / 2 + 10, 0),
                Size = new Size(parent.Width / 2 - 10, parent.Height),
                BackColor = Color.White,
                BorderStyle = BorderStyle.None
            };
            parent.Controls.Add(rightPanel);

            rightPanel.Paint += (s, e) =>
            {
                var rect = new Rectangle(0, 0, rightPanel.Width - 1, rightPanel.Height - 1);
                using (var pen = new Pen(Color.FromArgb(222, 226, 230)))
                {
                    e.Graphics.DrawRectangle(pen, rect);
                }
            };

            var activityTitle = new Label
            {
                Text = "🕒 Activités Récentes",
                Font = new Font("Segoe UI", 14, FontStyle.Bold),
                ForeColor = Color.FromArgb(33, 37, 41),
                Location = new Point(20, 20),
                AutoSize = true
            };
            rightPanel.Controls.Add(activityTitle);

            // Liste des activités
            CreateActivityList(rightPanel);
        }

        private void CreateActivityList(Panel parent)
        {
            var activities = new[]
            {
                "🛒 Nouvelle commande #1234 - 15:30",
                "👤 Nouveau client: Ahmed Benali - 14:45",
                "📦 Stock mis à jour: Produit XYZ - 14:20",
                "💰 Paiement reçu: Facture #5678 - 13:55",
                "📊 Rapport généré: Ventes hebdomadaires - 13:30"
            };

            var listPanel = new Panel
            {
                Location = new Point(20, 60),
                Size = new Size(parent.Width - 40, parent.Height - 80),
                AutoScroll = true
            };
            parent.Controls.Add(listPanel);

            for (int i = 0; i < activities.Length; i++)
            {
                var activityLabel = new Label
                {
                    Text = activities[i],
                    Font = new Font("Segoe UI", 10),
                    ForeColor = Color.FromArgb(73, 80, 87),
                    Location = new Point(0, i * 40),
                    Size = new Size(listPanel.Width - 20, 30),
                    AutoEllipsis = true
                };
                listPanel.Controls.Add(activityLabel);

                // Ligne de séparation
                if (i < activities.Length - 1)
                {
                    var separator = new Panel
                    {
                        Location = new Point(0, i * 40 + 35),
                        Size = new Size(listPanel.Width - 20, 1),
                        BackColor = Color.FromArgb(222, 226, 230)
                    };
                    listPanel.Controls.Add(separator);
                }
            }
        }

        private void LoadDashboardData()
        {
            // Ici, vous pouvez charger les données réelles depuis la base de données
            // Pour le moment, nous utilisons des données fictives
        }

        private void FrmDashboard_Load(object sender, EventArgs e)
        {
            LoadDashboardData();
        }
    }
}
