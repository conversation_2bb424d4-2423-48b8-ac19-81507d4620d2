using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace GestionDeStock2024.MODELS.DTOs
{
    /// <summary>
    /// DTO pour l'affichage des articles
    /// </summary>
    public class ArticleDTO
    {
        public int Id { get; set; }
        public string Code { get; set; }
        public string NomArticle { get; set; }
        public string Description { get; set; }
        public string CategorieName { get; set; }
        public string UniteName { get; set; }
        public decimal PrixAchatUnitaire { get; set; }
        public decimal PrixVenteUnitaire { get; set; }
        public decimal PrixVenteGros { get; set; }
        public decimal MargeBeneficiaire { get; set; }
        public decimal TvaVente { get; set; }
        public decimal StockActuel { get; set; }
        public decimal StockMinimum { get; set; }
        public decimal StockMaximum { get; set; }
        public decimal StockReserve { get; set; }
        public decimal StockDisponible { get; set; }
        public decimal PMP { get; set; }
        public decimal ValeurStock { get; set; }
        public string AlerteStock { get; set; }
        public string Statut { get; set; }
        public DateTime? DateExpiration { get; set; }
        public bool EstPerime { get; set; }
        public bool ExpireBientot { get; set; }
        public List<string> CodesBarres { get; set; } = new List<string>();
    }

    /// <summary>
    /// DTO pour la création/modification d'articles
    /// </summary>
    public class ArticleCreateUpdateDTO
    {
        [Required(ErrorMessage = "Le code article est obligatoire")]
        [StringLength(50, ErrorMessage = "Le code ne peut pas dépasser 50 caractères")]
        public string Code { get; set; }

        [Required(ErrorMessage = "Le nom de l'article est obligatoire")]
        [StringLength(255, ErrorMessage = "Le nom ne peut pas dépasser 255 caractères")]
        public string NomArticle { get; set; }

        public string Description { get; set; }

        [Required(ErrorMessage = "La catégorie est obligatoire")]
        public int CategorieId { get; set; }

        [Required(ErrorMessage = "L'unité de mesure est obligatoire")]
        public int UniteMesureId { get; set; }

        [Range(0, double.MaxValue, ErrorMessage = "Le prix d'achat doit être positif")]
        public decimal PrixAchatUnitaire { get; set; }

        [Range(0, double.MaxValue, ErrorMessage = "Le prix de vente doit être positif")]
        public decimal PrixVenteUnitaire { get; set; }

        [Range(0, double.MaxValue, ErrorMessage = "Le prix de vente en gros doit être positif")]
        public decimal PrixVenteGros { get; set; }

        [Range(0, 100, ErrorMessage = "La marge doit être entre 0 et 100%")]
        public decimal MargeBeneficiaire { get; set; }

        [Range(0, 100, ErrorMessage = "La TVA doit être entre 0 et 100%")]
        public decimal TvaAchat { get; set; } = 19.00m;

        [Range(0, 100, ErrorMessage = "La TVA doit être entre 0 et 100%")]
        public decimal TvaVente { get; set; } = 19.00m;

        [Range(0, double.MaxValue, ErrorMessage = "Le stock minimum doit être positif")]
        public decimal StockMinimum { get; set; }

        [Range(0, double.MaxValue, ErrorMessage = "Le stock maximum doit être positif")]
        public decimal StockMaximum { get; set; }

        [Range(0, double.MaxValue, ErrorMessage = "Le stock actuel doit être positif")]
        public decimal StockActuel { get; set; }

        [Range(1, int.MaxValue, ErrorMessage = "Le nombre de pièces par fardeau doit être au moins 1")]
        public int PiecesParFardeau { get; set; } = 1;

        [Range(0, double.MaxValue, ErrorMessage = "Le prix du fardeau doit être positif")]
        public decimal PrixFardeau { get; set; }

        public DateTime? DateExpiration { get; set; }

        [StringLength(100)]
        public string LotNumero { get; set; }

        [StringLength(100)]
        public string Emplacement { get; set; }

        public string Statut { get; set; } = "Actif";

        public List<string> CodesBarres { get; set; } = new List<string>();
    }

    /// <summary>
    /// DTO pour la recherche d'articles
    /// </summary>
    public class ArticleSearchDTO
    {
        public string SearchTerm { get; set; }
        public int? CategorieId { get; set; }
        public string Statut { get; set; }
        public bool? StockFaible { get; set; }
        public bool? RuptureStock { get; set; }
        public bool? ExpireBientot { get; set; }
        public decimal? PrixMin { get; set; }
        public decimal? PrixMax { get; set; }
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 20;
        public string SortField { get; set; } = "NomArticle";
        public bool SortAscending { get; set; } = true;
    }

    /// <summary>
    /// DTO pour l'ajustement de stock
    /// </summary>
    public class StockAdjustmentDTO
    {
        [Required(ErrorMessage = "L'ID de l'article est obligatoire")]
        public int ArticleId { get; set; }

        [Required(ErrorMessage = "Le nouveau stock est obligatoire")]
        [Range(0, double.MaxValue, ErrorMessage = "Le stock doit être positif")]
        public decimal NouveauStock { get; set; }

        [Required(ErrorMessage = "Le motif est obligatoire")]
        [StringLength(255, ErrorMessage = "Le motif ne peut pas dépasser 255 caractères")]
        public string Motif { get; set; }

        public string Notes { get; set; }
    }

    /// <summary>
    /// DTO pour les statistiques de stock
    /// </summary>
    public class StockStatisticsDTO
    {
        public int TotalArticles { get; set; }
        public int ArticlesActifs { get; set; }
        public int ArticlesStockFaible { get; set; }
        public int ArticlesRuptureStock { get; set; }
        public int ArticlesExpires { get; set; }
        public int ArticlesExpirantBientot { get; set; }
        public decimal ValeurTotaleStock { get; set; }
        public decimal ValeurStockFaible { get; set; }
        public decimal ValeurRuptureStock { get; set; }
    }

    /// <summary>
    /// DTO pour l'inventaire
    /// </summary>
    public class InventaireArticleDTO
    {
        public int ArticleId { get; set; }
        public string CodeArticle { get; set; }
        public string NomArticle { get; set; }
        public string CategorieName { get; set; }
        public string UniteAbreviation { get; set; }
        public decimal StockTheorique { get; set; }
        public decimal StockPhysique { get; set; }
        public decimal Ecart { get; set; }
        public decimal ValeurEcart { get; set; }
        public string MotifEcart { get; set; }
        public string TypeEcart { get; set; }
        public decimal PourcentageEcart { get; set; }
    }

    /// <summary>
    /// DTO pour les mouvements de stock
    /// </summary>
    public class MouvementStockDTO
    {
        public int Id { get; set; }
        public string ArticleNom { get; set; }
        public string ArticleCode { get; set; }
        public string TypeMouvement { get; set; }
        public string ReferenceDocument { get; set; }
        public string TypeDocument { get; set; }
        public decimal QuantiteAvant { get; set; }
        public decimal QuantiteMouvement { get; set; }
        public decimal QuantiteApres { get; set; }
        public decimal PrixUnitaire { get; set; }
        public decimal CoutTotal { get; set; }
        public string UtilisateurNom { get; set; }
        public DateTime DateMouvement { get; set; }
        public TimeSpan HeureMouvement { get; set; }
        public string Motif { get; set; }
        public string Notes { get; set; }
    }

    /// <summary>
    /// DTO pour les articles les plus vendus
    /// </summary>
    public class TopSellingArticleDTO
    {
        public int ArticleId { get; set; }
        public string CodeArticle { get; set; }
        public string NomArticle { get; set; }
        public string CategorieName { get; set; }
        public decimal QuantiteVendue { get; set; }
        public decimal ChiffreAffaires { get; set; }
        public int NombreVentes { get; set; }
        public decimal PrixMoyenVente { get; set; }
        public decimal MargeRealisee { get; set; }
    }

    /// <summary>
    /// DTO pour l'import/export d'articles
    /// </summary>
    public class ArticleImportExportDTO
    {
        public string Code { get; set; }
        public string NomArticle { get; set; }
        public string Description { get; set; }
        public string CategorieCode { get; set; }
        public string UniteCode { get; set; }
        public decimal PrixAchat { get; set; }
        public decimal PrixVente { get; set; }
        public decimal PrixVenteGros { get; set; }
        public decimal TvaVente { get; set; }
        public decimal StockActuel { get; set; }
        public decimal StockMinimum { get; set; }
        public decimal StockMaximum { get; set; }
        public string CodesBarres { get; set; } // Séparés par des virgules
        public string DateExpiration { get; set; }
        public string LotNumero { get; set; }
        public string Emplacement { get; set; }
        public string Statut { get; set; }
    }

    /// <summary>
    /// DTO pour la réservation de stock
    /// </summary>
    public class StockReservationDTO
    {
        [Required(ErrorMessage = "L'ID de l'article est obligatoire")]
        public int ArticleId { get; set; }

        [Required(ErrorMessage = "La quantité est obligatoire")]
        [Range(0.01, double.MaxValue, ErrorMessage = "La quantité doit être positive")]
        public decimal Quantite { get; set; }

        [Required(ErrorMessage = "La référence est obligatoire")]
        [StringLength(100, ErrorMessage = "La référence ne peut pas dépasser 100 caractères")]
        public string Reference { get; set; }

        [StringLength(255)]
        public string Motif { get; set; }

        public DateTime? DateExpiration { get; set; }
    }

    /// <summary>
    /// DTO pour les alertes de stock
    /// </summary>
    public class StockAlertDTO
    {
        public int ArticleId { get; set; }
        public string CodeArticle { get; set; }
        public string NomArticle { get; set; }
        public string CategorieName { get; set; }
        public decimal StockActuel { get; set; }
        public decimal StockMinimum { get; set; }
        public decimal StockDisponible { get; set; }
        public string TypeAlerte { get; set; } // "Stock faible", "Rupture", "Expiration"
        public string Priorite { get; set; } // "Haute", "Moyenne", "Basse"
        public DateTime DateAlerte { get; set; }
        public string Message { get; set; }
    }
}
