using System;
using System.Drawing;
using System.Windows.Forms;
using GestionDeStock2024.MODELS;

namespace GestionDeStock2024.FORMS
{
    /// <summary>
    /// Formulaire de gestion des fournisseurs
    /// </summary>
    public partial class FrmSuppliers : Form
    {
        private Utilisateur currentUser;

        public FrmSuppliers()
        {
            InitializeComponent();
            SetupDataGridView();
            ApplyModernTheme();
        }

        public FrmSuppliers(Utilisateur user) : this()
        {
            currentUser = user;
            cmbType.SelectedIndex = 0; // "Tous"
            cmbStatut.SelectedIndex = 0; // "Tous"
        }

        private void SetupDataGridView()
        {
            dgvSuppliers.Columns.Clear();

            dgvSuppliers.Columns.Add("Code", "Code");
            dgvSuppliers.Columns.Add("Nom", "Nom");
            dgvSuppliers.Columns.Add("TypeFournisseur", "Type");
            dgvSuppliers.Columns.Add("Telephone", "Téléphone");
            dgvSuppliers.Columns.Add("Email", "Email");
            dgvSuppliers.Columns.Add("Adresse", "Adresse");
            dgvSuppliers.Columns.Add("NIF", "NIF");
            dgvSuppliers.Columns.Add("Contact", "Contact");
            dgvSuppliers.Columns.Add("Statut", "Statut");

            dgvSuppliers.Columns["Code"].Width = 80;
            dgvSuppliers.Columns["Nom"].Width = 200;
            dgvSuppliers.Columns["TypeFournisseur"].Width = 120;
            dgvSuppliers.Columns["Telephone"].Width = 120;
            dgvSuppliers.Columns["Email"].Width = 180;
            dgvSuppliers.Columns["Adresse"].Width = 200;
            dgvSuppliers.Columns["NIF"].Width = 120;
            dgvSuppliers.Columns["Contact"].Width = 150;
            dgvSuppliers.Columns["Statut"].Width = 80;
        }

        private void ApplyModernTheme()
        {
            dgvSuppliers.EnableHeadersVisualStyles = false;
            dgvSuppliers.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(52, 58, 64);
            dgvSuppliers.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvSuppliers.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 10, FontStyle.Bold);
            dgvSuppliers.ColumnHeadersHeight = 40;

            dgvSuppliers.DefaultCellStyle.BackColor = Color.White;
            dgvSuppliers.DefaultCellStyle.ForeColor = Color.FromArgb(33, 37, 41);
            dgvSuppliers.DefaultCellStyle.Font = new Font("Segoe UI", 9);
            dgvSuppliers.DefaultCellStyle.SelectionBackColor = Color.FromArgb(0, 123, 255);
            dgvSuppliers.DefaultCellStyle.SelectionForeColor = Color.White;

            dgvSuppliers.AlternatingRowsDefaultCellStyle.BackColor = Color.FromArgb(248, 249, 250);
            dgvSuppliers.GridColor = Color.FromArgb(222, 226, 230);
            dgvSuppliers.RowHeadersVisible = false;
            dgvSuppliers.CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal;
        }

        private void LoadSuppliers()
        {
            dgvSuppliers.Rows.Clear();
            
            var sampleData = new[]
            {
                new object[] { "FOUR001", "SARL ALPHA DISTRIBUTION", "Grossiste", "021-123456", "<EMAIL>", "Zone Industrielle Rouiba, Alger", "123456789012345", "Ahmed Benali", "Actif" },
                new object[] { "FOUR002", "BETA IMPORT EXPORT", "Importateur", "021-789012", "<EMAIL>", "Port d'Alger, Alger", "987654321098765", "Fatima Khelifi", "Actif" },
                new object[] { "FOUR003", "GAMMA ALIMENTAIRE", "Producteur", "031-345678", "<EMAIL>", "Zone Industrielle Constantine", "456789123456789", "Mohamed Tabet", "Actif" },
                new object[] { "FOUR004", "DELTA BOISSONS", "Distributeur", "041-901234", "<EMAIL>", "Zone Industrielle Oran", "789123456789123", "Amina Cherif", "Actif" },
                new object[] { "FOUR005", "EPSILON HYGIENE", "Grossiste", "038-567890", "<EMAIL>", "Zone Industrielle Annaba", "321654987321654", "Karim Boudiaf", "Actif" },
                new object[] { "FOUR006", "ZETA ELECTRONIQUE", "Importateur", "036-234567", "<EMAIL>", "Zone Industrielle Sétif", "654987321654987", "Nadia Hamidi", "Actif" }
            };

            foreach (var row in sampleData)
            {
                dgvSuppliers.Rows.Add(row);
            }
        }

        private void TxtSearch_TextChanged(object sender, EventArgs e)
        {
            LoadSuppliers(); // Filtrage simple
        }

        private void CmbType_SelectedIndexChanged(object sender, EventArgs e)
        {
            LoadSuppliers(); // Filtrage simple
        }

        private void CmbStatut_SelectedIndexChanged(object sender, EventArgs e)
        {
            LoadSuppliers(); // Filtrage simple
        }

        private void BtnAdd_Click(object sender, EventArgs e)
        {
            var frmSupplierAddEdit = new FrmSupplierAddEdit();
            if (frmSupplierAddEdit.ShowDialog() == DialogResult.OK)
            {
                LoadSuppliers();
            }
        }

        private void BtnEdit_Click(object sender, EventArgs e)
        {
            if (dgvSuppliers.SelectedRows.Count > 0)
            {
                var selectedRow = dgvSuppliers.SelectedRows[0];
                var code = selectedRow.Cells["Code"].Value?.ToString();
                
                var frmSupplierAddEdit = new FrmSupplierAddEdit(code);
                if (frmSupplierAddEdit.ShowDialog() == DialogResult.OK)
                {
                    LoadSuppliers();
                }
            }
            else
            {
                MessageBox.Show("Veuillez sélectionner un fournisseur à modifier", "Attention", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private void BtnDelete_Click(object sender, EventArgs e)
        {
            if (dgvSuppliers.SelectedRows.Count > 0)
            {
                var result = MessageBox.Show("Êtes-vous sûr de vouloir supprimer ce fournisseur ?", "Confirmation", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                if (result == DialogResult.Yes)
                {
                    LoadSuppliers();
                    MessageBox.Show("Fournisseur supprimé avec succès", "Succès", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            else
            {
                MessageBox.Show("Veuillez sélectionner un fournisseur à supprimer", "Attention", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private void BtnRefresh_Click(object sender, EventArgs e)
        {
            LoadSuppliers();
        }

        private void FrmSuppliers_Load(object sender, EventArgs e)
        {
            LoadSuppliers();
        }
    }
}
