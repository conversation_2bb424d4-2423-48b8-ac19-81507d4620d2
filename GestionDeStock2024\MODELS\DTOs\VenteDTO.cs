using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace GestionDeStock2024.MODELS.DTOs
{
    /// <summary>
    /// DTO pour l'affichage des factures de vente
    /// </summary>
    public class FactureVenteDTO
    {
        public int Id { get; set; }
        public string NumeroFacture { get; set; }
        public string TypeFacture { get; set; }
        public int? ClientId { get; set; }
        public string ClientNom { get; set; }
        public string ClientType { get; set; }
        public string UtilisateurNom { get; set; }
        public string CaisseNom { get; set; }
        public DateTime DateFacture { get; set; }
        public TimeSpan HeureFacture { get; set; }
        public decimal MontantHT { get; set; }
        public decimal MontantTVA { get; set; }
        public decimal MontantTTC { get; set; }
        public decimal RemiseGlobale { get; set; }
        public decimal RemisePourcentage { get; set; }
        public decimal TimbreFiscal { get; set; }
        public decimal MontantTotal { get; set; }
        public decimal MontantPaye { get; set; }
        public decimal MontantRestant { get; set; }
        public string ModePaiementNom { get; set; }
        public string StatutPaiement { get; set; }
        public string StatutFacture { get; set; }
        public string Notes { get; set; }
        public int NombreArticles { get; set; }
        public decimal QuantiteTotale { get; set; }
        public List<DetailFactureVenteDTO> Details { get; set; } = new List<DetailFactureVenteDTO>();
    }

    /// <summary>
    /// DTO pour les détails de facture de vente
    /// </summary>
    public class DetailFactureVenteDTO
    {
        public int Id { get; set; }
        public int ArticleId { get; set; }
        public string ArticleCode { get; set; }
        public string ArticleNom { get; set; }
        public string UniteAbreviation { get; set; }
        public string CodeBarreUtilise { get; set; }
        public decimal Quantite { get; set; }
        public decimal PrixUnitaireHT { get; set; }
        public decimal PrixUnitaireTTC { get; set; }
        public decimal RemiseLigne { get; set; }
        public decimal RemisePourcentage { get; set; }
        public decimal TvaTaux { get; set; }
        public decimal MontantHT { get; set; }
        public decimal MontantTVA { get; set; }
        public decimal MontantTTC { get; set; }
        public decimal MontantRemise { get; set; }
    }

    /// <summary>
    /// DTO pour la création d'une facture de vente
    /// </summary>
    public class FactureVenteCreateDTO
    {
        public string TypeFacture { get; set; } = "Facture";

        public int? ClientId { get; set; }

        [Required(ErrorMessage = "L'utilisateur est obligatoire")]
        public int UtilisateurId { get; set; }

        public int? CaisseId { get; set; }

        [Required(ErrorMessage = "La date de facture est obligatoire")]
        public DateTime DateFacture { get; set; } = DateTime.Today;

        public TimeSpan HeureFacture { get; set; } = DateTime.Now.TimeOfDay;

        [Range(0, 100, ErrorMessage = "Le pourcentage de remise doit être entre 0 et 100")]
        public decimal RemisePourcentage { get; set; } = 0;

        public int? ModePaiementId { get; set; }

        public string Notes { get; set; }

        [Required(ErrorMessage = "La facture doit contenir au moins un article")]
        [MinLength(1, ErrorMessage = "La facture doit contenir au moins un article")]
        public List<DetailFactureVenteCreateDTO> Details { get; set; } = new List<DetailFactureVenteCreateDTO>();
    }

    /// <summary>
    /// DTO pour la création d'un détail de facture
    /// </summary>
    public class DetailFactureVenteCreateDTO
    {
        [Required(ErrorMessage = "L'article est obligatoire")]
        public int ArticleId { get; set; }

        public string CodeBarreUtilise { get; set; }

        [Required(ErrorMessage = "La quantité est obligatoire")]
        [Range(0.01, double.MaxValue, ErrorMessage = "La quantité doit être positive")]
        public decimal Quantite { get; set; }

        [Required(ErrorMessage = "Le prix unitaire TTC est obligatoire")]
        [Range(0, double.MaxValue, ErrorMessage = "Le prix unitaire TTC doit être positif")]
        public decimal PrixUnitaireTTC { get; set; }

        [Range(0, double.MaxValue, ErrorMessage = "La remise ligne doit être positive")]
        public decimal RemiseLigne { get; set; } = 0;

        [Range(0, 100, ErrorMessage = "Le pourcentage de remise doit être entre 0 et 100")]
        public decimal RemisePourcentage { get; set; } = 0;

        [Range(0, 100, ErrorMessage = "Le taux de TVA doit être entre 0 et 100")]
        public decimal TvaTaux { get; set; } = 19.00m;
    }

    /// <summary>
    /// DTO pour la recherche de factures
    /// </summary>
    public class FactureVenteSearchDTO
    {
        public string NumeroFacture { get; set; }
        public int? ClientId { get; set; }
        public string ClientNom { get; set; }
        public DateTime? DateDebut { get; set; }
        public DateTime? DateFin { get; set; }
        public string StatutFacture { get; set; }
        public string StatutPaiement { get; set; }
        public decimal? MontantMin { get; set; }
        public decimal? MontantMax { get; set; }
        public int? UtilisateurId { get; set; }
        public int? CaisseId { get; set; }
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 20;
        public string SortField { get; set; } = "DateFacture";
        public bool SortAscending { get; set; } = false;
    }

    /// <summary>
    /// DTO pour les paiements
    /// </summary>
    public class PaiementDTO
    {
        public int Id { get; set; }
        public string NumeroPaiement { get; set; }
        public string TypePaiement { get; set; }
        public int DocumentId { get; set; }
        public string DocumentReference { get; set; }
        public decimal MontantPaiement { get; set; }
        public string ModePaiementNom { get; set; }
        public string CompteBancaireNom { get; set; }
        public string CaisseNom { get; set; }
        public string NumeroCheque { get; set; }
        public DateTime? DateEcheanceCheque { get; set; }
        public string BanqueCheque { get; set; }
        public decimal TimbreFiscal { get; set; }
        public decimal MontantTotal { get; set; }
        public DateTime DatePaiement { get; set; }
        public TimeSpan HeurePaiement { get; set; }
        public string UtilisateurNom { get; set; }
        public string Notes { get; set; }
        public string Statut { get; set; }
    }

    /// <summary>
    /// DTO pour la création d'un paiement
    /// </summary>
    public class PaiementCreateDTO
    {
        [Required(ErrorMessage = "Le type de paiement est obligatoire")]
        public string TypePaiement { get; set; }

        [Required(ErrorMessage = "L'ID du document est obligatoire")]
        public int DocumentId { get; set; }

        [Required(ErrorMessage = "Le montant du paiement est obligatoire")]
        [Range(0.01, double.MaxValue, ErrorMessage = "Le montant doit être positif")]
        public decimal MontantPaiement { get; set; }

        [Required(ErrorMessage = "Le mode de paiement est obligatoire")]
        public int ModePaiementId { get; set; }

        public int? CompteBancaireId { get; set; }

        public int? CaisseId { get; set; }

        [StringLength(50)]
        public string NumeroCheque { get; set; }

        public DateTime? DateEcheanceCheque { get; set; }

        [StringLength(255)]
        public string BanqueCheque { get; set; }

        [Required(ErrorMessage = "La date de paiement est obligatoire")]
        public DateTime DatePaiement { get; set; } = DateTime.Today;

        public TimeSpan HeurePaiement { get; set; } = DateTime.Now.TimeOfDay;

        [Required(ErrorMessage = "L'utilisateur est obligatoire")]
        public int UtilisateurId { get; set; }

        public string Notes { get; set; }
    }

    /// <summary>
    /// DTO pour les statistiques de ventes
    /// </summary>
    public class VenteStatisticsDTO
    {
        public DateTime Date { get; set; }
        public int NombreFactures { get; set; }
        public decimal TotalHT { get; set; }
        public decimal TotalTVA { get; set; }
        public decimal TotalTTC { get; set; }
        public decimal TotalTimbre { get; set; }
        public decimal ChiffreAffaires { get; set; }
        public decimal MoyenneFacture { get; set; }
        public int NombreClients { get; set; }
        public int NombreArticlesVendus { get; set; }
        public decimal QuantiteTotaleVendue { get; set; }
    }

    /// <summary>
    /// DTO pour le tableau de bord des ventes
    /// </summary>
    public class VenteDashboardDTO
    {
        public decimal ChiffreAffairesJour { get; set; }
        public decimal ChiffreAffairesSemaine { get; set; }
        public decimal ChiffreAffairesMois { get; set; }
        public decimal ChiffreAffairesAnnee { get; set; }
        public int NombreFacturesJour { get; set; }
        public int NombreFacturesSemaine { get; set; }
        public int NombreFacturesMois { get; set; }
        public decimal MoyenneFactureJour { get; set; }
        public decimal MoyenneFactureMois { get; set; }
        public int NombreClientsActifs { get; set; }
        public decimal TotalCreances { get; set; }
        public int NombreFacturesImpayees { get; set; }
        public List<VenteStatisticsDTO> VentesParJour { get; set; } = new List<VenteStatisticsDTO>();
        public List<TopSellingArticleDTO> TopArticles { get; set; } = new List<TopSellingArticleDTO>();
        public List<TopClientDTO> TopClients { get; set; } = new List<TopClientDTO>();
    }

    /// <summary>
    /// DTO pour les meilleurs clients
    /// </summary>
    public class TopClientDTO
    {
        public int ClientId { get; set; }
        public string CodeClient { get; set; }
        public string NomClient { get; set; }
        public string TypeClient { get; set; }
        public int NombreFactures { get; set; }
        public decimal TotalAchats { get; set; }
        public decimal MoyenneAchats { get; set; }
        public decimal SoldeActuel { get; set; }
        public DateTime? DerniereFacture { get; set; }
    }

    /// <summary>
    /// DTO pour l'impression de factures
    /// </summary>
    public class FactureImpressionDTO
    {
        public FactureVenteDTO Facture { get; set; }
        public EntrepriseDTO Entreprise { get; set; }
        public ClientDTO Client { get; set; }
        public string QRCode { get; set; }
        public string CodeBarre { get; set; }
        public string MontantEnLettres { get; set; }
        public Dictionary<string, string> ParametresImpression { get; set; } = new Dictionary<string, string>();
    }

    /// <summary>
    /// DTO pour les retours clients
    /// </summary>
    public class RetourClientDTO
    {
        public int Id { get; set; }
        public string NumeroRetour { get; set; }
        public int? FactureVenteId { get; set; }
        public string NumeroFacture { get; set; }
        public int? ClientId { get; set; }
        public string ClientNom { get; set; }
        public string UtilisateurNom { get; set; }
        public DateTime DateRetour { get; set; }
        public string MotifRetour { get; set; }
        public string DescriptionMotif { get; set; }
        public decimal MontantHT { get; set; }
        public decimal MontantTVA { get; set; }
        public decimal MontantTTC { get; set; }
        public decimal MontantRembourse { get; set; }
        public string ModeRemboursementNom { get; set; }
        public string Statut { get; set; }
        public int NombreArticles { get; set; }
        public decimal QuantiteTotale { get; set; }
        public List<DetailRetourClientDTO> Details { get; set; } = new List<DetailRetourClientDTO>();
    }

    /// <summary>
    /// DTO pour les détails de retour client
    /// </summary>
    public class DetailRetourClientDTO
    {
        public int Id { get; set; }
        public int ArticleId { get; set; }
        public string ArticleCode { get; set; }
        public string ArticleNom { get; set; }
        public string UniteAbreviation { get; set; }
        public decimal QuantiteRetournee { get; set; }
        public decimal PrixUnitaireHT { get; set; }
        public decimal PrixUnitaireTTC { get; set; }
        public decimal TvaTaux { get; set; }
        public decimal MontantHT { get; set; }
        public decimal MontantTVA { get; set; }
        public decimal MontantTTC { get; set; }
        public string ActionStock { get; set; }
    }

    /// <summary>
    /// DTO pour la création d'un retour client
    /// </summary>
    public class RetourClientCreateDTO
    {
        public int? FactureVenteId { get; set; }

        public int? ClientId { get; set; }

        [Required(ErrorMessage = "L'utilisateur est obligatoire")]
        public int UtilisateurId { get; set; }

        [Required(ErrorMessage = "La date de retour est obligatoire")]
        public DateTime DateRetour { get; set; } = DateTime.Today;

        [Required(ErrorMessage = "Le motif de retour est obligatoire")]
        public string MotifRetour { get; set; }

        public string DescriptionMotif { get; set; }

        public int? ModeRemboursementId { get; set; }

        [Required(ErrorMessage = "Le retour doit contenir au moins un article")]
        [MinLength(1, ErrorMessage = "Le retour doit contenir au moins un article")]
        public List<DetailRetourClientCreateDTO> Details { get; set; } = new List<DetailRetourClientCreateDTO>();
    }

    /// <summary>
    /// DTO pour la création d'un détail de retour client
    /// </summary>
    public class DetailRetourClientCreateDTO
    {
        [Required(ErrorMessage = "L'article est obligatoire")]
        public int ArticleId { get; set; }

        [Required(ErrorMessage = "La quantité retournée est obligatoire")]
        [Range(0.01, double.MaxValue, ErrorMessage = "La quantité retournée doit être positive")]
        public decimal QuantiteRetournee { get; set; }

        [Required(ErrorMessage = "Le prix unitaire TTC est obligatoire")]
        [Range(0, double.MaxValue, ErrorMessage = "Le prix unitaire TTC doit être positif")]
        public decimal PrixUnitaireTTC { get; set; }

        [Range(0, 100, ErrorMessage = "Le taux de TVA doit être entre 0 et 100")]
        public decimal TvaTaux { get; set; } = 19.00m;

        public string ActionStock { get; set; } = "Remettre_stock";
    }
}
