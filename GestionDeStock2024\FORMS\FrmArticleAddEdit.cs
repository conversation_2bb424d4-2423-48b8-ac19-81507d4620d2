using System;
using System.Drawing;
using System.Windows.Forms;

namespace GestionDeStock2024.FORMS
{
    /// <summary>
    /// Formulaire d'ajout/modification d'article
    /// </summary>
    public partial class FrmArticleAddEdit : Form
    {
        private bool isEditMode = false;
        private string editCode;

        public FrmArticleAddEdit()
        {
            InitializeComponent();
            LoadComboBoxData();
            cmbStatut.SelectedIndex = 0; // Actif par défaut
        }

        public FrmArticleAddEdit(string code) : this()
        {
            isEditMode = true;
            editCode = code;
            this.Text = "Modifier l'article";
            titleLabel.Text = "✏️ Modifier l'article";
            LoadArticleData();
        }

        private void LoadComboBoxData()
        {
            // Charger les catégories
            cmbCategorie.Items.Clear();
            cmbCategorie.Items.AddRange(new[] { 
                "Alimentaire", "Boissons", "Hygiène", "Électronique", 
                "Textile", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>" 
            });

            // Charger les unités
            cmbUnite.Items.Clear();
            cmbUnite.Items.AddRange(new[] { 
                "KG", "G", "L", "ML", "PCS", "BOX", "M", "CM" 
            });

            // Charger les fournisseurs
            cmbFournisseur.Items.Clear();
            cmbFournisseur.Items.AddRange(new[] { 
                "Fournisseur A", "Fournisseur B", "Fournisseur C" 
            });

            // Charger les entrepôts
            cmbEntrepot.Items.Clear();
            cmbEntrepot.Items.AddRange(new[] { 
                "Magasin Principal", "Entrepôt Central", "Dépôt Constantine" 
            });
        }

        private void LoadArticleData()
        {
            if (isEditMode && !string.IsNullOrEmpty(editCode))
            {
                txtCode.Text = editCode;
                txtCode.ReadOnly = true;
                
                // Données fictives pour la démonstration
                switch (editCode)
                {
                    case "ART001":
                        txtNom.Text = "Coca Cola 1.5L";
                        txtCodeBarre.Text = "1234567890123";
                        cmbCategorie.SelectedItem = "Boissons";
                        cmbUnite.SelectedItem = "PCS";
                        txtPrixAchat.Text = "120,00";
                        txtPrixVente.Text = "150,00";
                        txtStockMin.Text = "10";
                        txtStockMax.Text = "100";
                        cmbFournisseur.SelectedItem = "Fournisseur A";
                        cmbEntrepot.SelectedItem = "Magasin Principal";
                        txtDescription.Text = "Boisson gazeuse Coca Cola 1.5L";
                        break;
                    default:
                        txtNom.Text = "Article exemple";
                        txtCodeBarre.Text = "";
                        cmbCategorie.SelectedIndex = 0;
                        cmbUnite.SelectedIndex = 0;
                        txtPrixAchat.Text = "0,00";
                        txtPrixVente.Text = "0,00";
                        txtStockMin.Text = "0";
                        txtStockMax.Text = "0";
                        break;
                }
                cmbStatut.SelectedItem = "Actif";
            }
        }

        private bool ValidateForm()
        {
            if (string.IsNullOrWhiteSpace(txtCode.Text))
            {
                MessageBox.Show("Le code est obligatoire", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtCode.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtNom.Text))
            {
                MessageBox.Show("Le nom est obligatoire", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtNom.Focus();
                return false;
            }

            if (cmbCategorie.SelectedIndex == -1)
            {
                MessageBox.Show("La catégorie est obligatoire", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                cmbCategorie.Focus();
                return false;
            }

            if (cmbUnite.SelectedIndex == -1)
            {
                MessageBox.Show("L'unité est obligatoire", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                cmbUnite.Focus();
                return false;
            }

            // Validation des prix
            if (!decimal.TryParse(txtPrixAchat.Text, out decimal prixAchat) || prixAchat < 0)
            {
                MessageBox.Show("Le prix d'achat doit être un nombre positif", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtPrixAchat.Focus();
                return false;
            }

            if (!decimal.TryParse(txtPrixVente.Text, out decimal prixVente) || prixVente < 0)
            {
                MessageBox.Show("Le prix de vente doit être un nombre positif", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtPrixVente.Focus();
                return false;
            }

            if (prixVente <= prixAchat)
            {
                MessageBox.Show("Le prix de vente doit être supérieur au prix d'achat", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtPrixVente.Focus();
                return false;
            }

            // Validation des stocks
            if (!int.TryParse(txtStockMin.Text, out int stockMin) || stockMin < 0)
            {
                MessageBox.Show("Le stock minimum doit être un nombre positif", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtStockMin.Focus();
                return false;
            }

            if (!int.TryParse(txtStockMax.Text, out int stockMax) || stockMax < 0)
            {
                MessageBox.Show("Le stock maximum doit être un nombre positif", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtStockMax.Focus();
                return false;
            }

            if (stockMax <= stockMin)
            {
                MessageBox.Show("Le stock maximum doit être supérieur au stock minimum", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtStockMax.Focus();
                return false;
            }

            return true;
        }

        private void BtnSave_Click(object sender, EventArgs e)
        {
            if (ValidateForm())
            {
                // Ici, vous ajouteriez la logique pour sauvegarder en base de données
                MessageBox.Show(
                    isEditMode ? "Article modifié avec succès" : "Article ajouté avec succès",
                    "Succès",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private void BtnGenerateBarcode_Click(object sender, EventArgs e)
        {
            // Générer un code-barres automatique
            var random = new Random();
            var barcode = "";
            for (int i = 0; i < 13; i++)
            {
                barcode += random.Next(0, 10).ToString();
            }
            txtCodeBarre.Text = barcode;
        }

        private void TxtPrixAchat_TextChanged(object sender, EventArgs e)
        {
            // Calculer automatiquement le prix de vente avec une marge de 25%
            if (decimal.TryParse(txtPrixAchat.Text, out decimal prixAchat) && prixAchat > 0)
            {
                var prixVente = prixAchat * 1.25m;
                txtPrixVente.Text = prixVente.ToString("F2");
            }
        }

        private void FrmArticleAddEdit_Load(object sender, EventArgs e)
        {
            if (!isEditMode)
            {
                txtCode.Focus();
            }
            else
            {
                txtNom.Focus();
            }
        }
    }
}
