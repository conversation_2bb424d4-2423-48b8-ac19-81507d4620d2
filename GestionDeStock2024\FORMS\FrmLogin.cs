using System;
using System.Drawing;
using System.Threading.Tasks;
using System.Windows.Forms;
using GestionDeStock2024.DATA;
using GestionDeStock2024.MODELS;
using GestionDeStock2024.UTILS;
using Dapper;

namespace GestionDeStock2024.FORMS
{
    /// <summary>
    /// Formulaire de connexion utilisateur
    /// </summary>
    public partial class FrmLogin : Form
    {
        private bool isLoggingIn = false;
        private int loginAttempts = 0;
        private const int MAX_LOGIN_ATTEMPTS = 3;

        public Utilisateur CurrentUser { get; private set; }

        public FrmLogin()
        {
            InitializeComponent();
            InitializeForm();
        }

        private void InitializeForm()
        {
            // Configuration du formulaire
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.StartPosition = FormStartPosition.CenterScreen;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.ShowInTaskbar = true;
            this.KeyPreview = true;

            // Appliquer le thème moderne
            ApplyModernTheme();

            // Charger les valeurs par défaut
            LoadDefaultValues();
        }

        private void ApplyModernTheme()
        {
            // Couleurs modernes
            this.BackColor = Color.FromArgb(45, 45, 48);
            
            // Style du panel principal
            panelMain.BackColor = Color.White;
            panelMain.BorderStyle = BorderStyle.None;

            // Style du panel de connexion
            panelLogin.BackColor = Color.White;

            // Style des labels
            lblTitle.ForeColor = Color.FromArgb(64, 64, 64);
            lblTitle.Font = new Font("Segoe UI", 20, FontStyle.Bold);
            
            lblSubtitle.ForeColor = Color.FromArgb(128, 128, 128);
            lblSubtitle.Font = new Font("Segoe UI", 10);
            
            lblUsername.ForeColor = Color.FromArgb(64, 64, 64);
            lblUsername.Font = new Font("Segoe UI", 9, FontStyle.Bold);
            
            lblPassword.ForeColor = Color.FromArgb(64, 64, 64);
            lblPassword.Font = new Font("Segoe UI", 9, FontStyle.Bold);
            
            lblStatus.ForeColor = Color.FromArgb(244, 67, 54);
            lblStatus.Font = new Font("Segoe UI", 9);

            // Style des textboxes
            ApplyTextBoxTheme(txtUsername);
            ApplyTextBoxTheme(txtPassword);

            // Style des boutons
            ApplyButtonTheme(btnLogin, Color.FromArgb(0, 122, 204));
            ApplyButtonTheme(btnCancel, Color.FromArgb(158, 158, 158));

            // Style des liens
            lblForgotPassword.ForeColor = Color.FromArgb(0, 122, 204);
            lblForgotPassword.Font = new Font("Segoe UI", 9, FontStyle.Underline);
            lblForgotPassword.Cursor = Cursors.Hand;
        }

        private void ApplyTextBoxTheme(TextBox textBox)
        {
            textBox.BorderStyle = BorderStyle.FixedSingle;
            textBox.Font = new Font("Segoe UI", 11);
            textBox.BackColor = Color.White;
            textBox.ForeColor = Color.FromArgb(64, 64, 64);
            textBox.Height = 35;
        }

        private void ApplyButtonTheme(Button button, Color backgroundColor)
        {
            button.FlatStyle = FlatStyle.Flat;
            button.Font = new Font("Segoe UI", 10, FontStyle.Bold);
            button.BackColor = backgroundColor;
            button.ForeColor = Color.White;
            button.FlatAppearance.BorderSize = 0;
            button.Cursor = Cursors.Hand;
            button.Height = 40;
        }

        private void LoadDefaultValues()
        {
            txtUsername.Text = "admin";
            txtPassword.Text = "";
            lblStatus.Text = "";
            
            // Focus sur le nom d'utilisateur ou mot de passe selon le cas
            if (string.IsNullOrEmpty(txtUsername.Text))
                txtUsername.Focus();
            else
                txtPassword.Focus();
        }

        private async void btnLogin_Click(object sender, EventArgs e)
        {
            if (isLoggingIn) return;

            await PerformLogin();
        }

        private async Task PerformLogin()
        {
            if (string.IsNullOrWhiteSpace(txtUsername.Text))
            {
                ShowStatus("Veuillez saisir votre nom d'utilisateur", StatusType.Error);
                txtUsername.Focus();
                return;
            }

            if (string.IsNullOrWhiteSpace(txtPassword.Text))
            {
                ShowStatus("Veuillez saisir votre mot de passe", StatusType.Error);
                txtPassword.Focus();
                return;
            }

            isLoggingIn = true;
            btnLogin.Enabled = false;
            btnLogin.Text = "Connexion...";
            ShowStatus("Vérification des identifiants...", StatusType.Info);

            try
            {
                var user = await AuthenticateUser(txtUsername.Text.Trim(), txtPassword.Text);

                if (user != null)
                {
                    // Vérifier le statut de l'utilisateur
                    if (user.Statut != "Actif")
                    {
                        ShowStatus("Votre compte est désactivé. Contactez l'administrateur.", StatusType.Error);
                        return;
                    }

                    // Connexion réussie
                    CurrentUser = user;
                    ShowStatus("Connexion réussie ! Ouverture de l'application...", StatusType.Success);
                    
                    await Task.Delay(1000); // Petit délai pour montrer le message de succès
                    
                    // Enregistrer la dernière connexion
                    await UpdateLastLogin(user.Id);
                    
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                else
                {
                    loginAttempts++;
                    ShowStatus($"Nom d'utilisateur ou mot de passe incorrect. Tentative {loginAttempts}/{MAX_LOGIN_ATTEMPTS}", StatusType.Error);
                    
                    if (loginAttempts >= MAX_LOGIN_ATTEMPTS)
                    {
                        ShowStatus("Trop de tentatives échouées. Application fermée.", StatusType.Error);
                        await Task.Delay(2000);
                        Application.Exit();
                        return;
                    }
                    
                    txtPassword.Clear();
                    txtPassword.Focus();
                }
            }
            catch (Exception ex)
            {
                ShowStatus($"Erreur de connexion : {ex.Message}", StatusType.Error);
            }
            finally
            {
                isLoggingIn = false;
                btnLogin.Enabled = true;
                btnLogin.Text = "Se connecter";
            }
        }

        private async Task<Utilisateur> AuthenticateUser(string username, string password)
        {
            try
            {
                var dbConnection = new DatabaseConnection();
                using var connection = await dbConnection.GetConnectionAsync();
                
                var sql = @"
                    SELECT id, nom_utilisateur, nom_complet, email, role, statut, mot_de_passe,
                           derniere_connexion, created_at, updated_at
                    FROM utilisateurs
                    WHERE nom_utilisateur = @Username
                    AND statut = 'Actif'";

                var user = await connection.QueryFirstOrDefaultAsync<Utilisateur>(sql, new
                {
                    Username = username
                });

                // Vérifier le mot de passe
                if (user != null)
                {
                    bool isPasswordValid = false;

                    // Vérifier si le mot de passe est haché avec BCrypt
                    if (user.MotDePasse.StartsWith("$2a$") || user.MotDePasse.StartsWith("$2b$") || user.MotDePasse.StartsWith("$2y$"))
                    {
                        // Mot de passe haché avec BCrypt
                        isPasswordValid = PasswordHelper.VerifyPassword(password, user.MotDePasse);
                    }
                    else
                    {
                        // Mot de passe en clair (pour compatibilité temporaire)
                        isPasswordValid = (user.MotDePasse == password);

                        // Si le mot de passe est correct, le hacher et le mettre à jour
                        if (isPasswordValid)
                        {
                            await UpdatePasswordToBCrypt(user.Id, password);
                        }
                    }

                    if (isPasswordValid)
                    {
                        return user;
                    }
                    return null; // Mot de passe incorrect
                }

                return user;
            }
            catch (Exception ex)
            {
                throw new Exception($"Erreur lors de l'authentification : {ex.Message}");
            }
        }

        private async Task UpdatePasswordToBCrypt(int userId, string plainPassword)
        {
            try
            {
                var dbConnection = new DatabaseConnection();
                using var connection = await dbConnection.GetConnectionAsync();

                var hashedPassword = PasswordHelper.HashPassword(plainPassword);

                var sql = @"
                    UPDATE utilisateurs
                    SET mot_de_passe = @MotDePasse, updated_at = @UpdatedAt
                    WHERE id = @UserId";

                await connection.ExecuteAsync(sql, new
                {
                    MotDePasse = hashedPassword,
                    UpdatedAt = DateTime.Now,
                    UserId = userId
                });
            }
            catch (Exception ex)
            {
                // Log l'erreur mais ne pas empêcher la connexion
                System.Diagnostics.Debug.WriteLine($"Erreur lors de la mise à jour du mot de passe : {ex.Message}");
            }
        }



        private async Task UpdateLastLogin(int userId)
        {
            try
            {
                var dbConnection = new DatabaseConnection();
                using var connection = await dbConnection.GetConnectionAsync();
                
                var sql = @"
                    UPDATE utilisateurs 
                    SET derniere_connexion = @LastLogin, updated_at = @UpdatedAt
                    WHERE id = @UserId";

                await connection.ExecuteAsync(sql, new 
                { 
                    LastLogin = DateTime.Now,
                    UpdatedAt = DateTime.Now,
                    UserId = userId
                });
            }
            catch
            {
                // Ignorer les erreurs de mise à jour de la dernière connexion
            }
        }

        private void ShowStatus(string message, StatusType type)
        {
            lblStatus.Text = message;
            
            switch (type)
            {
                case StatusType.Success:
                    lblStatus.ForeColor = Color.FromArgb(76, 175, 80);
                    break;
                case StatusType.Error:
                    lblStatus.ForeColor = Color.FromArgb(244, 67, 54);
                    break;
                case StatusType.Info:
                    lblStatus.ForeColor = Color.FromArgb(0, 122, 204);
                    break;
                case StatusType.Warning:
                    lblStatus.ForeColor = Color.FromArgb(255, 152, 0);
                    break;
            }
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private void FrmLogin_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                e.Handled = true;
                _ = PerformLogin();
            }
            else if (e.KeyCode == Keys.Escape)
            {
                btnCancel_Click(sender, e);
            }
        }

        private void txtPassword_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                e.Handled = true;
                _ = PerformLogin();
            }
        }

        private void txtUsername_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                e.Handled = true;
                txtPassword.Focus();
            }
        }

        private void lblForgotPassword_Click(object sender, EventArgs e)
        {
            MessageBox.Show(
                "Pour réinitialiser votre mot de passe, contactez l'administrateur système.\n\n" +
                "Email : <EMAIL>\n" +
                "Téléphone : +213 XXX XXX XXX",
                "Mot de passe oublié",
                MessageBoxButtons.OK,
                MessageBoxIcon.Information);
        }

        private void FrmLogin_Load(object sender, EventArgs e)
        {
            // Vérifier la connexion à la base de données
            _ = CheckDatabaseConnection();
        }

        private async Task CheckDatabaseConnection()
        {
            try
            {
                var dbConnection = new DatabaseConnection();
                bool isConnected = await dbConnection.TestConnectionAsync();

                if (!isConnected)
                {
                    ShowStatus("Erreur de connexion à la base de données", StatusType.Error);
                    btnLogin.Enabled = false;
                }
            }
            catch (Exception ex)
            {
                ShowStatus($"Erreur de base de données : {ex.Message}", StatusType.Error);
                btnLogin.Enabled = false;
            }
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            base.OnPaint(e);
            
            // Dessiner une bordure moderne
            using (var pen = new Pen(Color.FromArgb(0, 122, 204), 2))
            {
                e.Graphics.DrawRectangle(pen, 0, 0, this.Width - 1, this.Height - 1);
            }
        }

        private enum StatusType
        {
            Success,
            Error,
            Info,
            Warning
        }
    }
}
