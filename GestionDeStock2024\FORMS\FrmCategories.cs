using System;
using System.Drawing;
using System.Windows.Forms;
using GestionDeStock2024.MODELS;

namespace GestionDeStock2024.FORMS
{
    /// <summary>
    /// Formulaire de gestion des catégories
    /// </summary>
    public partial class FrmCategories : Form
    {
        private Utilisateur currentUser;
        private DataGridView dgvCategories;
        private TextBox txtSearch;
        private Button btnAdd, btnEdit, btnDelete, btnRefresh;
        private ComboBox cmbStatut;

        public FrmCategories()
        {
            InitializeComponent();
            InitializeForm();
        }

        public FrmCategories(Utilisateur user) : this()
        {
            currentUser = user;
            LoadCategories();
        }

        private void InitializeForm()
        {
            this.Text = "Gestion des Catégories";
            this.BackColor = Color.White;
            this.Size = new Size(1000, 700);
            this.WindowState = FormWindowState.Maximized;

            CreateLayout();
            ApplyModernTheme();
        }

        private void CreateLayout()
        {
            var mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20),
                BackColor = Color.FromArgb(248, 249, 250)
            };
            this.Controls.Add(mainPanel);

            // Titre
            var titleLabel = new Label
            {
                Text = "🏷️ Gestion des Catégories",
                Font = new Font("Segoe UI", 24, FontStyle.Bold),
                ForeColor = Color.FromArgb(33, 37, 41),
                AutoSize = true,
                Location = new Point(0, 0)
            };
            mainPanel.Controls.Add(titleLabel);

            // Panel des filtres et boutons
            var toolbarPanel = new Panel
            {
                Location = new Point(0, 60),
                Size = new Size(mainPanel.Width - 40, 60),
                Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right,
                BackColor = Color.White
            };
            mainPanel.Controls.Add(toolbarPanel);

            CreateToolbar(toolbarPanel);

            // DataGridView
            dgvCategories = new DataGridView
            {
                Location = new Point(0, 140),
                Size = new Size(mainPanel.Width - 40, mainPanel.Height - 180),
                Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
            };
            mainPanel.Controls.Add(dgvCategories);

            SetupDataGridView();
        }

        private void CreateToolbar(Panel parent)
        {
            // Recherche
            var lblSearch = new Label
            {
                Text = "🔍 Rechercher:",
                Font = new Font("Segoe UI", 10, FontStyle.Bold),
                ForeColor = Color.FromArgb(73, 80, 87),
                Location = new Point(10, 20),
                AutoSize = true
            };
            parent.Controls.Add(lblSearch);

            txtSearch = new TextBox
            {
                Location = new Point(110, 17),
                Size = new Size(200, 25),
                Font = new Font("Segoe UI", 10),
            };
            txtSearch.TextChanged += TxtSearch_TextChanged;
            parent.Controls.Add(txtSearch);

            // Statut
            var lblStatut = new Label
            {
                Text = "Statut:",
                Font = new Font("Segoe UI", 10),
                ForeColor = Color.FromArgb(73, 80, 87),
                Location = new Point(330, 20),
                AutoSize = true
            };
            parent.Controls.Add(lblStatut);

            cmbStatut = new ComboBox
            {
                Location = new Point(380, 17),
                Size = new Size(120, 25),
                Font = new Font("Segoe UI", 10),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            cmbStatut.Items.AddRange(new[] { "Tous", "Actif", "Inactif" });
            cmbStatut.SelectedIndex = 0;
            cmbStatut.SelectedIndexChanged += CmbStatut_SelectedIndexChanged;
            parent.Controls.Add(cmbStatut);

            // Boutons
            btnAdd = CreateButton("➕ Ajouter", new Point(520, 15), Color.FromArgb(40, 167, 69));
            btnAdd.Click += BtnAdd_Click;
            parent.Controls.Add(btnAdd);

            btnEdit = CreateButton("✏️ Modifier", new Point(630, 15), Color.FromArgb(0, 123, 255));
            btnEdit.Click += BtnEdit_Click;
            parent.Controls.Add(btnEdit);

            btnDelete = CreateButton("🗑️ Supprimer", new Point(740, 15), Color.FromArgb(220, 53, 69));
            btnDelete.Click += BtnDelete_Click;
            parent.Controls.Add(btnDelete);

            btnRefresh = CreateButton("🔄", new Point(860, 15), Color.FromArgb(108, 117, 125));
            btnRefresh.Size = new Size(35, 30);
            btnRefresh.Click += BtnRefresh_Click;
            parent.Controls.Add(btnRefresh);
        }

        private Button CreateButton(string text, Point location, Color backColor)
        {
            return new Button
            {
                Text = text,
                Location = location,
                Size = new Size(100, 30),
                Font = new Font("Segoe UI", 9, FontStyle.Bold),
                BackColor = backColor,
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                FlatAppearance = { BorderSize = 0 },
                Cursor = Cursors.Hand
            };
        }

        private void SetupDataGridView()
        {
            dgvCategories.Columns.Clear();

            dgvCategories.Columns.Add("Code", "Code");
            dgvCategories.Columns.Add("Nom", "Nom");
            dgvCategories.Columns.Add("Description", "Description");
            dgvCategories.Columns.Add("CategorieParent", "Catégorie parent");
            dgvCategories.Columns.Add("NombreArticles", "Nb. articles");
            dgvCategories.Columns.Add("Statut", "Statut");

            dgvCategories.Columns["Code"].Width = 100;
            dgvCategories.Columns["Nom"].Width = 200;
            dgvCategories.Columns["Description"].Width = 250;
            dgvCategories.Columns["CategorieParent"].Width = 150;
            dgvCategories.Columns["NombreArticles"].Width = 100;
            dgvCategories.Columns["Statut"].Width = 80;

            dgvCategories.Columns["NombreArticles"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
        }

        private void ApplyModernTheme()
        {
            dgvCategories.EnableHeadersVisualStyles = false;
            dgvCategories.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(52, 58, 64);
            dgvCategories.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvCategories.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 10, FontStyle.Bold);
            dgvCategories.ColumnHeadersHeight = 40;

            dgvCategories.DefaultCellStyle.BackColor = Color.White;
            dgvCategories.DefaultCellStyle.ForeColor = Color.FromArgb(33, 37, 41);
            dgvCategories.DefaultCellStyle.Font = new Font("Segoe UI", 9);
            dgvCategories.DefaultCellStyle.SelectionBackColor = Color.FromArgb(0, 123, 255);
            dgvCategories.DefaultCellStyle.SelectionForeColor = Color.White;

            dgvCategories.AlternatingRowsDefaultCellStyle.BackColor = Color.FromArgb(248, 249, 250);
            dgvCategories.GridColor = Color.FromArgb(222, 226, 230);
            dgvCategories.RowHeadersVisible = false;
            dgvCategories.CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal;
        }

        private void LoadCategories()
        {
            dgvCategories.Rows.Clear();
            
            var sampleData = new[]
            {
                new object[] { "ALI", "Alimentaire", "Produits alimentaires de base", "", 156, "Actif" },
                new object[] { "ALI-LAI", "Produits laitiers", "Lait, fromage, yaourt", "Alimentaire", 23, "Actif" },
                new object[] { "ALI-VIA", "Viandes", "Viandes fraîches et charcuterie", "Alimentaire", 45, "Actif" },
                new object[] { "ALI-LEG", "Légumes", "Légumes frais et surgelés", "Alimentaire", 67, "Actif" },
                new object[] { "BOI", "Boissons", "Boissons diverses", "", 89, "Actif" },
                new object[] { "BOI-EAU", "Eaux", "Eaux minérales et gazeuses", "Boissons", 12, "Actif" },
                new object[] { "BOI-JUS", "Jus", "Jus de fruits et nectars", "Boissons", 34, "Actif" },
                new object[] { "HYG", "Hygiène", "Produits d'hygiène et cosmétiques", "", 78, "Actif" },
                new object[] { "HYG-COR", "Hygiène corporelle", "Savons, shampoings, déodorants", "Hygiène", 45, "Actif" },
                new object[] { "ELE", "Électronique", "Appareils électroniques", "", 23, "Actif" }
            };

            foreach (var row in sampleData)
            {
                dgvCategories.Rows.Add(row);
            }
        }

        #region Event Handlers

        private void TxtSearch_TextChanged(object sender, EventArgs e)
        {
            FilterCategories();
        }

        private void CmbStatut_SelectedIndexChanged(object sender, EventArgs e)
        {
            FilterCategories();
        }

        private void FilterCategories()
        {
            LoadCategories();
        }

        private void BtnAdd_Click(object sender, EventArgs e)
        {
            var frmCategoryAddEdit = new FrmCategoryAddEdit();
            if (frmCategoryAddEdit.ShowDialog() == DialogResult.OK)
            {
                LoadCategories();
            }
        }

        private void BtnEdit_Click(object sender, EventArgs e)
        {
            if (dgvCategories.SelectedRows.Count > 0)
            {
                var selectedRow = dgvCategories.SelectedRows[0];
                var code = selectedRow.Cells["Code"].Value?.ToString();
                
                var frmCategoryAddEdit = new FrmCategoryAddEdit(code);
                if (frmCategoryAddEdit.ShowDialog() == DialogResult.OK)
                {
                    LoadCategories();
                }
            }
            else
            {
                MessageBox.Show("Veuillez sélectionner une catégorie à modifier", "Attention", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private void BtnDelete_Click(object sender, EventArgs e)
        {
            if (dgvCategories.SelectedRows.Count > 0)
            {
                var selectedRow = dgvCategories.SelectedRows[0];
                var nbArticles = Convert.ToInt32(selectedRow.Cells["NombreArticles"].Value);
                
                if (nbArticles > 0)
                {
                    MessageBox.Show("Impossible de supprimer cette catégorie car elle contient des articles", "Attention", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }
                
                var result = MessageBox.Show("Êtes-vous sûr de vouloir supprimer cette catégorie ?", "Confirmation", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                if (result == DialogResult.Yes)
                {
                    LoadCategories();
                    MessageBox.Show("Catégorie supprimée avec succès", "Succès", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            else
            {
                MessageBox.Show("Veuillez sélectionner une catégorie à supprimer", "Attention", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private void BtnRefresh_Click(object sender, EventArgs e)
        {
            LoadCategories();
        }

        #endregion

        private void FrmCategories_Load(object sender, EventArgs e)
        {
            LoadCategories();
        }
    }
}
