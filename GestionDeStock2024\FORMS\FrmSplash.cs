using System;
using System.Drawing;
using System.Threading.Tasks;
using System.Windows.Forms;
using GestionDeStock2024.DATA;
using GestionDeStock2024.UTILS;

namespace GestionDeStock2024.FORMS
{
    /// <summary>
    /// Formulaire de démarrage (Splash Screen)
    /// </summary>
    public partial class FrmSplash : Form
    {
        private Timer progressTimer;
        private int progressValue = 0;
        private string currentStatus = "Initialisation...";

        public FrmSplash()
        {
            InitializeComponent();
            InitializeForm();
        }

        private void InitializeForm()
        {
            // Configuration du formulaire
            this.FormBorderStyle = FormBorderStyle.None;
            this.StartPosition = FormStartPosition.CenterScreen;
            this.TopMost = true;
            this.ShowInTaskbar = false;

            // Initialiser le timer de progression
            progressTimer = new Timer();
            progressTimer.Interval = 50;
            progressTimer.Tick += ProgressTimer_Tick;

            // Appliquer le thème moderne
            ApplyModernTheme();
        }

        private void ApplyModernTheme()
        {
            // Couleurs modernes
            this.BackColor = Color.FromArgb(45, 45, 48);
            
            // Style des labels
            lblAppName.ForeColor = Color.White;
            lblAppName.Font = new Font("Segoe UI", 24, FontStyle.Bold);
            
            lblVersion.ForeColor = Color.FromArgb(200, 200, 200);
            lblVersion.Font = new Font("Segoe UI", 10);
            
            lblStatus.ForeColor = Color.FromArgb(0, 122, 204);
            lblStatus.Font = new Font("Segoe UI", 9);
            
            lblCopyright.ForeColor = Color.FromArgb(150, 150, 150);
            lblCopyright.Font = new Font("Segoe UI", 8);

            // Style de la barre de progression
            progressBar.BackColor = Color.FromArgb(60, 60, 60);
            progressBar.ForeColor = Color.FromArgb(0, 122, 204);
        }

        private async void FrmSplash_Load(object sender, EventArgs e)
        {
            // Démarrer l'animation de progression
            progressTimer.Start();

            // Effectuer les vérifications de démarrage
            await PerformStartupChecks();
        }

        private void ProgressTimer_Tick(object sender, EventArgs e)
        {
            if (progressValue < 100)
            {
                progressValue += 2;
                progressBar.Value = Math.Min(progressValue, 100);
                lblStatus.Text = currentStatus;
            }
        }

        private async Task PerformStartupChecks()
        {
            try
            {
                // Étape 1: Vérification des fichiers de configuration
                await UpdateProgress(10, "Vérification de la configuration...");
                await Task.Delay(500);

                var configErrors = ConfigurationManager.ValidateConfiguration();
                if (configErrors.Count > 0)
                {
                    await UpdateProgress(20, "Configuration incomplète...");
                    await Task.Delay(500);
                }

                // Étape 2: Vérification de la connexion à la base de données
                await UpdateProgress(30, "Vérification de la base de données...");
                await Task.Delay(500);

                var dbConnection = new DatabaseConnection();
                bool connectionExists = await dbConnection.TestConnectionAsync();

                if (!connectionExists)
                {
                    await UpdateProgress(50, "Base de données non configurée...");
                    await Task.Delay(1000);
                    
                    // Ouvrir le formulaire de configuration de base de données
                    await UpdateProgress(100, "Ouverture de la configuration...");
                    await Task.Delay(500);
                    
                    progressTimer.Stop();
                    OpenDatabaseConfigurationForm();
                    return;
                }

                // Étape 3: Vérification de la structure de la base de données
                await UpdateProgress(60, "Vérification de la structure...");
                await Task.Delay(500);

                bool structureValid = await dbConnection.ValidateDatabaseStructureAsync();
                if (!structureValid)
                {
                    await UpdateProgress(70, "Structure de base de données invalide...");
                    await Task.Delay(1000);
                    
                    progressTimer.Stop();
                    OpenDatabaseConfigurationForm();
                    return;
                }

                // Étape 4: Chargement des paramètres
                await UpdateProgress(80, "Chargement des paramètres...");
                await Task.Delay(500);

                // Étape 5: Finalisation
                await UpdateProgress(90, "Finalisation...");
                await Task.Delay(500);

                await UpdateProgress(100, "Démarrage terminé !");
                await Task.Delay(500);

                progressTimer.Stop();

                // Ouvrir le formulaire de connexion
                OpenLoginForm();
            }
            catch (Exception ex)
            {
                progressTimer.Stop();
                await UpdateProgress(100, "Erreur de démarrage !");
                
                MessageBox.Show(
                    $"Erreur lors du démarrage de l'application :\n\n{ex.Message}",
                    "Erreur de démarrage",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);

                Application.Exit();
            }
        }

        private async Task UpdateProgress(int value, string status)
        {
            progressValue = value;
            currentStatus = status;
            
            // Forcer la mise à jour de l'interface
            progressBar.Value = value;
            lblStatus.Text = status;
            
            this.Refresh();
            await Task.Delay(100);
        }

        private void OpenDatabaseConfigurationForm()
        {
            this.Hide();
            
            var frmDatabase = new FrmDatabase();
            frmDatabase.FormClosed += (s, e) => {
                // Redémarrer les vérifications après configuration
                this.Show();
                progressValue = 0;
                progressTimer.Start();
                _ = PerformStartupChecks();
            };
            
            frmDatabase.Show();
        }

        private void OpenLoginForm()
        {
            this.Hide();
            
            var frmLogin = new FrmLogin();
            frmLogin.FormClosed += (s, e) => {
                if (frmLogin.DialogResult == DialogResult.OK && frmLogin.CurrentUser != null)
                {
                    // Ouvrir le formulaire principal avec l'utilisateur connecté
                    var frmMain = new FrmMain(frmLogin.CurrentUser);
                    frmMain.Show();
                }
                else
                {
                    // Fermer l'application si connexion annulée
                    Application.Exit();
                }
                this.Close();
            };
            
            frmLogin.ShowDialog();
        }

        private void FrmSplash_KeyDown(object sender, KeyEventArgs e)
        {
            // Permettre de fermer avec Escape
            if (e.KeyCode == Keys.Escape)
            {
                Application.Exit();
            }
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            base.OnPaint(e);
            
            // Dessiner une bordure moderne
            using (var pen = new Pen(Color.FromArgb(0, 122, 204), 2))
            {
                e.Graphics.DrawRectangle(pen, 0, 0, this.Width - 1, this.Height - 1);
            }
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                progressTimer?.Dispose();
                components?.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}
