using System;
using System.Drawing;
using System.Windows.Forms;
using GestionDeStock2024.MODELS;

namespace GestionDeStock2024.FORMS
{
    /// <summary>
    /// Formulaire de gestion des entrepôts/magasins
    /// </summary>
    public partial class FrmWarehouses : Form
    {
        private Utilisateur currentUser;
        private DataGridView dgvWarehouses;
        private TextBox txtSearch;
        private Button btnAdd, btnEdit, btnDelete, btnRefresh;
        private ComboBox cmbType, cmbStatut;

        public FrmWarehouses()
        {
            InitializeComponent();
            InitializeForm();
        }

        public FrmWarehouses(Utilisateur user) : this()
        {
            currentUser = user;
            LoadWarehouses();
        }

        private void InitializeForm()
        {
            this.Text = "Gestion des Entrepôts/Magasins";
            this.BackColor = Color.White;
            this.Size = new Size(1200, 800);
            this.WindowState = FormWindowState.Maximized;

            CreateLayout();
            ApplyModernTheme();
        }

        private void CreateLayout()
        {
            var mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20),
                BackColor = Color.FromArgb(248, 249, 250)
            };
            this.Controls.Add(mainPanel);

            // Titre
            var titleLabel = new Label
            {
                Text = "🏪 Gestion des Entrepôts/Magasins",
                Font = new Font("Segoe UI", 24, FontStyle.Bold),
                ForeColor = Color.FromArgb(33, 37, 41),
                AutoSize = true,
                Location = new Point(0, 0)
            };
            mainPanel.Controls.Add(titleLabel);

            // Panel des filtres et boutons
            var toolbarPanel = new Panel
            {
                Location = new Point(0, 60),
                Size = new Size(mainPanel.Width - 40, 60),
                Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right,
                BackColor = Color.White
            };
            mainPanel.Controls.Add(toolbarPanel);

            CreateToolbar(toolbarPanel);

            // DataGridView
            dgvWarehouses = new DataGridView
            {
                Location = new Point(0, 140),
                Size = new Size(mainPanel.Width - 40, mainPanel.Height - 180),
                Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
            };
            mainPanel.Controls.Add(dgvWarehouses);

            SetupDataGridView();
        }

        private void CreateToolbar(Panel parent)
        {
            // Recherche
            var lblSearch = new Label
            {
                Text = "🔍 Rechercher:",
                Font = new Font("Segoe UI", 10, FontStyle.Bold),
                ForeColor = Color.FromArgb(73, 80, 87),
                Location = new Point(10, 20),
                AutoSize = true
            };
            parent.Controls.Add(lblSearch);

            txtSearch = new TextBox
            {
                Location = new Point(110, 17),
                Size = new Size(200, 25),
                Font = new Font("Segoe UI", 10),
            };
            txtSearch.TextChanged += TxtSearch_TextChanged;
            parent.Controls.Add(txtSearch);

            // Type
            var lblType = new Label
            {
                Text = "Type:",
                Font = new Font("Segoe UI", 10),
                ForeColor = Color.FromArgb(73, 80, 87),
                Location = new Point(330, 20),
                AutoSize = true
            };
            parent.Controls.Add(lblType);

            cmbType = new ComboBox
            {
                Location = new Point(370, 17),
                Size = new Size(120, 25),
                Font = new Font("Segoe UI", 10),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            cmbType.Items.AddRange(new[] { "Tous", "Magasin", "Entrepôt", "Dépôt" });
            cmbType.SelectedIndex = 0;
            cmbType.SelectedIndexChanged += CmbType_SelectedIndexChanged;
            parent.Controls.Add(cmbType);

            // Statut
            var lblStatut = new Label
            {
                Text = "Statut:",
                Font = new Font("Segoe UI", 10),
                ForeColor = Color.FromArgb(73, 80, 87),
                Location = new Point(510, 20),
                AutoSize = true
            };
            parent.Controls.Add(lblStatut);

            cmbStatut = new ComboBox
            {
                Location = new Point(560, 17),
                Size = new Size(100, 25),
                Font = new Font("Segoe UI", 10),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            cmbStatut.Items.AddRange(new[] { "Tous", "Actif", "Inactif" });
            cmbStatut.SelectedIndex = 0;
            cmbStatut.SelectedIndexChanged += CmbStatut_SelectedIndexChanged;
            parent.Controls.Add(cmbStatut);

            // Boutons
            btnAdd = CreateButton("➕ Ajouter", new Point(680, 15), Color.FromArgb(40, 167, 69));
            btnAdd.Click += BtnAdd_Click;
            parent.Controls.Add(btnAdd);

            btnEdit = CreateButton("✏️ Modifier", new Point(790, 15), Color.FromArgb(0, 123, 255));
            btnEdit.Click += BtnEdit_Click;
            parent.Controls.Add(btnEdit);

            btnDelete = CreateButton("🗑️ Supprimer", new Point(900, 15), Color.FromArgb(220, 53, 69));
            btnDelete.Click += BtnDelete_Click;
            parent.Controls.Add(btnDelete);

            btnRefresh = CreateButton("🔄", new Point(1020, 15), Color.FromArgb(108, 117, 125));
            btnRefresh.Size = new Size(35, 30);
            btnRefresh.Click += BtnRefresh_Click;
            parent.Controls.Add(btnRefresh);
        }

        private Button CreateButton(string text, Point location, Color backColor)
        {
            return new Button
            {
                Text = text,
                Location = location,
                Size = new Size(100, 30),
                Font = new Font("Segoe UI", 9, FontStyle.Bold),
                BackColor = backColor,
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                FlatAppearance = { BorderSize = 0 },
                Cursor = Cursors.Hand
            };
        }

        private void SetupDataGridView()
        {
            dgvWarehouses.Columns.Clear();

            dgvWarehouses.Columns.Add("Code", "Code");
            dgvWarehouses.Columns.Add("Nom", "Nom");
            dgvWarehouses.Columns.Add("Type", "Type");
            dgvWarehouses.Columns.Add("Adresse", "Adresse");
            dgvWarehouses.Columns.Add("Telephone", "Téléphone");
            dgvWarehouses.Columns.Add("Responsable", "Responsable");
            dgvWarehouses.Columns.Add("Capacite", "Capacité (m²)");
            dgvWarehouses.Columns.Add("Statut", "Statut");

            dgvWarehouses.Columns["Code"].Width = 80;
            dgvWarehouses.Columns["Nom"].Width = 200;
            dgvWarehouses.Columns["Type"].Width = 100;
            dgvWarehouses.Columns["Adresse"].Width = 250;
            dgvWarehouses.Columns["Telephone"].Width = 120;
            dgvWarehouses.Columns["Responsable"].Width = 150;
            dgvWarehouses.Columns["Capacite"].Width = 100;
            dgvWarehouses.Columns["Statut"].Width = 80;

            dgvWarehouses.Columns["Capacite"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
        }

        private void ApplyModernTheme()
        {
            dgvWarehouses.EnableHeadersVisualStyles = false;
            dgvWarehouses.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(52, 58, 64);
            dgvWarehouses.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvWarehouses.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 10, FontStyle.Bold);
            dgvWarehouses.ColumnHeadersHeight = 40;

            dgvWarehouses.DefaultCellStyle.BackColor = Color.White;
            dgvWarehouses.DefaultCellStyle.ForeColor = Color.FromArgb(33, 37, 41);
            dgvWarehouses.DefaultCellStyle.Font = new Font("Segoe UI", 9);
            dgvWarehouses.DefaultCellStyle.SelectionBackColor = Color.FromArgb(0, 123, 255);
            dgvWarehouses.DefaultCellStyle.SelectionForeColor = Color.White;

            dgvWarehouses.AlternatingRowsDefaultCellStyle.BackColor = Color.FromArgb(248, 249, 250);
            dgvWarehouses.GridColor = Color.FromArgb(222, 226, 230);
            dgvWarehouses.RowHeadersVisible = false;
            dgvWarehouses.CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal;
        }

        private void LoadWarehouses()
        {
            dgvWarehouses.Rows.Clear();
            
            var sampleData = new[]
            {
                new object[] { "MAG001", "Magasin Principal Alger", "Magasin", "Rue Didouche Mourad, Alger Centre", "021-123456", "Ahmed Benali", 500, "Actif" },
                new object[] { "ENT001", "Entrepôt Central", "Entrepôt", "Zone Industrielle Rouiba, Alger", "021-789012", "Fatima Khelifi", 2000, "Actif" },
                new object[] { "MAG002", "Magasin Oran", "Magasin", "Boulevard Mohamed V, Oran", "041-345678", "Mohamed Tabet", 400, "Actif" },
                new object[] { "DEP001", "Dépôt Constantine", "Dépôt", "Rue Larbi Ben M'hidi, Constantine", "031-901234", "Amina Cherif", 800, "Actif" },
                new object[] { "MAG003", "Magasin Annaba", "Magasin", "Avenue de l'ALN, Annaba", "038-567890", "Karim Boudiaf", 350, "Actif" },
                new object[] { "ENT002", "Entrepôt Froid", "Entrepôt", "Zone Industrielle Sétif", "036-234567", "Nadia Hamidi", 1200, "Actif" }
            };

            foreach (var row in sampleData)
            {
                dgvWarehouses.Rows.Add(row);
            }
        }

        #region Event Handlers

        private void TxtSearch_TextChanged(object sender, EventArgs e)
        {
            FilterWarehouses();
        }

        private void CmbType_SelectedIndexChanged(object sender, EventArgs e)
        {
            FilterWarehouses();
        }

        private void CmbStatut_SelectedIndexChanged(object sender, EventArgs e)
        {
            FilterWarehouses();
        }

        private void FilterWarehouses()
        {
            LoadWarehouses();
        }

        private void BtnAdd_Click(object sender, EventArgs e)
        {
            var frmWarehouseAddEdit = new FrmWarehouseAddEdit();
            if (frmWarehouseAddEdit.ShowDialog() == DialogResult.OK)
            {
                LoadWarehouses();
            }
        }

        private void BtnEdit_Click(object sender, EventArgs e)
        {
            if (dgvWarehouses.SelectedRows.Count > 0)
            {
                var selectedRow = dgvWarehouses.SelectedRows[0];
                var code = selectedRow.Cells["Code"].Value?.ToString();
                
                var frmWarehouseAddEdit = new FrmWarehouseAddEdit(code);
                if (frmWarehouseAddEdit.ShowDialog() == DialogResult.OK)
                {
                    LoadWarehouses();
                }
            }
            else
            {
                MessageBox.Show("Veuillez sélectionner un entrepôt à modifier", "Attention", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private void BtnDelete_Click(object sender, EventArgs e)
        {
            if (dgvWarehouses.SelectedRows.Count > 0)
            {
                var result = MessageBox.Show("Êtes-vous sûr de vouloir supprimer cet entrepôt ?", "Confirmation", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                if (result == DialogResult.Yes)
                {
                    LoadWarehouses();
                    MessageBox.Show("Entrepôt supprimé avec succès", "Succès", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            else
            {
                MessageBox.Show("Veuillez sélectionner un entrepôt à supprimer", "Attention", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private void BtnRefresh_Click(object sender, EventArgs e)
        {
            LoadWarehouses();
        }

        #endregion

        private void FrmWarehouses_Load(object sender, EventArgs e)
        {
            LoadWarehouses();
        }
    }
}
