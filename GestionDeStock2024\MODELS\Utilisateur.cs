using System;
using System.ComponentModel.DataAnnotations;
using System.Collections.Generic;
using static GestionDeStock2024.MODELS.Enums;

namespace GestionDeStock2024.MODELS
{
    /// <summary>
    /// Modèle pour les utilisateurs du système
    /// </summary>
    public class Utilisateur : BaseEntity, IStatutEntity
    {
        [Required(ErrorMessage = "Le nom d'utilisateur est obligatoire")]
        [StringLength(50, ErrorMessage = "Le nom d'utilisateur ne peut pas dépasser 50 caractères")]
        public string NomUtilisateur { get; set; }

        [Required(ErrorMessage = "Le mot de passe est obligatoire")]
        [StringLength(255)]
        public string MotDePasse { get; set; }

        [Required(ErrorMessage = "Le nom complet est obligatoire")]
        [StringLength(255)]
        public string NomComplet { get; set; }

        [StringLength(100)]
        [EmailAddress(ErrorMessage = "Format d'email invalide")]
        public string Email { get; set; }

        [StringLength(20)]
        [Phone(ErrorMessage = "Format de téléphone invalide")]
        public string Telephone { get; set; }

        [Required(ErrorMessage = "Le rôle est obligatoire")]
        public string Role { get; set; }

        /// <summary>
        /// Permissions détaillées en format JSON
        /// </summary>
        public string Permissions { get; set; }

        public DateTime? DerniereConnexion { get; set; }

        public string Statut { get; set; } = "Actif";

        // Propriétés calculées
        public bool EstActif => Statut == "Actif";

        public bool EstAdmin => Role == RoleUtilisateur.Admin.ToString();

        public TimeSpan? TempsDepuisDerniereConnexion => 
            DerniereConnexion.HasValue ? DateTime.Now - DerniereConnexion.Value : (TimeSpan?)null;
    }

    /// <summary>
    /// Modèle pour les sessions utilisateurs
    /// </summary>
    public class SessionUtilisateur : BaseEntity
    {
        [Required]
        public int UtilisateurId { get; set; }

        public DateTime HeureConnexion { get; set; } = DateTime.Now;

        public DateTime? HeureDeconnexion { get; set; }

        [StringLength(45)]
        public string AdresseIP { get; set; }

        [StringLength(255)]
        public string Navigateur { get; set; }

        public string StatutSession { get; set; } = "Active";

        // Navigation
        public virtual Utilisateur Utilisateur { get; set; }

        // Propriétés calculées
        public TimeSpan? DureeSession => HeureDeconnexion.HasValue ? 
            HeureDeconnexion.Value - HeureConnexion : 
            DateTime.Now - HeureConnexion;

        public bool EstActive => StatutSession == "Active" && !HeureDeconnexion.HasValue;
    }

    /// <summary>
    /// Classe pour les permissions utilisateur
    /// </summary>
    public class PermissionsUtilisateur
    {
        public bool Ventes { get; set; }
        public bool Achats { get; set; }
        public bool Stock { get; set; }
        public bool Clients { get; set; }
        public bool Fournisseurs { get; set; }
        public bool Comptabilite { get; set; }
        public bool Parametres { get; set; }
        public bool Utilisateurs { get; set; }
        public bool Rapports { get; set; }
        public bool Sauvegardes { get; set; }
        public bool Promotions { get; set; }
        public bool Inventaires { get; set; }

        /// <summary>
        /// Vérifie si l'utilisateur a toutes les permissions (Admin)
        /// </summary>
        public bool EstAdminComplet => Ventes && Achats && Stock && Clients && 
                                      Fournisseurs && Comptabilite && Parametres && 
                                      Utilisateurs && Rapports && Sauvegardes;

        /// <summary>
        /// Obtient les permissions par défaut selon le rôle
        /// </summary>
        public static PermissionsUtilisateur ObtenirPermissionsParDefaut(RoleUtilisateur role)
        {
            switch (role)
            {
                case RoleUtilisateur.Admin:
                    return new PermissionsUtilisateur
                    {
                        Ventes = true, Achats = true, Stock = true, Clients = true,
                        Fournisseurs = true, Comptabilite = true, Parametres = true,
                        Utilisateurs = true, Rapports = true, Sauvegardes = true,
                        Promotions = true, Inventaires = true
                    };

                case RoleUtilisateur.Manager:
                    return new PermissionsUtilisateur
                    {
                        Ventes = true, Achats = true, Stock = true, Clients = true,
                        Fournisseurs = true, Comptabilite = true, Parametres = false,
                        Utilisateurs = false, Rapports = true, Sauvegardes = false,
                        Promotions = true, Inventaires = true
                    };

                case RoleUtilisateur.Vendeur:
                    return new PermissionsUtilisateur
                    {
                        Ventes = true, Achats = false, Stock = true, Clients = true,
                        Fournisseurs = false, Comptabilite = false, Parametres = false,
                        Utilisateurs = false, Rapports = false, Sauvegardes = false,
                        Promotions = false, Inventaires = false
                    };

                case RoleUtilisateur.Comptable:
                    return new PermissionsUtilisateur
                    {
                        Ventes = true, Achats = true, Stock = false, Clients = true,
                        Fournisseurs = true, Comptabilite = true, Parametres = false,
                        Utilisateurs = false, Rapports = true, Sauvegardes = false,
                        Promotions = false, Inventaires = false
                    };

                case RoleUtilisateur.Magasinier:
                    return new PermissionsUtilisateur
                    {
                        Ventes = false, Achats = true, Stock = true, Clients = false,
                        Fournisseurs = true, Comptabilite = false, Parametres = false,
                        Utilisateurs = false, Rapports = false, Sauvegardes = false,
                        Promotions = false, Inventaires = true
                    };

                default:
                    return new PermissionsUtilisateur();
            }
        }
    }
}
