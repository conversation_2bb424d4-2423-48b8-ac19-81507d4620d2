using System;

namespace GestionDeStock2024.MODELS
{
    /// <summary>
    /// Types de clients
    /// </summary>
    public enum TypeClient
    {
        Particulier,
        Entreprise,
        Revendeur
    }

    /// <summary>
    /// Modes de paiement
    /// </summary>
    public enum ModePaiement
    {
        Espèces,
        Espèces_sans_timbre,
        Chèque,
        Virement,
        Carte_bancaire,
        Crédit
    }

    /// <summary>
    /// Statuts de paiement
    /// </summary>
    public enum StatutPaiement
    {
        Non_payé,
        Payé,
        Partiellement_payé
    }

    /// <summary>
    /// Types de mouvements de stock
    /// </summary>
    public enum TypeMouvement
    {
        Entrée,
        Sortie,
        Ajustement,
        Transfert,
        Inventaire
    }

    /// <summary>
    /// Types de promotions
    /// </summary>
    public enum TypePromotion
    {
        Pourcentage,
        Montant_fixe,
        Achetez_X_Obtenez_Y,
        Pack
    }

    /// <summary>
    /// Statuts génériques
    /// </summary>
    public enum Statut
    {
        Actif,
        Inactif,
        Suspendu,
        Archiv<PERSON>
    }

    /// <summary>
    /// Types de documents
    /// </summary>
    public enum TypeDocument
    {
        <PERSON><PERSON><PERSON>,
        <PERSON><PERSON>,
        <PERSON>_<PERSON>_commande,
        <PERSON>_de_livraison,
        <PERSON><PERSON>ir,
        Reçu
    }

    /// <summary>
    /// Rôles utilisateurs
    /// </summary>
    public enum RoleUtilisateur
    {
        Administrateur,
        Gestionnaire,
        Vendeur,
        Caissier,
        Magasinier,
        Comptable
    }

    /// <summary>
    /// Types de TVA
    /// </summary>
    public enum TypeTVA
    {
        Normal,
        Réduit,
        Exonéré
    }

    /// <summary>
    /// Types de comptes bancaires
    /// </summary>
    public enum TypeCompteBancaire
    {
        Courant,
        Épargne,
        Professionnel
    }

    /// <summary>
    /// Types de caisses
    /// </summary>
    public enum TypeCaisse
    {
        Principale,
        Secondaire,
        Mobile
    }

    /// <summary>
    /// États des factures
    /// </summary>
    public enum EtatFacture
    {
        Brouillon,
        Validée,
        Annulée,
        Archivée
    }

    /// <summary>
    /// Types d'inventaire
    /// </summary>
    public enum TypeInventaire
    {
        Complet,
        Partiel,
        Tournant,
        Exceptionnel
    }

    /// <summary>
    /// Priorités
    /// </summary>
    public enum Priorite
    {
        Basse,
        Normale,
        Haute,
        Critique
    }

    /// <summary>
    /// Types d'alertes
    /// </summary>
    public enum TypeAlerte
    {
        Stock_faible,
        Rupture_stock,
        Expiration_proche,
        Crédit_dépassé,
        Paiement_retard
    }

    /// <summary>
    /// Fréquences
    /// </summary>
    public enum Frequence
    {
        Quotidienne,
        Hebdomadaire,
        Mensuelle,
        Trimestrielle,
        Annuelle
    }

    /// <summary>
    /// Types de rapports
    /// </summary>
    public enum TypeRapport
    {
        Ventes,
        Achats,
        Stock,
        Financier,
        Client,
        Fournisseur
    }

    /// <summary>
    /// Formats d'export
    /// </summary>
    public enum FormatExport
    {
        PDF,
        Excel,
        CSV,
        Word
    }

    /// <summary>
    /// Types d'unités de mesure
    /// </summary>
    public enum TypeUniteMesure
    {
        Pièce,
        Kilogramme,
        Gramme,
        Litre,
        Mètre,
        Centimètre,
        Carton,
        Pack,
        Palette
    }

    /// <summary>
    /// Types de codes-barres
    /// </summary>
    public enum TypeCodeBarre
    {
        EAN13,
        EAN8,
        UPC,
        Code128,
        Code39,
        QRCode
    }

    /// <summary>
    /// Types de remises
    /// </summary>
    public enum TypeRemise
    {
        Pourcentage,
        Montant_fixe,
        Gratuit
    }

    /// <summary>
    /// Méthodes de calcul des prix
    /// </summary>
    public enum MethodeCalculPrix
    {
        Marge_sur_achat,
        Prix_de_vente_direct,
        Coefficient_multiplicateur
    }

    /// <summary>
    /// Types de sauvegarde
    /// </summary>
    public enum TypeSauvegarde
    {
        Complète,
        Incrémentale,
        Différentielle
    }

    /// <summary>
    /// États de synchronisation
    /// </summary>
    public enum EtatSynchronisation
    {
        En_attente,
        En_cours,
        Terminée,
        Échouée
    }
}
